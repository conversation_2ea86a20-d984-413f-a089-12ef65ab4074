import 'package:flutter/material.dart';
import 'package:rideoon/services/config_service.dart';
import 'package:rideoon/services/map_service.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';

/// Debug widget to display environment configuration
/// Only shows in development mode
class EnvDebugWidget extends StatelessWidget {
  const EnvDebugWidget({super.key});

  @override
  Widget build(BuildContext context) {
    // Only show in development mode
    if (!ConfigService.isDevelopment) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primaryLightest,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(
                Icons.bug_report,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Environment Debug Info',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                  fontFamily: 'Poppins',
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildInfoRow('Environment', ConfigService.appEnvironment),
          _buildInfoRow('App Name', ConfigService.appName),
          _buildInfoRow('App Version', ConfigService.appVersion),
          _buildInfoRow('API Base URL', ConfigService.apiBaseUrl),
          _buildInfoRow('Debug Mode', ConfigService.enableDebugMode.toString()),
          _buildInfoRow('Analytics', ConfigService.enableAnalytics.toString()),
          _buildInfoRow('Maps Configured', MapService.isConfigured.toString()),
          if (MapService.isConfigured)
            _buildInfoRow('Maps API Key', '${ConfigService.googleMapsApiKey.substring(0, 10)}...'),
          const SizedBox(height: 8),
          ElevatedButton(
            onPressed: () => _showFullConfig(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            child: const Text('Show Full Config'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: AppColors.black.withValues(alpha: 0.7),
                fontFamily: 'Poppins',
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12,
                color: AppColors.black.withValues(alpha: 0.9),
                fontFamily: 'Poppins',
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showFullConfig(BuildContext context) {
    try {
      final allConfig = ConfigService.getAllValues();
      final mapConfig = MapService.getConfigInfo();
      
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Full Configuration'),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Environment Variables:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...allConfig.entries.map((entry) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Text(
                      '${entry.key}: ${_maskSensitiveValue(entry.key, entry.value)}',
                      style: const TextStyle(fontSize: 12, fontFamily: 'monospace'),
                    ),
                  )),
                  const SizedBox(height: 16),
                  Text(
                    'Map Service Info:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...mapConfig.entries.map((entry) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Text(
                      '${entry.key}: ${entry.value}',
                      style: const TextStyle(fontSize: 12, fontFamily: 'monospace'),
                    ),
                  )),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading config: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  String _maskSensitiveValue(String key, String value) {
    final sensitiveKeys = ['API_KEY', 'SECRET', 'PASSWORD', 'TOKEN'];
    
    if (sensitiveKeys.any((sensitive) => key.toUpperCase().contains(sensitive))) {
      if (value.length <= 10) return '***';
      return '${value.substring(0, 6)}...${value.substring(value.length - 4)}';
    }
    
    return value;
  }
}
