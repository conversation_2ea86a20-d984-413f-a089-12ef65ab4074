import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/views/dashboards/user_dashboard/messages_view/chat_view.dart';

/// Customer Support view with help options and chat access
class CustomerSupportView extends StatefulWidget {
  const CustomerSupportView({super.key});

  @override
  State<CustomerSupportView> createState() => _CustomerSupportViewState();
}

class _CustomerSupportViewState extends State<CustomerSupportView> {
  // Social media links
  final List<Map<String, dynamic>> _socialLinks = [
    {
      'name': 'Facebook',
      'icon': Icons.facebook,
      'url': 'https://facebook.com/rideon',
      'color': const Color(0xFF1877F2),
    },
    {
      'name': 'Twitter',
      'icon': Icons.alternate_email,
      'url': 'https://twitter.com/rideon',
      'color': const Color(0xFF1DA1F2),
    },
    {
      'name': 'Instagram',
      'icon': Icons.camera_alt,
      'url': 'https://instagram.com/rideon',
      'color': const Color(0xFFE4405F),
    },
    {
      'name': 'LinkedIn',
      'icon': Icons.business,
      'url': 'https://linkedin.com/company/rideon',
      'color': const Color(0xFF0A66C2),
    },
    {
      'name': 'WhatsApp',
      'icon': Icons.phone,
      'url': 'https://wa.me/1234567890',
      'color': const Color(0xFF25D366),
    },
    {
      'name': 'Email',
      'icon': Icons.email,
      'url': 'mailto:<EMAIL>',
      'color': const Color(0xFF34495E),
    },
  ];

  // FAQ items for users
  final List<Map<String, dynamic>> _faqItems = [
    {
      'question': 'How do I place a delivery order?',
      'answer': 'You can place a delivery order from the Home tab. Select your pickup and delivery locations, add package details, and confirm your order.',
    },
    {
      'question': 'How are delivery fees calculated?',
      'answer': 'Delivery fees are calculated based on distance, package size, delivery urgency, and current demand. You\'ll see the exact fee before confirming your order.',
    },
    {
      'question': 'Can I track my package in real-time?',
      'answer': 'Yes! Once your order is accepted by a rider, you can track your package in real-time from the Track tab.',
    },
    {
      'question': 'What if my package is damaged or lost?',
      'answer': 'We have insurance coverage for all deliveries. Contact our support team immediately if you notice any damage or if your package is missing.',
    },
    {
      'question': 'How do I cancel an order?',
      'answer': 'You can cancel an order before it\'s picked up by going to your order history and selecting cancel. Cancellation fees may apply depending on timing.',
    },
    {
      'question': 'What payment methods do you accept?',
      'answer': 'We accept credit/debit cards, mobile money, bank transfers, and cash on delivery for select orders.',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5FF),
      appBar: _buildAppBar(context),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(_getHorizontalPadding(context)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: _getSpacing(context, 20)),

              // Customer support section
              _buildCustomerSupportSection(context),

              SizedBox(height: _getSpacing(context, 32)),

              // FAQ section
              _buildFAQSection(context),

              SizedBox(height: _getSpacing(context, 32)),

              // Social media links
              _buildSocialMediaSection(context),

              SizedBox(height: _getSpacing(context, 32)),

              // App information
              _buildAppInformation(context),

              SizedBox(height: _getSpacing(context, 20)),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: const Color(0xFFF5F5FF),
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: AppColors.black,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        'Customer Support',
        style: TextStyle(
          fontSize: _getFontSize(context, 20),
          fontWeight: FontWeight.bold,
          fontFamily: 'Poppins',
          color: AppColors.black,
        ),
      ),
    );
  }

  Widget _buildCustomerSupportSection(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 24)),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 20)),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: _getSpacing(context, 20),
            offset: Offset(0, _getSpacing(context, 8)),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.support_agent,
            size: _getIconSize(context, 48),
            color: AppColors.white,
          ),
          SizedBox(height: _getSpacing(context, 16)),
          Text(
            'Need Help?',
            style: TextStyle(
              fontSize: _getFontSize(context, 24),
              fontWeight: FontWeight.bold,
              fontFamily: 'Poppins',
              color: AppColors.white,
            ),
          ),
          SizedBox(height: _getSpacing(context, 8)),
          Text(
            'Our support team is here to help you 24/7. Chat with us for quick assistance with your deliveries.',
            style: TextStyle(
              fontSize: _getFontSize(context, 14),
              fontFamily: 'Poppins',
              color: AppColors.white.withValues(alpha: 0.9),
            ),
          ),
          SizedBox(height: _getSpacing(context, 20)),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                _openCustomerSupport(context);
              },
              icon: Icon(
                Icons.chat,
                size: _getIconSize(context, 20),
                color: AppColors.primary,
              ),
              label: Text(
                'Chat with Support',
                style: TextStyle(
                  fontSize: _getFontSize(context, 16),
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Poppins',
                  color: AppColors.primary,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.white,
                foregroundColor: AppColors.primary,
                padding: EdgeInsets.symmetric(
                  vertical: _getSpacing(context, 16),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
                ),
                elevation: 0,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFAQSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Frequently Asked Questions',
          style: TextStyle(
            fontSize: _getFontSize(context, 18),
            fontWeight: FontWeight.bold,
            fontFamily: 'Poppins',
            color: AppColors.black,
          ),
        ),
        SizedBox(height: _getSpacing(context, 16)),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _faqItems.length,
          itemBuilder: (context, index) {
            final faq = _faqItems[index];
            return Padding(
              padding: EdgeInsets.only(bottom: _getSpacing(context, 12)),
              child: _buildFAQItem(context, faq),
            );
          },
        ),
      ],
    );
  }

  Widget _buildFAQItem(BuildContext context, Map<String, dynamic> faq) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
        border: Border.all(
          color: AppColors.black.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: ExpansionTile(
        title: Text(
          faq['question'] as String,
          style: TextStyle(
            fontSize: _getFontSize(context, 14),
            fontWeight: FontWeight.w500,
            fontFamily: 'Poppins',
            color: AppColors.black,
          ),
        ),
        children: [
          Padding(
            padding: EdgeInsets.fromLTRB(
              _getSpacing(context, 16),
              0,
              _getSpacing(context, 16),
              _getSpacing(context, 16),
            ),
            child: Text(
              faq['answer'] as String,
              style: TextStyle(
                fontSize: _getFontSize(context, 13),
                fontFamily: 'Poppins',
                color: AppColors.black.withValues(alpha: 0.7),
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSocialMediaSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Connect with Us',
          style: TextStyle(
            fontSize: _getFontSize(context, 18),
            fontWeight: FontWeight.bold,
            fontFamily: 'Poppins',
            color: AppColors.black,
          ),
        ),
        SizedBox(height: _getSpacing(context, 16)),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(_getSpacing(context, 20)),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(_getBorderRadius(context, 16)),
            border: Border.all(
              color: AppColors.black.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              Text(
                'Follow us on social media for updates and news',
                style: TextStyle(
                  fontSize: _getFontSize(context, 14),
                  fontFamily: 'Poppins',
                  color: AppColors.black.withValues(alpha: 0.6),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: _getSpacing(context, 20)),
              Wrap(
                spacing: _getSpacing(context, 16),
                runSpacing: _getSpacing(context, 16),
                children: _socialLinks.map((social) => _buildSocialButton(context, social)).toList(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSocialButton(BuildContext context, Map<String, dynamic> social) {
    return GestureDetector(
      onTap: () {
        Toast.info('Opening ${social['name']}...');
      },
      child: Container(
        width: _getIconSize(context, 50),
        height: _getIconSize(context, 50),
        decoration: BoxDecoration(
          color: (social['color'] as Color).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
          border: Border.all(
            color: (social['color'] as Color).withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Icon(
          social['icon'] as IconData,
          size: _getIconSize(context, 24),
          color: social['color'] as Color,
        ),
      ),
    );
  }

  Widget _buildAppInformation(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'App Information',
          style: TextStyle(
            fontSize: _getFontSize(context, 18),
            fontWeight: FontWeight.bold,
            fontFamily: 'Poppins',
            color: AppColors.black,
          ),
        ),
        SizedBox(height: _getSpacing(context, 16)),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(_getSpacing(context, 20)),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(_getBorderRadius(context, 16)),
            border: Border.all(
              color: AppColors.black.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              _buildInfoRow(context, 'App Version', '1.0.0'),
              SizedBox(height: _getSpacing(context, 12)),
              _buildInfoRow(context, 'Build Number', '100'),
              SizedBox(height: _getSpacing(context, 12)),
              _buildInfoRow(context, 'Last Updated', 'June 10, 2025'),
              SizedBox(height: _getSpacing(context, 12)),
              _buildInfoRow(context, 'Support Email', '<EMAIL>'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: _getFontSize(context, 14),
            fontFamily: 'Poppins',
            color: AppColors.black.withValues(alpha: 0.6),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: _getFontSize(context, 14),
            fontWeight: FontWeight.w500,
            fontFamily: 'Poppins',
            color: AppColors.black,
          ),
        ),
      ],
    );
  }

  void _openCustomerSupport(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ChatView(
          conversationId: 'user_support_chat',
          conversationName: 'Customer Support',
          conversationType: 'support',
        ),
      ),
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16;
    } else if (screenWidth > 600) {
      basePadding = 40;
    } else {
      basePadding = 24;
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8;
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2;
    } else {
      fontSize = baseFontSize;
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8;
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2;
    } else {
      iconSize = baseIconSize;
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6;
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2;
    } else {
      borderRadius = baseBorderRadius;
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }
}
