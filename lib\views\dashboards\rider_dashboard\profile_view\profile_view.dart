import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/views/dashboards/rider_dashboard/profile_view/personal_information_view.dart';
import 'package:rideoon/views/dashboards/rider_dashboard/profile_view/payment_earnings_view.dart';
import 'package:rideoon/views/dashboards/rider_dashboard/profile_view/notifications_view.dart';
import 'package:rideoon/views/dashboards/rider_dashboard/profile_view/help_support_view.dart';

/// Profile view page for rider dashboard
///
/// This page will contain rider profile management features,
/// including personal information, vehicle details, and account settings.
class ProfileView extends StatefulWidget {
  const ProfileView({super.key});

  @override
  State<ProfileView> createState() => _ProfileViewState();
}

class _ProfileViewState extends State<ProfileView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5FF),
      body: <PERSON><PERSON><PERSON>(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 7% gap from top of screen
              SizedBox(height: MediaQuery.of(context).size.height * 0.07),

              // Header
              _buildHeader(context),

              SizedBox(height: _getSpacing(context, 16)),

              // Subtitle
              _buildSubtitle(context),

              SizedBox(height: _getSpacing(context, 32)),

              // Profile banner with stats
              _buildProfileHeader(context),

              SizedBox(height: _getSpacing(context, 32)),

              // Profile options
              _buildProfileOptions(context),

              SizedBox(height: _getSpacing(context, 100)), // Extra space for bottom nav
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Center(
      child: Text(
        'Profile',
        style: TextStyle(
          color: const Color(0xFF414141),
          fontSize: _getFontSize(context, 20),
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildSubtitle(BuildContext context) {
    return Center(
      child: Text(
        'Manage your profile and account settings',
        style: TextStyle(
          color: const Color(0xFF9A9A9A),
          fontSize: _getFontSize(context, 10),
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w500,
          letterSpacing: 0.20,
        ),
      ),
    );
  }

  Widget _buildProfileHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Container(
        padding: EdgeInsets.all(_getSpacing(context, 24)),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.primary,
              AppColors.primary.withValues(alpha: 0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 16)),
        ),
      child: Column(
        children: [
          // Avatar and basic info
          Row(
            children: [
              Container(
                width: _getAvatarSize(context),
                height: _getAvatarSize(context),
                decoration: BoxDecoration(
                  color: AppColors.white.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppColors.white,
                    width: 3,
                  ),
                ),
                child: Icon(
                  Icons.person,
                  size: _getAvatarSize(context) * 0.6,
                  color: AppColors.white,
                ),
              ),
              SizedBox(width: _getSpacing(context, 16)),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'John Rider',
                      style: TextStyle(
                        fontSize: _getNameFontSize(context),
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Poppins',
                        color: AppColors.white,
                      ),
                    ),
                    SizedBox(height: _getSpacing(context, 4)),
                    Text(
                      'Rider ID: RD001234',
                      style: TextStyle(
                        fontSize: _getSubtitleFontSize(context),
                        fontFamily: 'Poppins',
                        color: AppColors.white.withValues(alpha: 0.9),
                      ),
                    ),
                    SizedBox(height: _getSpacing(context, 8)),
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          size: _getSmallIconSize(context),
                          color: AppColors.rating,
                        ),
                        SizedBox(width: _getSpacing(context, 4)),
                        Text(
                          '4.8 Rating',
                          style: TextStyle(
                            fontSize: _getSubtitleFontSize(context),
                            fontFamily: 'Poppins',
                            color: AppColors.white.withValues(alpha: 0.9),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: _getSpacing(context, 12),
                  vertical: _getSpacing(context, 6),
                ),
                decoration: BoxDecoration(
                  color: AppColors.success,
                  borderRadius: BorderRadius.circular(_getSpacing(context, 16)),
                ),
                child: Text(
                  'Active',
                  style: TextStyle(
                    fontSize: _getStatusFontSize(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Poppins',
                    color: AppColors.white,
                  ),
                ),
              ),
            ],
          ),
          
          SizedBox(height: _getSpacing(context, 20)),
          
          // Contact info
          Row(
            children: [
              Expanded(
                child: _buildContactInfo(
                  context,
                  icon: Icons.email_outlined,
                  label: 'Email',
                  value: '<EMAIL>',
                ),
              ),
              SizedBox(width: _getSpacing(context, 16)),
              Expanded(
                child: _buildContactInfo(
                  context,
                  icon: Icons.phone_outlined,
                  label: 'Phone',
                  value: '****** 567 8900',
                ),
              ),
            ],
          ),

          SizedBox(height: _getSpacing(context, 24)),

          // Performance stats
          Container(
            padding: EdgeInsets.all(_getSpacing(context, 16)),
            decoration: BoxDecoration(
              color: AppColors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
              border: Border.all(
                color: AppColors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Text(
                  'Performance Overview',
                  style: TextStyle(
                    fontSize: _getFontSize(context, 16),
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Poppins',
                    color: AppColors.white,
                  ),
                ),
                SizedBox(height: _getSpacing(context, 16)),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatItem(
                        context,
                        icon: Icons.local_shipping,
                        title: 'Deliveries',
                        value: '1,234',
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        context,
                        icon: Icons.attach_money,
                        title: 'Earnings',
                        value: '₦45,250',
                      ),
                    ),
                  ],
                ),
                SizedBox(height: _getSpacing(context, 12)),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatItem(
                        context,
                        icon: Icons.schedule,
                        title: 'Hours',
                        value: '456h',
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        context,
                        icon: Icons.thumb_up,
                        title: 'Success',
                        value: '98.5%',
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    ),
    );
  }

  Widget _buildContactInfo(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: _getSmallIconSize(context),
              color: AppColors.white.withValues(alpha: 0.8),
            ),
            SizedBox(width: _getSpacing(context, 6)),
            Text(
              label,
              style: TextStyle(
                fontSize: _getContactLabelFontSize(context),
                fontFamily: 'Poppins',
                color: AppColors.white.withValues(alpha: 0.8),
              ),
            ),
          ],
        ),
        SizedBox(height: _getSpacing(context, 4)),
        Text(
          value,
          style: TextStyle(
            fontSize: _getContactValueFontSize(context),
            fontWeight: FontWeight.w500,
            fontFamily: 'Poppins',
            color: AppColors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          size: _getIconSize(context, 24),
          color: AppColors.white,
        ),
        SizedBox(height: _getSpacing(context, 8)),
        Text(
          value,
          style: TextStyle(
            fontSize: _getFontSize(context, 18),
            fontWeight: FontWeight.bold,
            fontFamily: 'Poppins',
            color: AppColors.white,
          ),
        ),
        SizedBox(height: _getSpacing(context, 4)),
        Text(
          title,
          style: TextStyle(
            fontSize: _getFontSize(context, 12),
            fontFamily: 'Poppins',
            color: AppColors.white.withValues(alpha: 0.8),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildStatisticsSection(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Performance Overview',
            style: TextStyle(
              fontSize: _getFontSize(context, 18),
              fontWeight: FontWeight.bold,
              fontFamily: 'Poppins',
              color: AppColors.black,
            ),
          ),
        SizedBox(height: _getSpacing(context, 16)),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                icon: Icons.local_shipping,
                title: 'Total Deliveries',
                value: '1,234',
                color: AppColors.success,
              ),
            ),
            SizedBox(width: _getSpacing(context, 12)),
            Expanded(
              child: _buildStatCard(
                context,
                icon: Icons.attach_money,
                title: 'Total Earnings',
                value: '₦12,450',
                color: AppColors.warning,
              ),
            ),
          ],
        ),
        SizedBox(height: _getSpacing(context, 12)),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                icon: Icons.schedule,
                title: 'Hours Worked',
                value: '456h',
                color: AppColors.primary,
              ),
            ),
            SizedBox(width: _getSpacing(context, 12)),
            Expanded(
              child: _buildStatCard(
                context,
                icon: Icons.thumb_up,
                title: 'Success Rate',
                value: '98.5%',
                color: AppColors.accept,
              ),
            ),
          ],
        ),
      ],
    ),
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(_getSpacing(context, 16)),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(
                icon,
                size: _getIconSize(context, 20),
                color: color,
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: _getValueFontSize(context),
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Poppins',
                  color: AppColors.black,
                ),
              ),
            ],
          ),
          SizedBox(height: _getSpacing(context, 8)),
          Text(
            title,
            style: TextStyle(
              fontSize: _getCardTitleFontSize(context),
              fontFamily: 'Poppins',
              color: AppColors.black.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileOptions(BuildContext context) {
    final options = [
      {
        'icon': Icons.person_outline,
        'title': 'Personal Information',
        'subtitle': 'Update your personal details',
        'route': 'personal_info',
      },
      {
        'icon': Icons.account_balance_wallet_outlined,
        'title': 'Payment & Earnings',
        'subtitle': 'View earnings and payment methods',
        'route': 'payment_earnings',
      },
      {
        'icon': Icons.notifications_outlined,
        'title': 'Notifications',
        'subtitle': 'Configure notification preferences',
        'route': 'notifications',
      },
      {
        'icon': Icons.help_outline,
        'title': 'Help & Support',
        'subtitle': 'Get help and contact support',
        'route': 'help_support',
      },
    ];

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Account Settings',
            style: TextStyle(
              fontSize: _getFontSize(context, 18),
              fontWeight: FontWeight.bold,
              fontFamily: 'Poppins',
              color: AppColors.black,
            ),
          ),
        SizedBox(height: _getSpacing(context, 16)),
        ...options.map((option) => _buildOptionItem(
          context,
          icon: option['icon'] as IconData,
          title: option['title'] as String,
          subtitle: option['subtitle'] as String,
          route: option['route'] as String,
        )),
      ],
    ),
    );
  }

  Widget _buildOptionItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required String route,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: _getSpacing(context, 8)),
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(
          horizontal: _getSpacing(context, 16),
          vertical: _getSpacing(context, 4),
        ),
        leading: Container(
          width: _getIconContainerSize(context),
          height: _getIconContainerSize(context),
          decoration: BoxDecoration(
            color: AppColors.black.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(_getIconContainerSize(context) / 2),
          ),
          child: Icon(
            icon,
            size: _getIconSize(context, 20),
            color: AppColors.black.withValues(alpha: 0.7),
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: _getTitleFontSize(context),
            fontWeight: FontWeight.w500,
            fontFamily: 'Poppins',
            color: AppColors.black,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: _getSubtitleFontSize(context),
            fontFamily: 'Poppins',
            color: AppColors.black.withValues(alpha: 0.6),
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: _getArrowIconSize(context),
          color: AppColors.black.withValues(alpha: 0.4),
        ),
        onTap: () {
          _navigateToPage(context, route, title);
        },
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
        ),
      ),
    );
  }

  void _navigateToPage(BuildContext context, String route, String title) {
    switch (route) {
      case 'personal_info':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const PersonalInformationView(),
          ),
        );
        break;
      case 'payment_earnings':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const PaymentEarningsView(),
          ),
        );
        break;
      case 'notifications':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const NotificationsView(),
          ),
        );
        break;
      case 'help_support':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const HelpSupportView(),
          ),
        );
        break;
      default:
        Toast.info('$title functionality to be implemented');
    }
  }

  // Responsive helper methods (matching history view pattern)
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 40; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet
    } else {
      spacing = baseSpacing; // Mobile
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2; // Tablet
    } else {
      fontSize = baseFontSize; // Mobile
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2; // Tablet
    } else {
      iconSize = baseIconSize; // Mobile
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2; // Tablet
    } else {
      borderRadius = baseBorderRadius; // Mobile
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }

  // Legacy helper methods for existing components
  double _getAvatarSize(BuildContext context) {
    return _getIconSize(context, 70);
  }

  double _getIconContainerSize(BuildContext context) {
    return _getIconSize(context, 40);
  }

  double _getArrowIconSize(BuildContext context) {
    return _getIconSize(context, 16);
  }

  double _getTitleFontSize(BuildContext context) {
    return _getFontSize(context, 16);
  }

  double _getSubtitleFontSize(BuildContext context) {
    return _getFontSize(context, 12);
  }

  double _getNameFontSize(BuildContext context) {
    return _getFontSize(context, 20);
  }

  double _getStatusFontSize(BuildContext context) {
    return _getFontSize(context, 12);
  }

  double _getContactLabelFontSize(BuildContext context) {
    return _getFontSize(context, 11);
  }

  double _getContactValueFontSize(BuildContext context) {
    return _getFontSize(context, 12);
  }

  double _getValueFontSize(BuildContext context) {
    return _getFontSize(context, 18);
  }

  double _getCardTitleFontSize(BuildContext context) {
    return _getFontSize(context, 12);
  }

  double _getSmallIconSize(BuildContext context) {
    return _getIconSize(context, 16);
  }

  double _getProfilePictureSize(BuildContext context) {
    return _getIconSize(context, 80);
  }

  double _getProfileIconSize(BuildContext context) {
    return _getIconSize(context, 40);
  }

  double _getRatingFontSize(BuildContext context) {
    return _getFontSize(context, 14);
  }

  double _getStarIconSize(BuildContext context) {
    return _getIconSize(context, 16);
  }
}
