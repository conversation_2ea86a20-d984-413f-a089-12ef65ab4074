import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/providers/toast_provider.dart';

/// History view page for rider dashboard
///
/// This page displays the delivery history for riders,
/// including completed deliveries, payment history, and tracking details.
class HistoryView extends StatefulWidget {
  const HistoryView({super.key});

  @override
  State<HistoryView> createState() => _HistoryViewState();
}

class _HistoryViewState extends State<HistoryView> {
  // Sample data - replace with actual data from your state management
  final List<Map<String, dynamic>> _deliveryHistory = [
    {
      'id': '1',
      'pickupStatus': 'Rider picked Up package',
      'deliveryStatus': 'Package Delivered to Destination',
      'date': '18th of december, 2024 10:24:02PM',
      'address': '14 LAGOS ISLAND. 18, SIMPSON STREET, BESIDE TOTAL FILLING STATION SURA, LAGOS',
      'amount': '2000',
      'canDispute': true,
    },
    {
      'id': '2',
      'pickupStatus': 'Rider picked Up package',
      'deliveryStatus': 'Package Delivered to Destination',
      'date': '18th of december, 2024 10:24:02PM',
      'address': '14 LAGOS ISLAND. 18, SIMPSON STREET, BESIDE TOTAL FILLING STATION SURA, LAGOS',
      'amount': '2000',
      'canDispute': true,
    },
    {
      'id': '3',
      'pickupStatus': 'Rider picked Up package',
      'deliveryStatus': 'Package Delivered to Destination',
      'date': '18th of december, 2024 10:24:02PM',
      'address': '14 LAGOS ISLAND. 18, SIMPSON STREET, BESIDE TOTAL FILLING STATION SURA, LAGOS',
      'amount': '2000',
      'canDispute': true,
    },
    {
      'id': '4',
      'pickupStatus': 'Rider picked Up package',
      'deliveryStatus': 'Package Delivered to Destination',
      'date': '18th of december, 2024 10:24:02PM',
      'address': '14 LAGOS ISLAND. 18, SIMPSON STREET, BESIDE TOTAL FILLING STATION SURA, LAGOS',
      'amount': '2000',
      'canDispute': true,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5FF),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 7% gap from top of screen
              SizedBox(height: MediaQuery.of(context).size.height * 0.07),

              // Header
              _buildHeader(context),

              SizedBox(height: _getSpacing(context, 16)),

              // Subtitle
              _buildSubtitle(context),

              SizedBox(height: _getSpacing(context, 32)),

              // History list
              _buildHistoryList(context),

              SizedBox(height: _getSpacing(context, 100)), // Extra space for bottom nav
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Center(
      child: Text(
        'History',
        style: TextStyle(
          color: const Color(0xFF414141),
          fontSize: _getFontSize(context, 20),
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildSubtitle(BuildContext context) {
    return Center(
      child: Text(
        'Showing all ride history',
        style: TextStyle(
          color: const Color(0xFF9A9A9A),
          fontSize: _getFontSize(context, 10),
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w500,
          letterSpacing: 0.20,
        ),
      ),
    );
  }

  Widget _buildHistoryList(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Column(
        children: _deliveryHistory.map((delivery) =>
          Padding(
            padding: EdgeInsets.only(bottom: _getSpacing(context, 12)),
            child: _buildHistoryCard(context, delivery),
          ),
        ).toList(),
      ),
    );
  }

  Widget _buildHistoryCard(BuildContext context, Map<String, dynamic> delivery) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 20)),
      decoration: ShapeDecoration(
        color: const Color(0xFFFEFEFE),
        shape: RoundedRectangleBorder(
          side: BorderSide(
            width: 1,
            color: AppColors.black.withValues(alpha: 0.07),
          ),
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // First tracking step with payment beside it
          _buildTrackingStepWithPayment(
            context,
            title: delivery['pickupStatus'],
            time: delivery['date'],
            amount: delivery['amount'],
            isCompleted: true,
            isLast: false,
          ),
          SizedBox(height: _getSpacing(context, 20)),
          // Second tracking step with address
          _buildTrackingStep(
            context,
            title: delivery['deliveryStatus'],
            time: null,
            address: delivery['address'],
            isCompleted: false,
            isLast: true,
          ),
          SizedBox(height: _getSpacing(context, 16)),
          // Dispute section
          if (delivery['canDispute'])
            Align(
              alignment: Alignment.centerRight,
              child: GestureDetector(
                onTap: () {
                  // TODO: Implement dispute functionality
                  Toast.info('Dispute functionality to be implemented');
                },
                child: Text(
                  'Initiate Dispute',
                  style: TextStyle(
                    color: const Color(0xFF385AE5),
                    fontSize: _getFontSize(context, 12),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                    letterSpacing: -0.60,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTrackingStepWithPayment(
    BuildContext context, {
    required String title,
    String? time,
    required String amount,
    required bool isCompleted,
    required bool isLast,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: _getIconSize(context, 19),
              height: _getIconSize(context, 19),
              decoration: BoxDecoration(
                color: const Color(0x23A9A9A9),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Container(
                  width: _getIconSize(context, 9),
                  height: _getIconSize(context, 9),
                  decoration: BoxDecoration(
                    color: isCompleted ? const Color(0xFF9713E7) : const Color(0xFFA5A5A5),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: _getSpacing(context, 40),
                color: AppColors.black.withValues(alpha: 0.1),
              ),
          ],
        ),
        SizedBox(width: _getSpacing(context, 16)),
        // Content
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left side - tracking info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        color: isCompleted
                            ? AppColors.black.withValues(alpha: 0.4)
                            : const Color(0xFFA5A5A5),
                        fontSize: _getFontSize(context, 10),
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                        height: 0.95,
                      ),
                    ),
                    if (time != null) ...[
                      SizedBox(height: _getSpacing(context, 4)),
                      Text(
                        time,
                        style: TextStyle(
                          color: const Color(0xFF111111),
                          fontSize: _getFontSize(context, 10),
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w500,
                          height: 0.95,
                          letterSpacing: -0.10,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              // Right side - payment info
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: _getSpacing(context, 8),
                  vertical: _getSpacing(context, 4),
                ),
                decoration: ShapeDecoration(
                  color: const Color(0x1C009951),
                  shape: RoundedRectangleBorder(
                    side: BorderSide(
                      width: 1,
                      color: AppColors.black.withValues(alpha: 0.03),
                    ),
                    borderRadius: BorderRadius.circular(_getBorderRadius(context, 10)),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.account_balance_wallet,
                      size: _getIconSize(context, 10),
                      color: const Color(0xFF14AE5C),
                    ),
                    SizedBox(width: _getSpacing(context, 4)),
                    Text(
                      '₦$amount',
                      style: TextStyle(
                        color: const Color(0xFF14AE5C),
                        fontSize: _getFontSize(context, 10),
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                        height: 1.60,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTrackingStep(
    BuildContext context, {
    required String title,
    String? time,
    String? address,
    required bool isCompleted,
    required bool isLast,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: _getIconSize(context, 19),
              height: _getIconSize(context, 19),
              decoration: BoxDecoration(
                color: const Color(0x23A9A9A9),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Container(
                  width: _getIconSize(context, 9),
                  height: _getIconSize(context, 9),
                  decoration: BoxDecoration(
                    color: isCompleted ? const Color(0xFF9713E7) : const Color(0xFFA5A5A5),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: _getSpacing(context, 40),
                color: AppColors.black.withValues(alpha: 0.1),
              ),
          ],
        ),
        SizedBox(width: _getSpacing(context, 16)),
        // Content
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: isCompleted
                      ? AppColors.black.withValues(alpha: 0.4)
                      : const Color(0xFFA5A5A5),
                  fontSize: _getFontSize(context, 10),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                  height: 0.95,
                ),
              ),
              if (time != null) ...[
                SizedBox(height: _getSpacing(context, 4)),
                Text(
                  time,
                  style: TextStyle(
                    color: const Color(0xFF111111),
                    fontSize: _getFontSize(context, 10),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                    height: 0.95,
                    letterSpacing: -0.10,
                  ),
                ),
              ],
              if (address != null) ...[
                SizedBox(height: _getSpacing(context, 8)),
                Text(
                  address,
                  style: TextStyle(
                    color: AppColors.black,
                    fontSize: _getFontSize(context, 10),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                    height: 0.95,
                    letterSpacing: 0.20,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 40; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet
    } else {
      spacing = baseSpacing; // Mobile
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2; // Tablet
    } else {
      fontSize = baseFontSize; // Mobile
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2; // Tablet
    } else {
      iconSize = baseIconSize; // Mobile
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2; // Tablet
    } else {
      borderRadius = baseBorderRadius; // Mobile
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }
}