import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/push_notfication.dart';

/// Example usage of the PushNotificationDialog widget
/// 
/// This file demonstrates different ways to use the reusable
/// push notification dialog throughout the app.
class PushNotificationExample extends StatelessWidget {
  const PushNotificationExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Push Notification Examples'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Push Notification Dialog Examples',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.black,
              ),
            ),
            const SizedBox(height: 32),

            // Example 1: Basic usage
            ElevatedButton(
              onPressed: () => _showBasicDialog(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.white,
              ),
              child: const Text('Show Basic Dialog'),
            ),
            const SizedBox(height: 16),

            // Example 2: Custom text
            ElevatedButton(
              onPressed: () => _showCustomDialog(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
                foregroundColor: AppColors.white,
              ),
              child: const Text('Show Custom Dialog'),
            ),
            const SizedBox(height: 16),

            // Example 3: With callbacks
            ElevatedButton(
              onPressed: () => _showDialogWithCallbacks(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.warning,
                foregroundColor: AppColors.black,
              ),
              child: const Text('Show Dialog with Callbacks'),
            ),
            const SizedBox(height: 16),

            // Example 4: Non-dismissible
            ElevatedButton(
              onPressed: () => _showNonDismissibleDialog(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: AppColors.white,
              ),
              child: const Text('Show Non-Dismissible Dialog'),
            ),
          ],
        ),
      ),
    );
  }

  /// Example 1: Basic usage with default values
  void _showBasicDialog(BuildContext context) {
    PushNotificationDialog.show(context);
  }

  /// Example 2: Custom title and description
  void _showCustomDialog(BuildContext context) {
    PushNotificationDialog.show(
      context,
      title: 'Enable Notifications',
      description: 'Get notified about ride updates, driver arrivals, and important announcements to enhance your RideOn experience.',
      acceptButtonText: 'Enable',
      declineButtonText: 'Maybe Later',
    );
  }

  /// Example 3: With custom callbacks
  void _showDialogWithCallbacks(BuildContext context) {
    PushNotificationDialog.show(
      context,
      title: 'Stay Updated',
      description: 'Allow notifications to receive real-time updates about your rides and special offers.',
      onAccept: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Notifications enabled!'),
            backgroundColor: AppColors.success,
          ),
        );
      },
      onDecline: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Notifications declined'),
            backgroundColor: AppColors.warning,
          ),
        );
      },
    );
  }

  /// Example 4: Non-dismissible dialog
  void _showNonDismissibleDialog(BuildContext context) {
    PushNotificationDialog.show(
      context,
      title: 'Important Notice',
      description: 'This is a critical notification request that requires your attention. Please make a selection.',
      barrierDismissible: false,
      acceptButtonText: 'Accept',
      declineButtonText: 'Decline',
      onAccept: () {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Thank you for accepting!'),
            backgroundColor: AppColors.success,
          ),
        );
      },
      onDecline: () {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Request declined'),
            backgroundColor: AppColors.error,
          ),
        );
      },
    );
  }
}
