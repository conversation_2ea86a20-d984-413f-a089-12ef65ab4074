/// Model representing a Collection (Order) in the RideOn app
/// 
/// A collection is a container where all packages are added for payment purposes.
/// Even a single package must be added to a collection and payment is processed on the collection itself.
/// Once a collection status has moved to 'pending payment' or 'paid', it can no longer be updated.
class Collection {
  final String uuid;
  final DateTime created;
  final DateTime updated;
  final String status;
  final String orderPickupType;
  final DateTime? orderPickupDate;
  final String? note;
  final String type;
  final int amount;
  final dynamic pickupAddress; // Can be string UUID or Address object
  final dynamic deliveryAddress; // Can be string UUID or Address object
  final List<dynamic> packages; // Can be string UUIDs or Package objects
  final Map<String, dynamic>? meta;

  const Collection({
    required this.uuid,
    required this.created,
    required this.updated,
    required this.status,
    required this.orderPickupType,
    this.orderPickupDate,
    this.note,
    required this.type,
    required this.amount,
    this.pickupAddress,
    this.deliveryAddress,
    required this.packages,
    this.meta,
  });

  /// Create Collection from JSON
  factory Collection.fromJson(Map<String, dynamic> json) {
    return Collection(
      uuid: json['uuid'] as String,
      created: DateTime.parse(json['created'] as String),
      updated: DateTime.parse(json['updated'] as String),
      status: json['status'] as String,
      orderPickupType: json['orderPickupType'] as String,
      orderPickupDate: json['orderPickupDate'] != null 
          ? DateTime.parse(json['orderPickupDate'] as String)
          : null,
      note: json['note'] as String?,
      type: json['type'] as String,
      amount: json['amount'] as int,
      pickupAddress: json['pickupAddress'],
      deliveryAddress: json['deliveryAddress'],
      packages: json['packages'] as List<dynamic>? ?? [],
      meta: json['meta'] as Map<String, dynamic>?,
    );
  }

  /// Convert Collection to JSON
  Map<String, dynamic> toJson() {
    return {
      'uuid': uuid,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
      'status': status,
      'orderPickupType': orderPickupType,
      if (orderPickupDate != null) 'orderPickupDate': orderPickupDate!.toIso8601String(),
      if (note != null) 'note': note,
      'type': type,
      'amount': amount,
      if (pickupAddress != null) 'pickupAddress': pickupAddress,
      if (deliveryAddress != null) 'deliveryAddress': deliveryAddress,
      'packages': packages,
      if (meta != null) 'meta': meta,
    };
  }

  /// Create a copy with updated fields
  Collection copyWith({
    String? uuid,
    DateTime? created,
    DateTime? updated,
    String? status,
    String? orderPickupType,
    DateTime? orderPickupDate,
    String? note,
    String? type,
    int? amount,
    dynamic pickupAddress,
    dynamic deliveryAddress,
    List<dynamic>? packages,
    Map<String, dynamic>? meta,
  }) {
    return Collection(
      uuid: uuid ?? this.uuid,
      created: created ?? this.created,
      updated: updated ?? this.updated,
      status: status ?? this.status,
      orderPickupType: orderPickupType ?? this.orderPickupType,
      orderPickupDate: orderPickupDate ?? this.orderPickupDate,
      note: note ?? this.note,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      pickupAddress: pickupAddress ?? this.pickupAddress,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      packages: packages ?? this.packages,
      meta: meta ?? this.meta,
    );
  }

  @override
  String toString() {
    return 'Collection(uuid: $uuid, status: $status, amount: $amount, packages: ${packages.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Collection && other.uuid == uuid;
  }

  @override
  int get hashCode => uuid.hashCode;
}

/// Request model for creating a new collection
class CreateCollectionRequest {
  final String orderPickupType;
  final DateTime? orderPickupDate;
  final String? note;
  final String? pickupAddressUuid;
  final String? deliveryAddressUuid;

  const CreateCollectionRequest({
    required this.orderPickupType,
    this.orderPickupDate,
    this.note,
    this.pickupAddressUuid,
    this.deliveryAddressUuid,
  });

  Map<String, dynamic> toJson() {
    return {
      'orderPickupType': orderPickupType,
      if (orderPickupDate != null) 'orderPickupDate': orderPickupDate!.toIso8601String(),
      if (note != null) 'note': note,
      if (pickupAddressUuid != null) 'pickupAddress': pickupAddressUuid,
      if (deliveryAddressUuid != null) 'deliveryAddress': deliveryAddressUuid,
    };
  }
}

/// Request model for updating a collection
class UpdateCollectionRequest {
  final String? orderPickupType;
  final DateTime? orderPickupDate;
  final String? note;
  final String? pickupAddressUuid;
  final String? deliveryAddressUuid;

  const UpdateCollectionRequest({
    this.orderPickupType,
    this.orderPickupDate,
    this.note,
    this.pickupAddressUuid,
    this.deliveryAddressUuid,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {};
    
    if (orderPickupType != null) json['orderPickupType'] = orderPickupType;
    if (orderPickupDate != null) json['orderPickupDate'] = orderPickupDate!.toIso8601String();
    if (note != null) json['note'] = note;
    if (pickupAddressUuid != null) json['pickupAddress'] = pickupAddressUuid;
    if (deliveryAddressUuid != null) json['deliveryAddress'] = deliveryAddressUuid;
    
    return json;
  }
}
