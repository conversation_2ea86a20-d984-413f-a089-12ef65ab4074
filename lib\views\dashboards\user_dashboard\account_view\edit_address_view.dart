import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/providers/address_provider.dart';
import 'package:rideoon/services/address_service.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/services/geocoding_service.dart';
import 'package:rideoon/models/address/address_request.dart';

import 'package:rideoon/views/dashboards/user_dashboard/send_a_package/location_confirmation_map.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Edit Address page for modifying existing addresses
class EditAddressView extends StatefulWidget {
  final Map<String, dynamic> address;

  const EditAddressView({
    super.key,
    required this.address,
  });

  @override
  State<EditAddressView> createState() => _EditAddressViewState();
}

class _EditAddressViewState extends State<EditAddressView> {
  // Form controllers
  late final TextEditingController _nameController;
  late final TextEditingController _streetController;
  late final TextEditingController _landmarkController;
  late final TextEditingController _cityController;
  late final TextEditingController _lgaController;
  late final TextEditingController _phoneController;
  
  // Form state
  String _selectedState = 'Lagos';
  String _selectedType = 'pickup';
  bool _isLoading = false;
  bool _hasUnsavedChanges = false;


  // Location data
  LatLng? _confirmedLocation;
  double? _backgroundLatitude;
  double? _backgroundLongitude;

  // Track last auto-filled street address to detect user modifications
  String? _lastAutoFilledStreetAddress;
  
  // Nigerian states for dropdown
  final List<String> _nigerianStates = [
    'Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue',
    'Borno', 'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu',
    'FCT', 'Gombe', 'Imo', 'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi',
    'Kogi', 'Kwara', 'Lagos', 'Nasarawa', 'Niger', 'Ogun', 'Ondo', 'Osun',
    'Oyo', 'Plateau', 'Rivers', 'Sokoto', 'Taraba', 'Yobe', 'Zamfara'
  ];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _setupStreetAddressListener();
    _setupChangeListeners();
  }

  /// Setup listener for street address changes
  void _setupStreetAddressListener() {
    _streetController.addListener(() {
      final currentText = _streetController.text.trim();

      // Check if the street address has changed from the last auto-filled state
      if (_lastAutoFilledStreetAddress != null &&
          currentText != _lastAutoFilledStreetAddress) {
        // User has modified the street address after auto-fill
        setState(() {
          _backgroundLatitude = null;
          _backgroundLongitude = null;
          _confirmedLocation = null;
          _hasUnsavedChanges = true;
        });
        print('🔄 [EditAddressView] Street address modified, clearing coordinates');
      }
    });
  }

  /// Get the appropriate icon for the auto-fill button based on current state
  IconData _getAutoFillButtonIcon() {
    if (_isLoading) {
      return Icons.hourglass_empty;
    } else if (_hasLocationData()) {
      return Icons.check_circle;
    } else {
      return Icons.location_searching;
    }
  }

  /// Get the appropriate icon color for the auto-fill button
  Color _getAutoFillButtonIconColor() {
    if (_isLoading) {
      return AppColors.black.withValues(alpha: 0.4);
    } else {
      return AppColors.white;
    }
  }

  /// Get the appropriate text for the auto-fill button based on current state
  String _getAutoFillButtonText() {
    if (_isLoading) {
      return 'Auto-filling...';
    } else if (_hasLocationData()) {
      if (_hasStreetAddressChanged()) {
        return 'Confirm Auto-Fill';
      } else {
        return 'Location Auto-Filled ✓';
      }
    } else {
      return 'Auto-Fill Location Details';
    }
  }

  /// Get the appropriate text color for the auto-fill button
  Color _getAutoFillButtonTextColor() {
    if (_isLoading) {
      return AppColors.black.withValues(alpha: 0.4);
    } else {
      return AppColors.white;
    }
  }

  /// Get the appropriate background color for the auto-fill button
  Color _getAutoFillButtonBackgroundColor() {
    if (_isLoading) {
      return AppColors.black.withValues(alpha: 0.1);
    } else if (_hasLocationData()) {
      if (_hasStreetAddressChanged()) {
        return AppColors.warning; // Orange/amber color for "confirm" state
      } else {
        return AppColors.success; // Green for successful auto-fill
      }
    } else {
      return AppColors.primary; // Default blue
    }
  }

  /// Check if we have location data (coordinates)
  bool _hasLocationData() {
    return (_backgroundLatitude != null && _backgroundLongitude != null) ||
           _confirmedLocation != null;
  }

  /// Check if the street address has been modified since last auto-fill
  bool _hasStreetAddressChanged() {
    if (_lastAutoFilledStreetAddress == null) return false;
    return _streetController.text.trim() != _lastAutoFilledStreetAddress;
  }

  void _initializeControllers() {
    // Pre-populate controllers with existing data
    _nameController = TextEditingController(text: widget.address['name']?.toString() ?? '');
    _phoneController = TextEditingController(text: widget.address['phoneNumber']?.toString() ?? '');
    
    // Parse street and landmark from the street field
    final streetData = widget.address['street']?.toString() ?? '';
    final streetParts = streetData.split(', ');
    _streetController = TextEditingController(text: streetParts.isNotEmpty ? streetParts[0] : '');
    _landmarkController = TextEditingController(text: streetParts.length > 1 ? streetParts.sublist(1).join(', ') : '');
    
    // Parse city and LGA from the city field
    final cityData = widget.address['city']?.toString() ?? '';
    final cityParts = cityData.split(', ');
    _cityController = TextEditingController(text: cityParts.isNotEmpty ? cityParts[0] : '');
    _lgaController = TextEditingController(text: cityParts.length > 1 ? cityParts.sublist(1).join(', ') : '');
    
    // Set location coordinates if available
    final latitude = widget.address['latitude'];
    final longitude = widget.address['longitude'];
    if (latitude != null && longitude != null) {
      _confirmedLocation = LatLng(
        latitude is double ? latitude : double.tryParse(latitude.toString()) ?? 0.0,
        longitude is double ? longitude : double.tryParse(longitude.toString()) ?? 0.0,
      );
    }
    
    // Validate and set state
    final addressState = widget.address['state']?.toString().trim() ?? 'Lagos';
    _selectedState = _nigerianStates.contains(addressState) ? addressState : 'Lagos';
    
    // Validate and set type
    final addressType = widget.address['type']?.toString().toLowerCase().trim() ?? 'pickup';
    if (addressType == 'pickup' || addressType == 'receiver') {
      _selectedType = addressType;
    } else if (addressType == 'delivery' || addressType == 'deliver') {
      _selectedType = 'receiver';
    } else {
      _selectedType = 'pickup';
    }
  }

  void _setupChangeListeners() {
    _nameController.addListener(() => setState(() => _hasUnsavedChanges = true));
    _streetController.addListener(() => setState(() => _hasUnsavedChanges = true));
    _landmarkController.addListener(() => setState(() => _hasUnsavedChanges = true));
    _cityController.addListener(() => setState(() => _hasUnsavedChanges = true));
    _lgaController.addListener(() => setState(() => _hasUnsavedChanges = true));
    _phoneController.addListener(() => setState(() => _hasUnsavedChanges = true));
  }

  @override
  void dispose() {
    _nameController.dispose();
    _streetController.dispose();
    _landmarkController.dispose();
    _cityController.dispose();
    _lgaController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5FF),
      appBar: _buildAppBar(),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormFields(),
              const SizedBox(height: 32),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: IconButton(
        onPressed: () => _handleBackPress(),
        icon: Icon(
          Icons.arrow_back,
          color: AppColors.black,
          size: 24,
        ),
      ),
      title: Text(
        'Edit Address',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          fontFamily: 'Poppins',
          color: AppColors.black,
        ),
      ),
      centerTitle: true,
    );
  }

  Widget _buildFormFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Address Type Selection
        _buildDropdownField(
          label: 'Address Type',
          value: _selectedType,
          items: [
            DropdownMenuItem(value: 'pickup', child: Text('Pickup Address')),
            DropdownMenuItem(value: 'receiver', child: Text('Delivery Address')),
          ],
          onChanged: (value) {
            setState(() {
              _selectedType = value!;
              _hasUnsavedChanges = true;
            });
          },
        ),
        const SizedBox(height: 20),
        
        // Name Field
        _buildTextField(
          controller: _nameController,
          label: _selectedType == 'pickup' ? 'Sender Name' : 'Receiver Name',
          isRequired: true,
        ),
        const SizedBox(height: 20),
        
        // Street Address Field
        _buildTextField(
          controller: _streetController,
          label: 'Street Address',
          hint: 'e.g., 123 Main Street',
          maxLines: 2,
          isRequired: true,
        ),
        const SizedBox(height: 20),

        // Confirm on Map Button (moved after street address)
        _buildConfirmOnMapButton(),
        const SizedBox(height: 20),

        // Landmark Field
        _buildTextField(
          controller: _landmarkController,
          label: 'Landmark (Optional)',
          hint: 'e.g., Near City Mall',
        ),
        const SizedBox(height: 20),

        // City Field
        _buildTextField(
          controller: _cityController,
          label: 'City',
          hint: 'e.g., Ikeja',
          isRequired: true,
        ),
        const SizedBox(height: 20),
        
        // LGA Field
        _buildTextField(
          controller: _lgaController,
          label: 'Local Government Area',
          hint: 'e.g., Ikeja LGA',
          isRequired: true,
        ),
        const SizedBox(height: 20),
        
        // State Dropdown
        _buildDropdownField(
          label: 'State',
          value: _selectedState,
          items: _nigerianStates.map((state) => 
            DropdownMenuItem(value: state, child: Text(state))
          ).toList(),
          onChanged: (value) {
            setState(() {
              _selectedState = value!;
              _hasUnsavedChanges = true;
            });
          },
        ),
        const SizedBox(height: 20),
        
        // Phone Field
        _buildTextField(
          controller: _phoneController,
          label: 'Phone Number',
          keyboardType: TextInputType.phone,
          isRequired: true,
        ),
        const SizedBox(height: 20),
        
        // Location Confirmation Section
        _buildLocationSection(),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    TextInputType? keyboardType,
    int maxLines = 1,
    bool isRequired = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label + (isRequired ? ' *' : ''),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            fontFamily: 'Poppins',
            color: AppColors.black,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            hintText: hint,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.black.withValues(alpha: 0.2)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.black.withValues(alpha: 0.2)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          keyboardType: keyboardType,
          maxLines: maxLines,
        ),
      ],
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String value,
    required List<DropdownMenuItem<String>> items,
    required ValueChanged<String?> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label + ' *',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            fontFamily: 'Poppins',
            color: AppColors.black,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: value,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.black.withValues(alpha: 0.2)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.black.withValues(alpha: 0.2)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          items: items,
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildLocationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Location Verification (Optional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            fontFamily: 'Poppins',
            color: AppColors.black,
          ),
        ),
        const SizedBox(height: 12),

        // Verify on Map Button
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.map_outlined,
                    color: AppColors.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Verify Location on Map',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Poppins',
                            color: AppColors.primary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Optional: Verify and fine-tune your exact location on an interactive map',
                          style: TextStyle(
                            fontSize: 12,
                            fontFamily: 'Poppins',
                            color: AppColors.black.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              // Show location status if available
              if (_confirmedLocation != null || _backgroundLatitude != null) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _confirmedLocation != null
                        ? AppColors.primary.withValues(alpha: 0.1)
                        : AppColors.success.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: _confirmedLocation != null
                          ? AppColors.primary.withValues(alpha: 0.3)
                          : AppColors.success.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _confirmedLocation != null ? Icons.verified_outlined : Icons.location_on,
                        color: _confirmedLocation != null ? AppColors.primary : AppColors.success,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _confirmedLocation != null
                                  ? 'Map Verified Location'
                                  : 'Auto-Detected Location ✓',
                              style: TextStyle(
                                fontSize: 12,
                                fontFamily: 'Poppins',
                                color: _confirmedLocation != null ? AppColors.primary : AppColors.success,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              _confirmedLocation != null
                                  ? 'Lat: ${_confirmedLocation!.latitude.toStringAsFixed(6)}, Lng: ${_confirmedLocation!.longitude.toStringAsFixed(6)}'
                                  : 'Lat: ${_backgroundLatitude!.toStringAsFixed(6)}, Lng: ${_backgroundLongitude!.toStringAsFixed(6)}',
                              style: TextStyle(
                                fontSize: 10,
                                fontFamily: 'Poppins',
                                color: (_confirmedLocation != null ? AppColors.primary : AppColors.success).withValues(alpha: 0.8),
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 16),

              // Verify on Map Button
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: _isLoading ? null : _verifyLocationOnMap,
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    side: BorderSide(color: AppColors.primary),
                  ),
                  icon: Icon(
                    _confirmedLocation != null ? Icons.edit_location : Icons.location_searching,
                    size: 18,
                    color: AppColors.primary,
                  ),
                  label: Text(
                    _confirmedLocation != null
                        ? 'Update Location on Map'
                        : 'Verify on Map',
                    style: TextStyle(
                      fontSize: 14,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // Update Button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _updateAddress,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: _isLoading
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    'Update Address',
                    style: TextStyle(
                      fontSize: 16,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
        const SizedBox(height: 12),

        // Cancel Button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => _handleBackPress(),
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: AppColors.black.withValues(alpha: 0.3)),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              'Cancel',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w600,
                color: AppColors.black.withValues(alpha: 0.7),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Build the Confirm on Map button
  Widget _buildConfirmOnMapButton() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.map_outlined,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Auto-Fill All Location Fields',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Poppins',
                        color: AppColors.primary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Automatically fill Landmark, City, LGA, and State based on your street address',
                      style: TextStyle(
                        fontSize: 12,
                        fontFamily: 'Poppins',
                        color: AppColors.black.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : _autoDetectLocationDetails,
              icon: Icon(
                _getAutoFillButtonIcon(),
                size: 20,
                color: _getAutoFillButtonIconColor(),
              ),
              label: Text(
                _getAutoFillButtonText(),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Poppins',
                  color: _getAutoFillButtonTextColor(),
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: _getAutoFillButtonBackgroundColor(),
                foregroundColor: AppColors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Extract both city and LGA from formatted address string as comprehensive fallback
  Map<String, String?> _extractLocationFromFormattedAddress(String formattedAddress, String? detectedState) {
    try {
      final result = <String, String?>{
        'city': null,
        'lga': null,
        'landmark': null,
      };

      // Split address into parts
      final addressParts = formattedAddress.split(',').map((part) => part.trim()).toList();

      print('🔍 [EditAddressView] Analyzing formatted address parts:');
      for (int i = 0; i < addressParts.length; i++) {
        print('   [$i]: "${addressParts[i]}"');
      }

      // Common Nigerian city patterns
      final nigerianCities = [
        'Lagos', 'Abuja', 'Kano', 'Ibadan', 'Port Harcourt', 'Benin City', 'Maiduguri',
        'Zaria', 'Aba', 'Jos', 'Ilorin', 'Oyo', 'Enugu', 'Abeokuta', 'Kaduna',
        'Warri', 'Sokoto', 'Calabar', 'Katsina', 'Owerri', 'Bauchi', 'Akure',
        'Makurdi', 'Minna', 'Effon', 'Lafia', 'Umuahia', 'Gombe', 'Yenagoa',
        'Jalingo', 'Ado-Ekiti', 'Lokoja', 'Yola', 'Asaba', 'Awka', 'Damaturu',
        'Dutse', 'Birnin Kebbi', 'Gusau', 'Osogbo', 'Abakaliki', 'Uyo'
      ];

      // Look for known cities
      for (final part in addressParts) {
        for (final city in nigerianCities) {
          if (part.toLowerCase().contains(city.toLowerCase()) && result['city'] == null) {
            result['city'] = city;
            print('   → Found city: $city');
            break;
          }
        }
      }

      // If we have a detected state, try to find LGA patterns
      if (detectedState != null) {
        for (int i = 0; i < addressParts.length; i++) {
          final part = addressParts[i];

          // Check if this part is followed by the state (typical Nigerian address pattern)
          if (i < addressParts.length - 1) {
            final nextPart = addressParts[i + 1];
            if (nextPart.toLowerCase().contains(detectedState.toLowerCase())) {
              // This part might be an LGA
              final potentialLGA = part.trim();
              if (_isValidLGACandidate(potentialLGA)) {
                result['lga'] = _formatLocationName(potentialLGA);
                print('   → Found LGA (before state): ${result['lga']}');
                break;
              }
            }
          }
        }
      }

      // If still no LGA found, look for LGA keywords
      if (result['lga'] == null) {
        final lgaKeywords = [
          'Local Government', 'LGA', 'Local Govt', 'L.G.A', 'L.G',
          'Municipal', 'Council', 'Area Council', 'Development Area'
        ];

        for (final part in addressParts) {
          for (final keyword in lgaKeywords) {
            if (part.toLowerCase().contains(keyword.toLowerCase())) {
              final lgaName = part.toLowerCase()
                  .replaceAll(keyword.toLowerCase(), '')
                  .trim();

              if (lgaName.isNotEmpty && lgaName.length > 2) {
                result['lga'] = _formatLocationName(lgaName);
                print('   → Found LGA (with keyword): ${result['lga']}');
                break;
              }
            }
          }
          if (result['lga'] != null) break;
        }
      }

      // Extract landmark information from address parts
      if (result['landmark'] == null) {
        final landmarkKeywords = [
          'near', 'beside', 'opposite', 'behind', 'close to', 'next to',
          'mall', 'plaza', 'market', 'school', 'hospital', 'bank', 'church',
          'mosque', 'hotel', 'restaurant', 'filling station', 'petrol station',
          'junction', 'roundabout', 'bridge', 'park', 'stadium'
        ];

        for (final part in addressParts) {
          final lowerPart = part.toLowerCase();
          for (final keyword in landmarkKeywords) {
            if (lowerPart.contains(keyword)) {
              // This part likely contains landmark information
              final cleanedLandmark = part.trim();
              if (cleanedLandmark.isNotEmpty && cleanedLandmark.length > 3) {
                result['landmark'] = _formatLocationName(cleanedLandmark);
                print('   → Found landmark: ${result['landmark']}');
                break;
              }
            }
          }
          if (result['landmark'] != null) break;
        }
      }

      return result;
    } catch (e) {
      print('Error extracting location from formatted address: $e');
      return {'city': null, 'lga': null, 'landmark': null};
    }
  }

  /// Check if a string is a valid LGA candidate
  bool _isValidLGACandidate(String candidate) {
    final cleaned = candidate.toLowerCase().trim();

    // Must be reasonable length
    if (cleaned.length < 3 || cleaned.length > 50) return false;

    // Exclude common non-LGA terms
    final excludeTerms = [
      'nigeria', 'street', 'road', 'avenue', 'close', 'crescent',
      'estate', 'phase', 'block', 'flat', 'apartment', 'house',
      'no', 'number', 'km', 'mile', 'junction', 'roundabout'
    ];

    for (final term in excludeTerms) {
      if (cleaned.contains(term)) return false;
    }

    return true;
  }

  /// Format location name with proper capitalization
  String _formatLocationName(String name) {
    return name.split(' ')
        .map((word) => word.isEmpty ? '' :
            '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}')
        .join(' ');
  }

  /// Extract landmark information directly from street address input
  String? _extractLandmarkFromStreetAddress(String streetAddress) {
    try {
      if (streetAddress.isEmpty) return null;

      // Common landmark patterns and keywords for Nigerian addresses
      final landmarkPatterns = [
        // Religious buildings
        r'\b(st\.?\s+\w+\s+catholic\s+church|saint\s+\w+\s+catholic\s+church)\b',
        r'\b(\w+\s+catholic\s+church)\b',
        r'\b(\w+\s+church)\b',
        r'\b(\w+\s+mosque)\b',
        r'\b(\w+\s+cathedral)\b',

        // Commercial landmarks
        r'\b(\w+\s+mall|shopping\s+mall)\b',
        r'\b(\w+\s+plaza)\b',
        r'\b(\w+\s+market)\b',
        r'\b(\w+\s+hotel)\b',
        r'\b(\w+\s+bank)\b',
        r'\b(\w+\s+hospital)\b',
        r'\b(\w+\s+school)\b',
        r'\b(\w+\s+university)\b',
        r'\b(\w+\s+college)\b',

        // Infrastructure
        r'\b(\w+\s+junction)\b',
        r'\b(\w+\s+roundabout)\b',
        r'\b(\w+\s+bridge)\b',
        r'\b(\w+\s+park)\b',
        r'\b(\w+\s+stadium)\b',
        r'\b(\w+\s+filling\s+station)\b',
        r'\b(\w+\s+petrol\s+station)\b',
      ];

      final lowerAddress = streetAddress.toLowerCase();

      // Try to match landmark patterns
      for (final pattern in landmarkPatterns) {
        final regex = RegExp(pattern, caseSensitive: false);
        final match = regex.firstMatch(lowerAddress);

        if (match != null && match.group(1) != null) {
          final landmark = match.group(1)!.trim();
          if (landmark.isNotEmpty && landmark.length > 3) {
            // Format the landmark properly
            return _formatLocationName(landmark);
          }
        }
      }

      // Look for specific landmark keywords with context
      final landmarkKeywords = [
        'church', 'mosque', 'cathedral', 'chapel',
        'mall', 'plaza', 'market', 'shopping center',
        'hotel', 'bank', 'hospital', 'clinic',
        'school', 'university', 'college', 'institute',
        'junction', 'roundabout', 'bridge', 'park',
        'stadium', 'filling station', 'petrol station'
      ];

      // Split address into parts and look for landmarks
      final addressParts = streetAddress.split(RegExp(r'[,\s]+'))
          .where((part) => part.trim().isNotEmpty)
          .toList();

      for (int i = 0; i < addressParts.length; i++) {
        final part = addressParts[i].toLowerCase();

        for (final keyword in landmarkKeywords) {
          if (part.contains(keyword)) {
            // Try to get the full landmark name by including surrounding words
            final startIndex = (i - 2).clamp(0, addressParts.length);
            final endIndex = (i + 3).clamp(0, addressParts.length);

            final landmarkParts = addressParts.sublist(startIndex, endIndex);
            final potentialLandmark = landmarkParts.join(' ').trim();

            // Filter out common non-landmark terms
            if (!_containsNonLandmarkTerms(potentialLandmark) &&
                potentialLandmark.length > 3 &&
                potentialLandmark.length < 100) {
              return _formatLocationName(potentialLandmark);
            }
          }
        }
      }

      return null;
    } catch (e) {
      print('Error extracting landmark from street address: $e');
      return null;
    }
  }

  /// Check if text contains terms that are unlikely to be landmarks
  bool _containsNonLandmarkTerms(String text) {
    final nonLandmarkTerms = [
      'street', 'road', 'avenue', 'close', 'crescent',
      'estate', 'phase', 'block', 'flat', 'apartment',
      'house', 'no', 'number', 'km', 'mile',
      'nigeria', 'state', 'local', 'government', 'area'
    ];

    final lowerText = text.toLowerCase();
    return nonLandmarkTerms.any((term) => lowerText.contains(term));
  }

  /// Extract LGA from formatted address string as fallback
  String? _extractLGAFromFormattedAddress(String formattedAddress, String? detectedState) {
    try {
      // Common Nigerian LGA patterns and keywords
      final lgaKeywords = [
        'Local Government', 'LGA', 'Local Govt', 'L.G.A', 'L.G',
        'Municipal', 'Council', 'Area Council', 'Development Area'
      ];

      // Split address into parts
      final addressParts = formattedAddress.split(',').map((part) => part.trim()).toList();

      // Look for parts that might contain LGA information
      for (int i = 0; i < addressParts.length; i++) {
        final part = addressParts[i];

        // Check if this part contains LGA keywords
        for (final keyword in lgaKeywords) {
          if (part.toLowerCase().contains(keyword.toLowerCase())) {
            // Extract the LGA name (usually before the keyword)
            final lgaName = part.toLowerCase()
                .replaceAll(keyword.toLowerCase(), '')
                .trim()
                .split(' ')
                .where((word) => word.isNotEmpty)
                .join(' ');

            if (lgaName.isNotEmpty && lgaName.length > 2) {
              // Capitalize first letter of each word
              return lgaName.split(' ')
                  .map((word) => word.isEmpty ? '' :
                      '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}')
                  .join(' ');
            }
          }
        }

        // If we have a detected state, look for parts that might be LGA
        // (usually appear before the state in Nigerian addresses)
        if (detectedState != null && i < addressParts.length - 1) {
          final nextPart = addressParts[i + 1];
          if (nextPart.toLowerCase().contains(detectedState.toLowerCase())) {
            // This part might be an LGA since it's followed by the state
            final potentialLGA = part.trim();
            if (potentialLGA.isNotEmpty &&
                potentialLGA.length > 2 &&
                !potentialLGA.toLowerCase().contains('nigeria') &&
                !potentialLGA.toLowerCase().contains('street') &&
                !potentialLGA.toLowerCase().contains('road') &&
                !potentialLGA.toLowerCase().contains('avenue')) {

              // Capitalize first letter of each word
              return potentialLGA.split(' ')
                  .map((word) => word.isEmpty ? '' :
                      '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}')
                  .join(' ');
            }
          }
        }
      }

      return null;
    } catch (e) {
      print('Error extracting LGA from formatted address: $e');
      return null;
    }
  }

  /// Auto-detect location details using geocoding
  Future<void> _autoDetectLocationDetails() async {
    // Check if street address is provided
    if (_streetController.text.trim().isEmpty) {
      Toast.error('Please enter a street address first');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Build address for geocoding
      final addressParts = <String>[];
      addressParts.add(_streetController.text.trim());

      if (_landmarkController.text.trim().isNotEmpty) {
        addressParts.add(_landmarkController.text.trim());
      }

      // Add Nigeria for better geocoding results
      addressParts.add('Nigeria');

      final fullAddress = addressParts.join(', ');
      print('🗺️ [EditAddressView] Auto-detecting location for: "$fullAddress"');

      // Get detailed location information
      final locationDetails = await GeocodingService.getLocationDetails(fullAddress);

      if (locationDetails != null) {
        print('✅ [EditAddressView] Location details received');

        // Parse address components to extract state and LGA
        final addressComponents = locationDetails['addressComponents'] as List<dynamic>?;
        final formattedAddress = locationDetails['address'] as String?;

        if (addressComponents != null) {
          String? detectedState;
          String? detectedCity;
          String? detectedLGA;

          print('🔍 [EditAddressView] Parsing address components:');

          // Parse address components
          for (final component in addressComponents) {
            final types = component['types'] as List<dynamic>;
            final longName = component['long_name'] as String;
            final shortName = component['short_name'] as String;

            print('   - Component: "$longName" ($shortName) - Types: $types');

            // Check for administrative areas
            if (types.contains('administrative_area_level_1')) {
              // This is typically the state
              detectedState = longName;
              print('     → Detected as STATE');
            } else if (types.contains('administrative_area_level_2')) {
              // This could be LGA
              detectedLGA = longName;
              print('     → Detected as LGA (level 2)');
            } else if (types.contains('administrative_area_level_3')) {
              // This could also be LGA in some cases
              if (detectedLGA == null) {
                detectedLGA = longName;
                print('     → Detected as LGA (level 3)');
              }
            } else if (types.contains('locality')) {
              // This is typically the city/town
              detectedCity = longName;
              print('     → Detected as CITY (locality)');
            } else if (types.contains('sublocality') || types.contains('sublocality_level_1')) {
              // This could be a district or area within a city
              if (detectedCity == null) {
                detectedCity = longName;
                print('     → Detected as CITY (sublocality)');
              }
            }
          }

          // Fallback: Try to extract LGA from formatted address if not found in components
          if (detectedLGA == null && formattedAddress != null) {
            print('🔄 [EditAddressView] LGA not found in components, trying formatted address parsing...');
            detectedLGA = _extractLGAFromFormattedAddress(formattedAddress, detectedState);
            if (detectedLGA != null) {
              print('     → Extracted LGA from formatted address: "$detectedLGA"');
            }
          }

          // Update the form fields with detected values - ALWAYS fill all fields
          bool hasUpdates = false;

          // Always update state if detected
          if (detectedState != null && _nigerianStates.contains(detectedState)) {
            setState(() {
              _selectedState = detectedState!;
              _hasUnsavedChanges = true;
            });
            hasUpdates = true;
            print('🏛️ [EditAddressView] State auto-filled: $detectedState');
          }

          // Always update city if detected (overwrite existing)
          if (detectedCity != null && detectedCity.isNotEmpty) {
            _cityController.text = detectedCity;
            setState(() {
              _hasUnsavedChanges = true;
            });
            hasUpdates = true;
            print('🏙️ [EditAddressView] City auto-filled: $detectedCity');
          }

          // Always update LGA if detected (overwrite existing)
          if (detectedLGA != null && detectedLGA.isNotEmpty) {
            _lgaController.text = detectedLGA;
            setState(() {
              _hasUnsavedChanges = true;
            });
            hasUpdates = true;
            print('🏘️ [EditAddressView] LGA auto-filled: $detectedLGA');
          }

          // Extract landmark from the original street address input
          if (_landmarkController.text.trim().isEmpty) {
            final detectedLandmark = _extractLandmarkFromStreetAddress(_streetController.text.trim());
            if (detectedLandmark != null && detectedLandmark.isNotEmpty) {
              _landmarkController.text = detectedLandmark;
              setState(() {
                _hasUnsavedChanges = true;
              });
              hasUpdates = true;
              print('🏛️ [EditAddressView] Landmark auto-filled from street address: $detectedLandmark');
            }
          }

          // If we still don't have city or LGA, try to extract from formatted address
          if ((detectedCity == null || detectedLGA == null) && formattedAddress != null) {
            print('🔄 [EditAddressView] Attempting to extract missing fields from formatted address...');
            final extractedData = _extractLocationFromFormattedAddress(formattedAddress, detectedState);

            if (extractedData['city'] != null && detectedCity == null) {
              _cityController.text = extractedData['city']!;
              hasUpdates = true;
              print('🏙️ [EditAddressView] City extracted from address: ${extractedData['city']}');
            }

            if (extractedData['lga'] != null && detectedLGA == null) {
              _lgaController.text = extractedData['lga']!;
              hasUpdates = true;
              print('🏘️ [EditAddressView] LGA extracted from address: ${extractedData['lga']}');
            }

            // Auto-fill landmark if detected
            if (extractedData['landmark'] != null && _landmarkController.text.trim().isEmpty) {
              _landmarkController.text = extractedData['landmark']!;
              setState(() {
                _hasUnsavedChanges = true;
              });
              hasUpdates = true;
              print('🏛️ [EditAddressView] Landmark extracted from address: ${extractedData['landmark']}');
            }
          }

          // Also store coordinates for later use
          final latitude = locationDetails['latitude'] as double?;
          final longitude = locationDetails['longitude'] as double?;

          if (latitude != null && longitude != null) {
            setState(() {
              _backgroundLatitude = latitude;
              _backgroundLongitude = longitude;
            });
            print('📍 [EditAddressView] Coordinates auto-detected: $latitude, $longitude');
          }

          if (hasUpdates) {
            // Track the current street address as the last auto-filled one
            _lastAutoFilledStreetAddress = _streetController.text.trim();
            Toast.success('Location details auto-detected successfully');
          } else {
            Toast.info('No additional location details could be detected');
          }
        } else {
          Toast.error('Could not parse location details');
        }
      } else {
        Toast.error('Could not detect location details for this address');
      }
    } catch (e) {
      print('💥 [EditAddressView] Error auto-detecting location: $e');
      Toast.error('Failed to auto-detect location: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Get coordinates in background using geocoding
  Future<void> _getBackgroundCoordinates() async {
    print('🌍 [EditAddressView] Starting background geocoding...');

    setState(() {
      _isLoading = true;
    });

    try {
      // Build full address string
      final addressParts = <String>[];
      if (_streetController.text.trim().isNotEmpty) {
        addressParts.add(_streetController.text.trim());
      }
      if (_landmarkController.text.trim().isNotEmpty) {
        addressParts.add(_landmarkController.text.trim());
      }
      if (_cityController.text.trim().isNotEmpty) {
        addressParts.add(_cityController.text.trim());
      }
      if (_lgaController.text.trim().isNotEmpty) {
        addressParts.add(_lgaController.text.trim());
      }
      if (_selectedState.isNotEmpty) {
        addressParts.add(_selectedState);
      }
      addressParts.add('Nigeria');

      final fullAddress = addressParts.join(', ');
      print('📍 [EditAddressView] Full address for geocoding: "$fullAddress"');

      if (fullAddress.trim().isEmpty) {
        print('❌ [EditAddressView] Empty address, cannot geocode');
        Toast.error('Please enter address details first');
        return;
      }

      // Get coordinates using geocoding service
      print('🔍 [EditAddressView] Calling GeocodingService...');
      final coordinates = await GeocodingService.getCoordinatesFromAddress(fullAddress);

      if (coordinates != null) {
        print('✅ [EditAddressView] Geocoding successful:');
        print('   - Latitude: ${coordinates.latitude}');
        print('   - Longitude: ${coordinates.longitude}');

        setState(() {
          _backgroundLatitude = coordinates.latitude;
          _backgroundLongitude = coordinates.longitude;
          _hasUnsavedChanges = true;
        });
        Toast.success('Coordinates obtained successfully');
      } else {
        print('❌ [EditAddressView] Geocoding failed - no coordinates returned');
        Toast.error('Could not find coordinates for this address');
      }
    } catch (e, stackTrace) {
      print('💥 [EditAddressView] Exception during geocoding:');
      print('   - Error: $e');
      print('   - Stack trace: $stackTrace');
      Toast.error('Failed to get coordinates: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _verifyLocationOnMap() async {
    final currentAddress = _streetController.text.trim();
    if (currentAddress.isEmpty) {
      Toast.error('Please enter a street address first');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Build full address for better map initialization
      final addressParts = <String>[];
      addressParts.add(currentAddress);

      if (_landmarkController.text.trim().isNotEmpty) {
        addressParts.add(_landmarkController.text.trim());
      }

      if (_cityController.text.trim().isNotEmpty) {
        addressParts.add(_cityController.text.trim());
      }

      if (_lgaController.text.trim().isNotEmpty) {
        addressParts.add(_lgaController.text.trim());
      }

      if (_selectedState.isNotEmpty) {
        addressParts.add(_selectedState);
      }

      addressParts.add('Nigeria');
      final fullAddress = addressParts.join(', ');

      // Determine the best coordinates to pass to the map
      LatLng? coordinatesToPass;
      if (_confirmedLocation != null) {
        coordinatesToPass = _confirmedLocation;
        print('🗺️ [EditAddressView] Using map-verified coordinates: ${_confirmedLocation!.latitude}, ${_confirmedLocation!.longitude}');
      } else if (_backgroundLatitude != null && _backgroundLongitude != null) {
        coordinatesToPass = LatLng(_backgroundLatitude!, _backgroundLongitude!);
        print('🗺️ [EditAddressView] Using auto-detected coordinates: $_backgroundLatitude, $_backgroundLongitude');
      } else {
        print('🗺️ [EditAddressView] No coordinates available, map will geocode address');
      }

      print('🚀 [EditAddressView] Opening location verification map:');
      print('   - Full address: $fullAddress');
      print('   - Coordinates to pass: $coordinatesToPass');

      final result = await Navigator.of(context).push<Map<String, dynamic>>(
        MaterialPageRoute(
          builder: (context) => LocationConfirmationMap(
            address: fullAddress,
            title: 'Verify Address Location',
            initialCoordinates: coordinatesToPass,
          ),
        ),
      );

      if (result != null && mounted) {
        print('📥 [EditAddressView] Received result from map verification:');
        print('   - Result: $result');

        final coordinates = result['coordinates'] as LatLng?;
        final confirmedAddress = result['address'] as String?;

        if (coordinates != null) {
          setState(() {
            _confirmedLocation = coordinates;
            _hasUnsavedChanges = true;
          });

          print('✅ [EditAddressView] Map verification successful:');
          print('   - New coordinates: ${coordinates.latitude}, ${coordinates.longitude}');
          print('   - Address: $confirmedAddress');

          // Optionally update the address field with the confirmed address
          if (confirmedAddress != null && confirmedAddress.isNotEmpty &&
              confirmedAddress != fullAddress) {
            print('🔄 [EditAddressView] Updating street address with verified address');
            _streetController.text = confirmedAddress;
          }

          Toast.success('Location verified successfully');
        } else {
          print('❌ [EditAddressView] No coordinates received from map verification');
          Toast.error('Failed to verify location');
        }
      } else {
        print('ℹ️ [EditAddressView] Map verification cancelled or no result');
      }
    } catch (e) {
      Toast.error('Failed to verify location: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Show address confirmation dialog
  Future<bool> _showAddressConfirmationDialog() async {
    // Build full address for confirmation
    final addressParts = <String>[];
    if (_streetController.text.trim().isNotEmpty) {
      addressParts.add(_streetController.text.trim());
    }
    if (_landmarkController.text.trim().isNotEmpty) {
      addressParts.add('(${_landmarkController.text.trim()})');
    }
    if (_cityController.text.trim().isNotEmpty) {
      addressParts.add(_cityController.text.trim());
    }
    if (_lgaController.text.trim().isNotEmpty) {
      addressParts.add(_lgaController.text.trim());
    }
    if (_selectedState.isNotEmpty) {
      addressParts.add(_selectedState);
    }

    final fullAddress = addressParts.join(', ');
    final hasCoordinates = _confirmedLocation != null || _backgroundLatitude != null;

    return await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Confirm Address',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              fontFamily: 'Poppins',
              color: AppColors.black,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Please confirm this address is correct:',
                style: TextStyle(
                  fontSize: 14,
                  fontFamily: 'Poppins',
                  color: AppColors.black.withValues(alpha: 0.7),
                ),
              ),
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                ),
                child: Text(
                  fullAddress,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Poppins',
                    color: AppColors.black,
                  ),
                ),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    hasCoordinates ? Icons.location_on : Icons.location_off,
                    size: 16,
                    color: hasCoordinates ? AppColors.success : AppColors.warning,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    hasCoordinates ? 'Location coordinates available' : 'No location coordinates',
                    style: TextStyle(
                      fontSize: 12,
                      fontFamily: 'Poppins',
                      color: hasCoordinates ? AppColors.success : AppColors.warning,
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'Cancel',
                style: TextStyle(
                  color: AppColors.black.withValues(alpha: 0.6),
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.white,
              ),
              child: const Text('Confirm & Update'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  Future<void> _updateAddress() async {
    print('🔄 [EditAddressView] Update button pressed');

    if (!_validateForm()) {
      print('❌ [EditAddressView] Form validation failed');
      return;
    }
    print('✅ [EditAddressView] Form validation passed');

    // Show address confirmation dialog first
    print('📋 [EditAddressView] Showing address confirmation dialog');
    final confirmed = await _showAddressConfirmationDialog();
    if (!confirmed) {
      print('❌ [EditAddressView] User cancelled address confirmation');
      return;
    }
    print('✅ [EditAddressView] User confirmed address');

    setState(() {
      _isLoading = true;
    });
    print('⏳ [EditAddressView] Started loading state');

    try {
      print('🔐 [EditAddressView] Getting auth token...');
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        print('❌ [EditAddressView] No auth token available');
        Toast.error('Authentication required');
        return;
      }
      print('✅ [EditAddressView] Auth token obtained: ${authToken.substring(0, 20)}...');

      // Parse phone number to int
      print('📞 [EditAddressView] Parsing phone number: ${_phoneController.text}');
      final phoneNumber = int.tryParse(_phoneController.text.replaceAll(RegExp(r'[^\d]'), ''));
      if (phoneNumber == null) {
        print('❌ [EditAddressView] Invalid phone number: ${_phoneController.text}');
        Toast.error('Invalid phone number');
        return;
      }
      print('✅ [EditAddressView] Phone number parsed: $phoneNumber');

      // Get coordinates - prioritize map confirmation, then background geocoding
      double? finalLatitude;
      double? finalLongitude;

      print('📍 [EditAddressView] Determining coordinates...');
      print('   - Confirmed location: $_confirmedLocation');
      print('   - Background coordinates: lat=$_backgroundLatitude, lng=$_backgroundLongitude');

      if (_confirmedLocation != null) {
        // Use map-verified coordinates (highest priority)
        finalLatitude = _confirmedLocation!.latitude;
        finalLongitude = _confirmedLocation!.longitude;
        print('✅ [EditAddressView] Using map-verified coordinates: $finalLatitude, $finalLongitude');
      } else if (_backgroundLatitude != null && _backgroundLongitude != null) {
        // Use auto-detected coordinates
        finalLatitude = _backgroundLatitude;
        finalLongitude = _backgroundLongitude;
        print('✅ [EditAddressView] Using auto-detected coordinates: $finalLatitude, $finalLongitude');
      } else {
        // Try to get coordinates automatically as fallback
        print('🔍 [EditAddressView] Attempting to get coordinates automatically...');
        await _getBackgroundCoordinates();
        finalLatitude = _backgroundLatitude;
        finalLongitude = _backgroundLongitude;
        print('📍 [EditAddressView] Fallback coordinates result: $finalLatitude, $finalLongitude');
      }

      // Create proper AddressRequest with coordinates
      final fullStreet = _landmarkController.text.trim().isNotEmpty
          ? '${_streetController.text.trim()}, ${_landmarkController.text.trim()}'
          : _streetController.text.trim();
      final fullCity = _lgaController.text.trim().isNotEmpty
          ? '${_cityController.text.trim()}, ${_lgaController.text.trim()}'
          : _cityController.text.trim();

      print('🏗️ [EditAddressView] Building AddressRequest:');
      print('   - Name: ${_nameController.text.trim()}');
      print('   - Phone: $phoneNumber');
      print('   - Street: $fullStreet');
      print('   - City: $fullCity');
      print('   - State: $_selectedState');
      print('   - Type: $_selectedType');
      print('   - Coordinates: $finalLatitude, $finalLongitude');
      // Get the address UUID - it might be stored as 'id' or 'uuid'
      final addressUuid = widget.address['uuid'] ?? widget.address['id'];
      if (addressUuid == null) {
        throw Exception('Address UUID not found in address data');
      }
      print('   - Address UUID: $addressUuid');

      final addressRequest = AddressRequest(
        name: _nameController.text.trim(),
        phoneNumber: phoneNumber,
        street: fullStreet,
        city: fullCity,
        state: _selectedState,
        country: 'Nigeria',
        type: _selectedType,
        longitude: finalLongitude,
        latitude: finalLatitude,
      );

      print('📤 [EditAddressView] Sending update request to API...');
      print('📋 [EditAddressView] Request JSON: ${addressRequest.toJson()}');

      final response = await AddressService.updateAddress(
        addressUuid.toString(),
        addressRequest,
        authToken: authToken,
      );

      print('📥 [EditAddressView] API Response received:');
      print('   - Success: ${response.success}');
      print('   - Status Code: ${response.statusCode}');
      print('   - Message: ${response.message}');
      if (response.data != null) {
        print('   - Response Data: ${response.data!.toJson()}');
      }

      if (response.success) {
        print('✅ [EditAddressView] Address update successful');

        // Refresh the address list
        if (mounted) {
          print('🔄 [EditAddressView] Refreshing address provider...');
          final addressProvider = Provider.of<AddressProvider>(context, listen: false);
          addressProvider.refresh();
        }

        Toast.success('Address updated successfully');
        if (mounted) {
          print('🔙 [EditAddressView] Navigating back with success result');
          Navigator.of(context).pop(true); // Return true to indicate success
        }
      } else {
        print('❌ [EditAddressView] Address update failed: ${response.message}');
        Toast.error('Failed to update address: ${response.message}');
      }
    } catch (e, stackTrace) {
      print('💥 [EditAddressView] Exception during address update:');
      print('   - Error: $e');
      print('   - Stack trace: $stackTrace');
      Toast.error('Failed to update address: $e');
    } finally {
      if (mounted) {
        print('🏁 [EditAddressView] Ending loading state');
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  bool _validateForm() {
    print('📝 [EditAddressView] Validating form...');
    print('   - Name: "${_nameController.text.trim()}"');
    print('   - Street: "${_streetController.text.trim()}"');
    print('   - Landmark: "${_landmarkController.text.trim()}"');
    print('   - City: "${_cityController.text.trim()}"');
    print('   - LGA: "${_lgaController.text.trim()}"');
    print('   - State: "$_selectedState"');
    print('   - Type: "$_selectedType"');
    print('   - Phone: "${_phoneController.text.trim()}"');

    if (_nameController.text.trim().isEmpty) {
      print('❌ [EditAddressView] Validation failed: Name is empty');
      Toast.error('Please enter a name');
      return false;
    }
    if (_streetController.text.trim().isEmpty) {
      print('❌ [EditAddressView] Validation failed: Street is empty');
      Toast.error('Please enter a street address');
      return false;
    }
    if (_cityController.text.trim().isEmpty) {
      print('❌ [EditAddressView] Validation failed: City is empty');
      Toast.error('Please enter a city');
      return false;
    }
    if (_lgaController.text.trim().isEmpty) {
      print('❌ [EditAddressView] Validation failed: LGA is empty');
      Toast.error('Please enter a local government area');
      return false;
    }
    if (_phoneController.text.trim().isEmpty) {
      print('❌ [EditAddressView] Validation failed: Phone is empty');
      Toast.error('Please enter a phone number');
      return false;
    }

    print('✅ [EditAddressView] Form validation passed');
    return true;
  }

  void _handleBackPress() {
    if (_hasUnsavedChanges) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Unsaved Changes'),
          content: Text('You have unsaved changes. Are you sure you want to leave?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(); // Go back
              },
              child: Text('Leave'),
            ),
          ],
        ),
      );
    } else {
      Navigator.of(context).pop();
    }
  }
}
