import 'package:flutter/material.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';

/// Example file showing how to use the toast system throughout the app
/// 
/// This file demonstrates various ways to show toast messages using
/// the custom toast system integrated with AppColors.
class ToastUsageExamples extends StatelessWidget {
  const ToastUsageExamples({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Toast Usage Examples'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Toast System Examples',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                fontFamily: 'Poppins',
              ),
            ),
            const SizedBox(height: 20),
            
            // Success Toast
            ElevatedButton(
              onPressed: () {
                Toast.success('Order completed successfully!');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
              ),
              child: const Text('Show Success Toast'),
            ),
            const SizedBox(height: 12),
            
            // Error Toast
            ElevatedButton(
              onPressed: () {
                Toast.error('Failed to process payment. Please try again.');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
              ),
              child: const Text('Show Error Toast'),
            ),
            const SizedBox(height: 12),
            
            // Warning Toast
            ElevatedButton(
              onPressed: () {
                Toast.warning('Your location permission is required for better service.');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.warning,
              ),
              child: const Text('Show Warning Toast'),
            ),
            const SizedBox(height: 12),
            
            // Info Toast
            ElevatedButton(
              onPressed: () {
                Toast.info('New features are available in this update!');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
              ),
              child: const Text('Show Info Toast'),
            ),
            const SizedBox(height: 12),
            
            // Custom Toast
            ElevatedButton(
              onPressed: () {
                Toast.custom(
                  message: 'Rider is on the way to pickup location',
                  backgroundColor: AppColors.accept,
                  icon: Icons.directions_bike,
                  duration: const Duration(seconds: 5),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.accept,
              ),
              child: const Text('Show Custom Toast'),
            ),
            const SizedBox(height: 12),
            
            // Using direct method
            ElevatedButton(
              onPressed: () {
                Toast.success('Package delivered to destination!');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.rating,
              ),
              child: const Text('Show Direct Toast'),
            ),
            const SizedBox(height: 12),
            
            // Simple fallback toast
            ElevatedButton(
              onPressed: () {
                Toast.info('Simple toast message');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.black,
              ),
              child: const Text('Show Simple Toast'),
            ),
            const SizedBox(height: 20),
            
            // Clear toasts button
            OutlinedButton(
              onPressed: () {
                Toast.removeAll();
              },
              child: const Text('Clear All Toasts'),
            ),
            
            const SizedBox(height: 20),
            const Text(
              'Usage Examples:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                fontFamily: 'Poppins',
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '''
// Method 1: Using static methods
Toast.success('Success message');
Toast.error('Error message');
Toast.warning('Warning message');
Toast.info('Info message');

// Method 2: Custom toast
Toast.custom(
  message: 'Custom message',
  backgroundColor: AppColors.primary,
  icon: Icons.star,
);

// Method 3: Remove all toasts
Toast.removeAll();
              ''',
              style: TextStyle(
                fontSize: 12,
                fontFamily: 'monospace',
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Common toast messages used throughout the app
class AppToastMessages {
  // Authentication messages
  static void loginSuccess() => Toast.success('Welcome back!');
  static void loginError() => Toast.error('Invalid credentials. Please try again.');
  static void logoutSuccess() => Toast.info('You have been logged out successfully.');

  // Order messages
  static void orderPlaced() => Toast.success('Order placed successfully!');
  static void orderCancelled() => Toast.warning('Order has been cancelled.');
  static void orderCompleted() => Toast.success('Order completed successfully!');

  // Rider messages
  static void riderAccepted() => Toast.info('Rider has accepted your order.');
  static void riderArrived() => Toast.info('Rider has arrived at pickup location.');
  static void packagePickedUp() => Toast.info('Package has been picked up.');
  static void packageDelivered() => Toast.success('Package delivered successfully!');

  // Error messages
  static void networkError() => Toast.error('Network error. Please check your connection.');
  static void locationError() => Toast.warning('Unable to get your location. Please enable GPS.');
  static void paymentError() => Toast.error('Payment failed. Please try again.');

  // General messages
  static void featureComingSoon() => Toast.info('This feature is coming soon!');
  static void updateAvailable() => Toast.info('A new update is available.');
  static void maintenanceMode() => Toast.warning('App is under maintenance. Please try again later.');
}
