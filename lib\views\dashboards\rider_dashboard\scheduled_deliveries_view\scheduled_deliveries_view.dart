import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/services/package_data_service.dart';

/// Scheduled Deliveries view page for rider dashboard
///
/// This page contains scheduled delivery management features,
/// including available scheduled deliveries and active scheduled deliveries with pickup and delivery times.
class ScheduledDeliveriesView extends StatefulWidget {
  const ScheduledDeliveriesView({super.key});

  @override
  State<ScheduledDeliveriesView> createState() => _ScheduledDeliveriesViewState();
}

class _ScheduledDeliveriesViewState extends State<ScheduledDeliveriesView> with SingleTickerProviderStateMixin {
  TabController? _tabController;

  List<Map<String, dynamic>> _availableScheduledDeliveries = [];
  List<Map<String, dynamic>> _activeScheduledDeliveries = [];
  bool _isLoading = true;


  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadScheduledDeliveries();
  }

  /// Load scheduled deliveries from saved checkout data
  Future<void> _loadScheduledDeliveries() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Load completed orders from PackageDataService
      final completedOrders = await PackageDataService.getCompletedOrders();

      // Filter for scheduled deliveries only
      final scheduledDeliveries = completedOrders.where((order) {
        final packageData = order['packageData'] as Map<String, dynamic>?;
        final deliveryType = packageData?['deliveryType'] ?? 'instant';
        return deliveryType == 'scheduled';
      }).toList();

      // Convert to delivery format and categorize
      final availableDeliveries = <Map<String, dynamic>>[];
      final activeDeliveries = <Map<String, dynamic>>[];

      for (final order in scheduledDeliveries) {
        final deliveryData = _convertOrderToScheduledDelivery(order);

        // Categorize based on status
        final status = order['status'] ?? 'pending';
        if (status == 'pending' || status == 'available') {
          availableDeliveries.add(deliveryData);
        } else if (status == 'in_progress' || status == 'accepted' || status == 'scheduled') {
          activeDeliveries.add({
            ...deliveryData,
            'status': status,
            'acceptedTime': _formatTimestamp(order['timestamp']),
          });
        }
      }

      setState(() {
        _availableScheduledDeliveries = availableDeliveries;
        _activeScheduledDeliveries = activeDeliveries;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading scheduled deliveries: $e');
      setState(() {
        _availableScheduledDeliveries = [];
        _activeScheduledDeliveries = [];
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5FF),
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 7% gap from top of screen
            SizedBox(height: MediaQuery.of(context).size.height * 0.07),

            // Header
            _buildHeader(context),

            SizedBox(height: _getSpacing(context, 16)),

            // Subtitle
            _buildSubtitle(context),

            SizedBox(height: _getSpacing(context, 24)),

            // Tab bar
            _buildTabBar(context),

            SizedBox(height: _getSpacing(context, 16)),

            // Tab views
            Expanded(
              child: TabBarView(
                controller: _tabController!,
                children: [
                  _buildAvailableTab(context),
                  _buildActiveTab(context),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Center(
      child: Text(
        'Scheduled Deliveries',
        style: TextStyle(
          color: const Color(0xFF414141),
          fontSize: _getFontSize(context, 20),
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildSubtitle(BuildContext context) {
    return Center(
      child: Text(
        'Manage your scheduled delivery tasks',
        style: TextStyle(
          color: const Color(0xFF9A9A9A),
          fontSize: _getFontSize(context, 10),
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w500,
          letterSpacing: 0.20,
        ),
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      decoration: BoxDecoration(
        color: AppColors.black.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
      ),
      child: TabBar(
        controller: _tabController!,
        labelColor: AppColors.white,
        unselectedLabelColor: AppColors.black.withValues(alpha: 0.7),
        labelStyle: TextStyle(
          fontSize: _getFontSize(context, 13),
          fontWeight: FontWeight.w500,
          fontFamily: 'Poppins',
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: _getFontSize(context, 13),
          fontWeight: FontWeight.w400,
          fontFamily: 'Poppins',
        ),
        indicator: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        tabs: const [
          Tab(text: 'Available'),
          Tab(text: 'Active'),
        ],
      ),
    );
  }

  Widget _buildAvailableTab(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingState(context);
    }

    if (_availableScheduledDeliveries.isEmpty) {
      return _buildEmptyState(context, 'No Available Scheduled Deliveries', 'Check back later for new scheduled delivery opportunities');
    }

    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
        child: Column(
          children: _availableScheduledDeliveries.map((delivery) =>
            Padding(
              padding: EdgeInsets.only(bottom: _getSpacing(context, 12)),
              child: _buildAvailableScheduledDeliveryCard(context, delivery),
            ),
          ).toList(),
        ),
      ),
    );
  }

  Widget _buildActiveTab(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingState(context);
    }

    if (_activeScheduledDeliveries.isEmpty) {
      return _buildEmptyState(context, 'No Active Scheduled Deliveries', 'Accept available scheduled deliveries to see them here');
    }

    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
        child: Column(
          children: _activeScheduledDeliveries.map((delivery) =>
            Padding(
              padding: EdgeInsets.only(bottom: _getSpacing(context, 12)),
              child: _buildActiveScheduledDeliveryCard(context, delivery),
            ),
          ).toList(),
        ),
      ),
    );
  }

  Widget _buildAvailableScheduledDeliveryCard(BuildContext context, Map<String, dynamic> delivery) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 20)),
      decoration: ShapeDecoration(
        color: const Color(0xFFFEFEFE),
        shape: RoundedRectangleBorder(
          side: BorderSide(
            width: 1,
            color: AppColors.black.withValues(alpha: 0.07),
          ),
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with package type, amount and scheduled date
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Package type badge
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: _getSpacing(context, 8),
                  vertical: _getSpacing(context, 4),
                ),
                decoration: ShapeDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(_getBorderRadius(context, 10)),
                  ),
                ),
                child: Text(
                  delivery['packageType'] ?? 'Package',
                  style: TextStyle(
                    color: AppColors.primary,
                    fontSize: _getFontSize(context, 10),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              // Amount
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: _getSpacing(context, 8),
                  vertical: _getSpacing(context, 4),
                ),
                decoration: ShapeDecoration(
                  color: const Color(0x1C009951),
                  shape: RoundedRectangleBorder(
                    side: BorderSide(
                      width: 1,
                      color: AppColors.black.withValues(alpha: 0.03),
                    ),
                    borderRadius: BorderRadius.circular(_getBorderRadius(context, 10)),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.account_balance_wallet,
                      size: _getIconSize(context, 10),
                      color: const Color(0xFF14AE5C),
                    ),
                    SizedBox(width: _getSpacing(context, 4)),
                    Text(
                      '₦${delivery['amount'] ?? '0'}',
                      style: TextStyle(
                        color: const Color(0xFF14AE5C),
                        fontSize: _getFontSize(context, 10),
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                        height: 1.60,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: _getSpacing(context, 12)),

          // Scheduled date with appropriate icon
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: _getSpacing(context, 12),
              vertical: _getSpacing(context, 8),
            ),
            decoration: BoxDecoration(
              color: _getScheduleColor(delivery['scheduleType'] as String?).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _getScheduleDateIcon(delivery['scheduleType'] as String?),
                  size: _getIconSize(context, 14),
                  color: _getScheduleColor(delivery['scheduleType'] as String?),
                ),
                SizedBox(width: _getSpacing(context, 8)),
                Text(
                  delivery['scheduledDate'] ?? 'No date',
                  style: TextStyle(
                    color: _getScheduleColor(delivery['scheduleType'] as String?),
                    fontSize: _getFontSize(context, 12),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: _getSpacing(context, 20)),

          // Pickup location component with time
          _buildScheduledLocationComponent(
            context,
            icon: Icons.location_on,
            iconColor: const Color(0xFF9713E7),
            title: 'Pickup Location',
            location: delivery['pickupLocation'] ?? 'Unknown location',
            address: delivery['pickupAddress'] ?? 'No address',
            time: delivery['pickupTime'] ?? 'No time',
            scheduleType: delivery['scheduleType'] as String?,
            isCompleted: false,
            isLast: false,
          ),

          SizedBox(height: _getSpacing(context, 20)),

          // Delivery location component with time
          _buildScheduledLocationComponent(
            context,
            icon: Icons.flag,
            iconColor: const Color(0xFF14AE5C),
            title: 'Delivery Location',
            location: delivery['deliveryLocation'] ?? 'Unknown location',
            address: delivery['deliveryAddress'] ?? 'No address',
            time: delivery['deliveryTime'] ?? 'No time',
            scheduleType: delivery['scheduleType'] as String?,
            isCompleted: false,
            isLast: true,
          ),

          SizedBox(height: _getSpacing(context, 16)),

          // Distance and action button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Distance: ${delivery['distance'] ?? 'Unknown'}',
                style: TextStyle(
                  color: const Color(0xFF9A9A9A),
                  fontSize: _getFontSize(context, 10),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  // TODO: Implement accept scheduled delivery
                  Toast.success('Scheduled delivery accepted! You will be notified when it\'s time for pickup.');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    horizontal: _getSpacing(context, 16),
                    vertical: _getSpacing(context, 8),
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  'Accept',
                  style: TextStyle(
                    fontSize: _getFontSize(context, 12),
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Poppins',
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActiveScheduledDeliveryCard(BuildContext context, Map<String, dynamic> delivery) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 20)),
      decoration: ShapeDecoration(
        color: const Color(0xFFFEFEFE),
        shape: RoundedRectangleBorder(
          side: BorderSide(
            width: 1,
            color: AppColors.black.withValues(alpha: 0.07),
          ),
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with package type and amount
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Package type badge
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: _getSpacing(context, 8),
                  vertical: _getSpacing(context, 4),
                ),
                decoration: ShapeDecoration(
                  color: AppColors.warning.withValues(alpha: 0.1),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(_getBorderRadius(context, 10)),
                  ),
                ),
                child: Text(
                  delivery['packageType'] ?? 'Package',
                  style: TextStyle(
                    color: AppColors.warning,
                    fontSize: _getFontSize(context, 10),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              // Amount
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: _getSpacing(context, 8),
                  vertical: _getSpacing(context, 4),
                ),
                decoration: ShapeDecoration(
                  color: const Color(0x1C009951),
                  shape: RoundedRectangleBorder(
                    side: BorderSide(
                      width: 1,
                      color: AppColors.black.withValues(alpha: 0.03),
                    ),
                    borderRadius: BorderRadius.circular(_getBorderRadius(context, 10)),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.account_balance_wallet,
                      size: _getIconSize(context, 10),
                      color: const Color(0xFF14AE5C),
                    ),
                    SizedBox(width: _getSpacing(context, 4)),
                    Text(
                      '₦${delivery['amount'] ?? '0'}',
                      style: TextStyle(
                        color: const Color(0xFF14AE5C),
                        fontSize: _getFontSize(context, 10),
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                        height: 1.60,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: _getSpacing(context, 12)),

          // Scheduled date with appropriate icon
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: _getSpacing(context, 12),
              vertical: _getSpacing(context, 8),
            ),
            decoration: BoxDecoration(
              color: _getScheduleColor(delivery['scheduleType'] as String?).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _getScheduleDateIcon(delivery['scheduleType'] as String?),
                  size: _getIconSize(context, 14),
                  color: _getScheduleColor(delivery['scheduleType'] as String?),
                ),
                SizedBox(width: _getSpacing(context, 8)),
                Text(
                  delivery['scheduledDate'] ?? 'No date',
                  style: TextStyle(
                    color: _getScheduleColor(delivery['scheduleType'] as String?),
                    fontSize: _getFontSize(context, 12),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: _getSpacing(context, 20)),

          // Pickup location component with time
          _buildScheduledLocationComponent(
            context,
            icon: Icons.location_on,
            iconColor: const Color(0xFF9713E7),
            title: 'Pickup Location',
            location: delivery['pickupLocation'] ?? 'Unknown location',
            address: delivery['pickupAddress'] ?? 'No address',
            time: delivery['pickupTime'] ?? 'No time',
            scheduleType: delivery['scheduleType'] as String?,
            isCompleted: (delivery['status'] as String?) == 'pickup_ready' || (delivery['status'] as String?) == 'picked_up',
            isLast: false,
          ),

          SizedBox(height: _getSpacing(context, 20)),

          // Delivery location component with time
          _buildScheduledLocationComponent(
            context,
            icon: Icons.flag,
            iconColor: const Color(0xFF14AE5C),
            title: 'Delivery Location',
            location: delivery['deliveryLocation'] ?? 'Unknown location',
            address: delivery['deliveryAddress'] ?? 'No address',
            time: delivery['deliveryTime'] ?? 'No time',
            scheduleType: delivery['scheduleType'] as String?,
            isCompleted: false,
            isLast: true,
          ),

          SizedBox(height: _getSpacing(context, 16)),

          // Status and accepted time
          Container(
            padding: EdgeInsets.all(_getSpacing(context, 12)),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
            ),
            child: Row(
              children: [
                Icon(
                  (delivery['status'] as String?) == 'pickup_ready' ? Icons.schedule : Icons.event_available,
                  size: _getIconSize(context, 16),
                  color: AppColors.warning,
                ),
                SizedBox(width: _getSpacing(context, 8)),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        (delivery['status'] as String?) == 'pickup_ready' ? 'Ready for Pickup' : 'Scheduled',
                        style: TextStyle(
                          color: AppColors.warning,
                          fontSize: _getFontSize(context, 12),
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        'Accepted: ${delivery['acceptedTime'] ?? 'Unknown time'}',
                        style: TextStyle(
                          color: AppColors.black.withValues(alpha: 0.6),
                          fontSize: _getFontSize(context, 10),
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: _getSpacing(context, 16)),

          // Distance and action button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Distance: ${delivery['distance'] ?? 'Unknown'}',
                style: TextStyle(
                  color: const Color(0xFF9A9A9A),
                  fontSize: _getFontSize(context, 10),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  // TODO: Implement scheduled delivery action
                  if ((delivery['status'] as String?) == 'pickup_ready') {
                    Toast.info('Starting pickup process for scheduled delivery.');
                  } else {
                    Toast.info('Opening delivery details...');
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.warning,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    horizontal: _getSpacing(context, 16),
                    vertical: _getSpacing(context, 8),
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  (delivery['status'] as String?) == 'pickup_ready' ? 'Start Pickup' : 'View Details',
                  style: TextStyle(
                    fontSize: _getFontSize(context, 12),
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Poppins',
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildScheduledLocationComponent(
    BuildContext context, {
    required IconData icon,
    required Color iconColor,
    required String title,
    required String location,
    required String address,
    required String time,
    required String? scheduleType,
    required bool isCompleted,
    required bool isLast,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: _getIconSize(context, 19),
              height: _getIconSize(context, 19),
              decoration: BoxDecoration(
                color: const Color(0x23A9A9A9),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Container(
                  width: _getIconSize(context, 9),
                  height: _getIconSize(context, 9),
                  decoration: BoxDecoration(
                    color: isCompleted ? iconColor : const Color(0xFFA5A5A5),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: _getSpacing(context, 40),
                color: AppColors.black.withValues(alpha: 0.1),
              ),
          ],
        ),
        SizedBox(width: _getSpacing(context, 16)),
        // Content
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: isCompleted
                      ? AppColors.black.withValues(alpha: 0.4)
                      : const Color(0xFFA5A5A5),
                  fontSize: _getFontSize(context, 10),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                  height: 0.95,
                ),
              ),
              SizedBox(height: _getSpacing(context, 4)),
              Text(
                location,
                style: TextStyle(
                  color: const Color(0xFF111111),
                  fontSize: _getFontSize(context, 12),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w600,
                  height: 0.95,
                ),
              ),
              SizedBox(height: _getSpacing(context, 4)),
              // Time badge with appropriate icon based on schedule type
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: _getSpacing(context, 8),
                  vertical: _getSpacing(context, 4),
                ),
                decoration: BoxDecoration(
                  color: iconColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(_getBorderRadius(context, 6)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getScheduleIcon(scheduleType),
                      size: _getIconSize(context, 12),
                      color: iconColor,
                    ),
                    SizedBox(width: _getSpacing(context, 4)),
                    Text(
                      time,
                      style: TextStyle(
                        color: iconColor,
                        fontSize: _getFontSize(context, 10),
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: _getSpacing(context, 8)),
              Text(
                address,
                style: TextStyle(
                  color: AppColors.black,
                  fontSize: _getFontSize(context, 10),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                  height: 0.95,
                  letterSpacing: 0.20,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Helper method to get appropriate icon based on schedule type
  IconData _getScheduleIcon(String? scheduleType) {
    switch (scheduleType) {
      case 'time':
        return Icons.access_time;
      case 'day':
        return Icons.today;
      case 'month':
        return Icons.calendar_month;
      default:
        return Icons.schedule;
    }
  }

  // Helper method to get appropriate date icon based on schedule type
  IconData _getScheduleDateIcon(String? scheduleType) {
    switch (scheduleType) {
      case 'time':
        return Icons.schedule;
      case 'day':
        return Icons.date_range;
      case 'month':
        return Icons.calendar_view_month;
      default:
        return Icons.calendar_today;
    }
  }

  // Helper method to get appropriate color based on schedule type
  Color _getScheduleColor(String? scheduleType) {
    switch (scheduleType) {
      case 'time':
        return const Color(0xFF385AE5); // Blue for time-based
      case 'day':
        return const Color(0xFF9713E7); // Purple for day-based
      case 'month':
        return const Color(0xFF14AE5C); // Green for month-based
      default:
        return const Color(0xFF385AE5);
    }
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 40; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet
    } else {
      spacing = baseSpacing; // Mobile
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2; // Tablet
    } else {
      fontSize = baseFontSize; // Mobile
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2; // Tablet
    } else {
      iconSize = baseIconSize; // Mobile
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2; // Tablet
    } else {
      borderRadius = baseBorderRadius; // Mobile
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }

  /// Convert order data to scheduled delivery format
  Map<String, dynamic> _convertOrderToScheduledDelivery(Map<String, dynamic> order) {
    final pickupData = order['pickupData'] as Map<String, dynamic>? ?? {};
    final receiverData = order['receiverData'] as Map<String, dynamic>? ?? {};
    final paymentData = order['paymentData'] as Map<String, dynamic>? ?? {};
    final cargoItems = order['cargoItems'] as List? ?? [];
    final packageData = order['packageData'] as Map<String, dynamic>? ?? {};

    // Get package type from cargo items or package data
    String packageType = 'Package';
    if (cargoItems.isNotEmpty) {
      packageType = cargoItems.first['category'] ?? 'Package';
    } else if (packageData['packageDetails'] != null) {
      final packageDetails = packageData['packageDetails'] as Map<String, dynamic>;
      packageType = packageDetails['category'] ?? 'Package';
    }

    // Get schedule information
    final scheduleData = packageData['scheduleData'] as Map<String, dynamic>? ?? {};
    final scheduleType = scheduleData['type'] ?? 'time';

    return {
      'id': order['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
      'pickupLocation': _getLocationName(pickupData),
      'pickupAddress': pickupData['fullAddress'] ?? 'No address provided',
      'deliveryLocation': _getLocationName(receiverData),
      'deliveryAddress': receiverData['address'] ?? 'No address provided',
      'amount': (paymentData['total'] ?? 0.0).toStringAsFixed(0),
      'distance': _calculateDistance(pickupData, receiverData),
      'packageType': packageType,
      'pickupTime': _getScheduleTime(scheduleData, 'pickup'),
      'deliveryTime': _getScheduleTime(scheduleData, 'delivery'),
      'scheduledDate': _getScheduledDate(scheduleData),
      'scheduleType': scheduleType,
      'trackingNumber': order['trackingNumber'] ?? '#RO00000',
      'orderData': order, // Keep original order data for reference
    };
  }

  /// Get location name from address data
  String _getLocationName(Map<String, dynamic> addressData) {
    if (addressData['senderName'] != null) {
      return '${addressData['senderName']}, ${addressData['state'] ?? 'Unknown'}';
    } else if (addressData['name'] != null) {
      return '${addressData['name']}, ${addressData['state'] ?? 'Unknown'}';
    } else {
      return addressData['state'] ?? 'Unknown Location';
    }
  }

  /// Calculate approximate distance (placeholder implementation)
  String _calculateDistance(Map<String, dynamic> pickup, Map<String, dynamic> delivery) {
    // This is a placeholder - in a real app, you'd use actual coordinates
    return '${(2.0 + (pickup.hashCode % 10)).toStringAsFixed(1)} km';
  }

  /// Get schedule time based on schedule data
  String _getScheduleTime(Map<String, dynamic> scheduleData, String type) {
    final scheduleType = scheduleData['type'] ?? 'time';

    switch (scheduleType) {
      case 'time':
        if (type == 'pickup') {
          // Try different possible field names for pickup time
          return scheduleData['pickupTime'] ??
                 scheduleData['selectedTime'] ??
                 scheduleData['time'] ??
                 _formatTimeFromData(scheduleData, 'pickup') ??
                 '9:00 AM - 5:00 PM';
        } else {
          // Try different possible field names for delivery time
          return scheduleData['deliveryTime'] ??
                 scheduleData['selectedDeliveryTime'] ??
                 scheduleData['deliveryWindow'] ??
                 _formatTimeFromData(scheduleData, 'delivery') ??
                 'Same Day';
        }
      case 'day':
        final days = scheduleData['selectedDays'] as List?;
        if (days != null && days.isNotEmpty) {
          return days.join(', ');
        }
        return scheduleData['dayRange'] ?? 'Monday - Friday';
      case 'month':
        final months = scheduleData['selectedMonths'] as List?;
        if (months != null && months.isNotEmpty) {
          return months.join(', ');
        }
        return scheduleData['monthRange'] ?? 'This Quarter';
      default:
        // Try to extract any time-related information
        return _extractAnyTimeInfo(scheduleData) ?? 'Flexible Schedule';
    }
  }

  /// Try to format time from various data fields
  String? _formatTimeFromData(Map<String, dynamic> data, String type) {
    // Look for hour/minute fields
    final hour = data['${type}Hour'] ?? data['hour'];
    final minute = data['${type}Minute'] ?? data['minute'];

    if (hour != null) {
      final h = hour is int ? hour : int.tryParse(hour.toString());
      final m = minute is int ? minute : int.tryParse(minute?.toString() ?? '0');

      if (h != null) {
        final period = h >= 12 ? 'PM' : 'AM';
        final displayHour = h > 12 ? h - 12 : (h == 0 ? 12 : h);
        final displayMinute = (m ?? 0).toString().padLeft(2, '0');
        return '$displayHour:$displayMinute $period';
      }
    }

    // Look for time range fields
    final startTime = data['${type}StartTime'] ?? data['startTime'];
    final endTime = data['${type}EndTime'] ?? data['endTime'];

    if (startTime != null && endTime != null) {
      return '$startTime - $endTime';
    }

    return null;
  }

  /// Extract any time-related information from schedule data
  String? _extractAnyTimeInfo(Map<String, dynamic> data) {
    // Check for common time fields
    final timeFields = [
      'time', 'selectedTime', 'preferredTime', 'timeSlot',
      'pickupTime', 'deliveryTime', 'scheduleTime'
    ];

    for (final field in timeFields) {
      final value = data[field];
      if (value != null && value.toString().isNotEmpty) {
        return value.toString();
      }
    }

    // Check for date/time objects
    final dateTime = data['dateTime'] ?? data['scheduledDateTime'];
    if (dateTime != null) {
      try {
        final dt = dateTime is DateTime
            ? dateTime
            : DateTime.parse(dateTime.toString());
        return '${dt.hour}:${dt.minute.toString().padLeft(2, '0')} ${dt.hour >= 12 ? 'PM' : 'AM'}';
      } catch (e) {
        // Ignore parsing errors
      }
    }

    return null;
  }

  /// Get scheduled date display
  String _getScheduledDate(Map<String, dynamic> scheduleData) {
    final scheduleType = scheduleData['type'] ?? 'time';

    switch (scheduleType) {
      case 'time':
        // Try to get actual selected date
        final selectedDate = scheduleData['selectedDate'] ??
                           scheduleData['date'] ??
                           scheduleData['scheduledDate'];

        if (selectedDate != null) {
          try {
            final date = selectedDate is DateTime
                ? selectedDate
                : DateTime.parse(selectedDate.toString());

            final now = DateTime.now();
            final today = DateTime(now.year, now.month, now.day);
            final targetDate = DateTime(date.year, date.month, date.day);

            final difference = targetDate.difference(today).inDays;

            if (difference == 0) {
              return 'Today, ${_formatDateShort(date)}';
            } else if (difference == 1) {
              return 'Tomorrow, ${_formatDateShort(date)}';
            } else if (difference > 1 && difference <= 7) {
              return '${_getDayName(date.weekday)}, ${_formatDateShort(date)}';
            } else {
              return _formatDateShort(date);
            }
          } catch (e) {
            return selectedDate.toString();
          }
        }
        return 'Today';

      case 'day':
        final selectedDays = scheduleData['selectedDays'] as List?;
        if (selectedDays != null && selectedDays.isNotEmpty) {
          return 'Every ${selectedDays.join(', ')}';
        }
        return 'This Week';

      case 'month':
        final selectedMonths = scheduleData['selectedMonths'] as List?;
        if (selectedMonths != null && selectedMonths.isNotEmpty) {
          return selectedMonths.join(', ');
        }
        return 'This Quarter';

      default:
        return _extractAnyDateInfo(scheduleData) ?? 'Flexible Schedule';
    }
  }

  /// Format date in short format
  String _formatDateShort(DateTime date) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[date.month - 1]} ${date.day}';
  }

  /// Get day name from weekday number
  String _getDayName(int weekday) {
    const days = [
      'Monday', 'Tuesday', 'Wednesday', 'Thursday',
      'Friday', 'Saturday', 'Sunday'
    ];
    return days[weekday - 1];
  }

  /// Extract any date-related information from schedule data
  String? _extractAnyDateInfo(Map<String, dynamic> data) {
    // Check for common date fields
    final dateFields = [
      'date', 'selectedDate', 'preferredDate', 'scheduledDate',
      'targetDate', 'deliveryDate', 'pickupDate'
    ];

    for (final field in dateFields) {
      final value = data[field];
      if (value != null) {
        try {
          final date = value is DateTime
              ? value
              : DateTime.parse(value.toString());
          return _formatDateShort(date);
        } catch (e) {
          // If it's not a valid date, return as string
          if (value.toString().isNotEmpty) {
            return value.toString();
          }
        }
      }
    }

    return null;
  }

  /// Format timestamp for display
  String? _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return null;

    try {
      final dateTime = timestamp is int
          ? DateTime.fromMillisecondsSinceEpoch(timestamp)
          : DateTime.parse(timestamp.toString());

      return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return null;
    }
  }

  /// Build loading state
  Widget _buildLoadingState(BuildContext context) {
    return Center(
      child: CircularProgressIndicator(
        color: AppColors.primary,
      ),
    );
  }

  /// Build empty state
  Widget _buildEmptyState(BuildContext context, String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.schedule,
            size: 64,
            color: AppColors.black.withValues(alpha: 0.3),
          ),
          SizedBox(height: _getSpacing(context, 16)),
          Text(
            title,
            style: TextStyle(
              fontSize: _getFontSize(context, 18),
              fontWeight: FontWeight.w600,
              fontFamily: 'Poppins',
              color: AppColors.black.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: _getSpacing(context, 8)),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: _getFontSize(context, 14),
              fontFamily: 'Poppins',
              color: AppColors.black.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
