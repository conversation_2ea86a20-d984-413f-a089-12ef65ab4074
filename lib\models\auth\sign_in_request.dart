/// Request model for user sign-in API call
class SignInRequest {
  final String email;
  final String password;

  const SignInRequest({
    required this.email,
    required this.password,
  });

  /// Convert the request to JSON format for API call
  Map<String, dynamic> to<PERSON>son() {
    return {
      'email': email,
      'password': password,
    };
  }

  /// Create a SignInRequest from JSON data
  factory SignInRequest.fromJson(Map<String, dynamic> json) {
    return SignInRequest(
      email: json['email'] as String,
      password: json['password'] as String,
    );
  }

  /// Create a copy of this request with some fields updated
  SignInRequest copyWith({
    String? email,
    String? password,
  }) {
    return SignInRequest(
      email: email ?? this.email,
      password: password ?? this.password,
    );
  }

  @override
  String toString() {
    return 'SignInRequest(email: $email)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SignInRequest &&
        other.email == email &&
        other.password == password;
  }

  @override
  int get hashCode {
    return Object.hash(email, password);
  }
}
