import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/shipment_card.dart';

/// Reusable Current Shipments widget
/// 
/// A customizable widget for displaying current shipments with a header
/// and list of shipment cards. Supports responsive design and callbacks.
class CurrentShipmentsWidget extends StatefulWidget {
  /// Title for the section
  final String title;
  
  /// List of shipments to display
  final List<ShipmentData> shipments;
  
  /// Callback when "See all" is tapped
  final VoidCallback? onSeeAllTap;
  
  /// Callback when a shipment card is tapped
  final Function(ShipmentData shipment)? onShipmentTap;
  
  /// Whether to show the "See all" link
  final bool showSeeAll;
  
  /// Custom "See all" text
  final String? seeAllText;
  
  /// Maximum number of shipments to display
  final int? maxShipments;
  
  /// Whether to show tracking timeline by default
  final bool showTrackingTimeline;
  
  /// Custom horizontal padding
  final double? horizontalPadding;
  
  /// Custom empty state widget
  final Widget? emptyStateWidget;

  const CurrentShipmentsWidget({
    super.key,
    this.title = 'Current Shipments',
    required this.shipments,
    this.onSeeAllTap,
    this.onShipmentTap,
    this.showSeeAll = true,
    this.seeAllText,
    this.maxShipments,
    this.showTrackingTimeline = true,
    this.horizontalPadding,
    this.emptyStateWidget,
  });

  @override
  State<CurrentShipmentsWidget> createState() => _CurrentShipmentsWidgetState();
}

class _CurrentShipmentsWidgetState extends State<CurrentShipmentsWidget> {
  late List<ShipmentData> _shipments;

  @override
  void initState() {
    super.initState();
    _shipments = List.from(widget.shipments);
  }

  @override
  void didUpdateWidget(CurrentShipmentsWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.shipments != widget.shipments) {
      _shipments = List.from(widget.shipments);
    }
  }

  void _toggleExpansion(int index) {
    setState(() {
      _shipments[index] = _shipments[index].copyWith(
        isExpanded: !_shipments[index].isExpanded,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    final displayShipments = widget.maxShipments != null 
        ? _shipments.take(widget.maxShipments!).toList()
        : _shipments;

    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: widget.horizontalPadding ?? _getHorizontalPadding(context),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and see all
          _buildHeader(context),
          
          SizedBox(height: _getSpacing(context, 16)),
          
          // Shipments list or empty state
          if (displayShipments.isEmpty)
            widget.emptyStateWidget ?? _buildEmptyState(context)
          else
            _buildShipmentsList(context, displayShipments),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          widget.title,
          style: TextStyle(
            color: AppColors.black,
            fontSize: _getFontSize(context, 20),
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w500,
            height: 0.95,
            letterSpacing: -1,
          ),
        ),
        if (widget.showSeeAll && widget.onSeeAllTap != null)
          GestureDetector(
            onTap: widget.onSeeAllTap,
            child: Text(
              widget.seeAllText ?? 'See all',
              style: TextStyle(
                color: AppColors.primary,
                fontSize: _getFontSize(context, 14),
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w500,
                height: 0.95,
                letterSpacing: -0.5,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildShipmentsList(BuildContext context, List<ShipmentData> shipments) {
    return Column(
      children: shipments.asMap().entries.map((entry) {
        final index = entry.key;
        final shipment = entry.value;
        
        return Padding(
          padding: EdgeInsets.only(
            bottom: index < shipments.length - 1 ? _getSpacing(context, 16) : 0,
          ),
          child: ShipmentCard(
            shipment: shipment,
            onExpandToggle: widget.showTrackingTimeline 
                ? () => _toggleExpansion(index)
                : null,
            onCardTap: widget.onShipmentTap != null 
                ? () => widget.onShipmentTap!(shipment)
                : null,
            showTrackingTimeline: widget.showTrackingTimeline,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 32)),
      decoration: BoxDecoration(
        color: AppColors.black.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 16)),
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: _getIconSize(context, 48),
              color: AppColors.black.withValues(alpha: 0.4),
            ),
            SizedBox(height: _getSpacing(context, 12)),
            Text(
              'No current shipments',
              style: TextStyle(
                color: AppColors.black.withValues(alpha: 0.6),
                fontSize: _getFontSize(context, 16),
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: _getSpacing(context, 4)),
            Text(
              'Your active shipments will appear here',
              style: TextStyle(
                color: AppColors.black.withValues(alpha: 0.5),
                fontSize: _getFontSize(context, 14),
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16;
    } else if (screenWidth > 600) {
      basePadding = 40;
    } else {
      basePadding = 24;
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8;
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2;
    } else {
      fontSize = baseFontSize;
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8;
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2;
    } else {
      iconSize = baseIconSize;
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6;
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2;
    } else {
      borderRadius = baseBorderRadius;
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }
}
