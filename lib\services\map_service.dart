import 'dart:async';
import 'dart:math' as math;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location/location.dart';
import 'package:rideoon/services/config_service.dart';

/// Service class to manage Google Maps functionality
/// Uses environment variables for configuration
class MapService {
  static Location? _location;
  static StreamSubscription<LocationData>? _locationSubscription;
  static LocationData? _currentLocation;
  static final Set<Marker> _markers = {};
  static GoogleMapController? _mapController;

  /// Initialize the map service
  static Future<void> initialize() async {
    _location = Location();
    
    // Check if location service is enabled
    bool serviceEnabled = await _location!.serviceEnabled();
    if (!serviceEnabled) {
      serviceEnabled = await _location!.requestService();
      if (!serviceEnabled) {
        throw Exception('Location service is not enabled');
      }
    }

    // Check location permissions
    PermissionStatus permissionGranted = await _location!.hasPermission();
    if (permissionGranted == PermissionStatus.denied) {
      permissionGranted = await _location!.requestPermission();
      if (permissionGranted != PermissionStatus.granted) {
        throw Exception('Location permission not granted');
      }
    }
  }

  /// Get the Google Maps API key from environment variables
  static String get apiKey => ConfigService.googleMapsApiKey;

  /// Check if the map service is properly configured
  static bool get isConfigured => ConfigService.hasValue('GOOGLE_MAPS_API_KEY');

  /// Get current location
  static Future<LocationData?> getCurrentLocation() async {
    if (_location == null) {
      await initialize();
    }
    
    try {
      _currentLocation = await _location!.getLocation();
      return _currentLocation;
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('Error getting current location: $e');
      }
      return null;
    }
  }

  /// Start listening to location updates
  static StreamSubscription<LocationData>? startLocationUpdates({
    required Function(LocationData) onLocationUpdate,
  }) {
    if (_location == null) return null;

    _locationSubscription = _location!.onLocationChanged.listen(
      (LocationData locationData) {
        _currentLocation = locationData;
        onLocationUpdate(locationData);
      },
      onError: (error) {
        if (ConfigService.enableDebugMode) {
          print('Location update error: $error');
        }
      },
    );

    return _locationSubscription;
  }

  /// Stop listening to location updates
  static void stopLocationUpdates() {
    _locationSubscription?.cancel();
    _locationSubscription = null;
  }

  /// Set the map controller
  static void setMapController(GoogleMapController controller) {
    _mapController = controller;
  }

  /// Move camera to specific location
  static Future<void> moveCameraToLocation(LatLng location, {double zoom = 15.0}) async {
    if (_mapController != null) {
      await _mapController!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: location,
            zoom: zoom,
          ),
        ),
      );
    }
  }

  /// Move camera to current location
  static Future<void> moveCameraToCurrentLocation({double zoom = 15.0}) async {
    final location = await getCurrentLocation();
    if (location != null && location.latitude != null && location.longitude != null) {
      await moveCameraToLocation(
        LatLng(location.latitude!, location.longitude!),
        zoom: zoom,
      );
    }
  }

  /// Add a marker to the map
  static void addMarker({
    required String markerId,
    required LatLng position,
    String? title,
    String? snippet,
    BitmapDescriptor? icon,
  }) {
    final marker = Marker(
      markerId: MarkerId(markerId),
      position: position,
      infoWindow: InfoWindow(
        title: title,
        snippet: snippet,
      ),
      icon: icon ?? BitmapDescriptor.defaultMarker,
    );
    
    _markers.add(marker);
  }

  /// Remove a marker from the map
  static void removeMarker(String markerId) {
    _markers.removeWhere((marker) => marker.markerId.value == markerId);
  }

  /// Clear all markers
  static void clearMarkers() {
    _markers.clear();
  }

  /// Get all markers
  static Set<Marker> get markers => Set.from(_markers);

  /// Add current location marker
  static Future<void> addCurrentLocationMarker() async {
    final location = await getCurrentLocation();
    if (location != null && location.latitude != null && location.longitude != null) {
      addMarker(
        markerId: 'current_location',
        position: LatLng(location.latitude!, location.longitude!),
        title: 'Your Location',
        snippet: 'Current position',
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
      );
    }
  }

  /// Get default camera position (centered on a default location)
  static CameraPosition get defaultCameraPosition {
    // Default to New York City if no location is available
    return const CameraPosition(
      target: LatLng(40.7128, -74.0060),
      zoom: 12.0,
    );
  }

  /// Get camera position for current location
  static Future<CameraPosition> getCurrentLocationCameraPosition({double zoom = 15.0}) async {
    final location = await getCurrentLocation();
    if (location != null && location.latitude != null && location.longitude != null) {
      return CameraPosition(
        target: LatLng(location.latitude!, location.longitude!),
        zoom: zoom,
      );
    }
    return defaultCameraPosition;
  }

  /// Calculate distance between two points (in kilometers)
  static double calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    double lat1Rad = point1.latitude * (math.pi / 180);
    double lat2Rad = point2.latitude * (math.pi / 180);
    double deltaLatRad = (point2.latitude - point1.latitude) * (math.pi / 180);
    double deltaLngRad = (point2.longitude - point1.longitude) * (math.pi / 180);

    double a = math.sin(deltaLatRad / 2) * math.sin(deltaLatRad / 2) +
        math.cos(lat1Rad) * math.cos(lat2Rad) *
        math.sin(deltaLngRad / 2) * math.sin(deltaLngRad / 2);
    double c = 2 * math.asin(math.sqrt(a));

    return earthRadius * c;
  }

  /// Dispose of resources
  static void dispose() {
    stopLocationUpdates();
    _mapController?.dispose();
    _mapController = null;
    _currentLocation = null;
    clearMarkers();
  }

  /// Get configuration info for debugging
  static Map<String, dynamic> getConfigInfo() {
    if (!ConfigService.isDevelopment) {
      throw Exception('Config info can only be accessed in development mode');
    }
    
    return {
      'hasApiKey': isConfigured,
      'apiKeyLength': apiKey.length,
      'currentLocation': _currentLocation != null ? {
        'latitude': _currentLocation!.latitude,
        'longitude': _currentLocation!.longitude,
      } : null,
      'markersCount': _markers.length,
      'isLocationServiceInitialized': _location != null,
    };
  }
}
