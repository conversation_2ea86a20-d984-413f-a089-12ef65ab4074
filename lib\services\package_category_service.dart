import 'dart:async';
import 'package:rideoon/services/api_service.dart';
import 'package:rideoon/models/api_response.dart';

/// Service for fetching package categories from the API
class PackageCategoryService {
  static Map<String, dynamic>? _cachedCategories;
  static DateTime? _lastFetch;
  static const Duration _cacheExpiry = Duration(hours: 1);

  /// Fetch package categories from API
  static Future<ApiResponse<Map<String, dynamic>>> getPackageCategories() async {
    // Return cached data if still valid
    if (_cachedCategories != null && 
        _lastFetch != null && 
        DateTime.now().difference(_lastFetch!) < _cacheExpiry) {
      return ApiResponse<Map<String, dynamic>>(
        success: true,
        data: _cachedCategories!,
        message: 'Categories loaded from cache',
      );
    }

    try {
      final response = await ApiService.makeRequest<Map<String, dynamic>>(
        endpoint: '/v1/s/config/package-categories/',
        method: 'GET',
        requiresAuth: false,
        fromJson: (json) => json,
      );

      if (response.success && response.data != null) {
        _cachedCategories = response.data;
        _lastFetch = DateTime.now();
      }

      return response;
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: 'Failed to fetch package categories: $e',
      );
    }
  }

  /// Get flattened category list for UI display
  static List<Map<String, dynamic>> getFlattenedCategories(Map<String, dynamic> categories) {
    final List<Map<String, dynamic>> flattened = [];
    
    void _flatten(Map<String, dynamic> data, List<String> path) {
      data.forEach((key, value) {
        final currentPath = [...path, key];
        
        if (value is Map<String, dynamic>) {
          // This is a parent category, recurse into it
          _flatten(value, currentPath);
        } else {
          // This is a leaf category, add it to the list
          flattened.add({
            'name': key,
            'path': currentPath,
            'hierarchy': currentPath.join(' > '),
            'size': value.toString(),
          });
        }
      });
    }
    
    _flatten(categories, []);
    return flattened;
  }

  /// Get category hierarchy for a specific category name
  static List<String>? getCategoryHierarchy(String categoryName, Map<String, dynamic> categories) {
    List<String>? _findPath(Map<String, dynamic> data, String target, List<String> currentPath) {
      for (final entry in data.entries) {
        final key = entry.key;
        final value = entry.value;
        final newPath = [...currentPath, key];
        
        if (key == target) {
          return newPath;
        }
        
        if (value is Map<String, dynamic>) {
          final result = _findPath(value, target, newPath);
          if (result != null) {
            return result;
          }
        }
      }
      return null;
    }
    
    return _findPath(categories, categoryName, []);
  }

  /// Clear cached categories (useful for testing or forced refresh)
  static void clearCache() {
    _cachedCategories = null;
    _lastFetch = null;
  }

  /// Get default categories for fallback
  static Map<String, dynamic> getDefaultCategories() {
    return {
      'General': 'medium',
      'Electronics': {
        'Mobile phones & accessories': 'small',
        'Laptops & small gadgets': {
          'Laptops': 'medium',
          'Tablets': 'medium',
          'Smartwatches': 'small'
        }
      },
      'Documents': 'small',
      'Clothing': 'medium',
      'Books': 'small',
      'Food & Beverages': 'medium',
      'Home & Garden': 'large',
      'Sports & Outdoors': 'medium',
      'Automotive': 'large',
      'Health & Beauty': 'small',
    };
  }

  /// Get category display name from hierarchy
  static String getCategoryDisplayName(List<String> hierarchy) {
    if (hierarchy.isEmpty) return 'General';
    return hierarchy.last;
  }

  /// Validate category hierarchy against API data
  static bool isValidCategoryHierarchy(List<String> hierarchy, Map<String, dynamic> categories) {
    if (hierarchy.isEmpty) return false;
    
    Map<String, dynamic> current = categories;
    
    for (int i = 0; i < hierarchy.length; i++) {
      final category = hierarchy[i];
      
      if (!current.containsKey(category)) {
        return false;
      }
      
      final value = current[category];
      
      if (i == hierarchy.length - 1) {
        // Last item, should be either a string or the final category
        return true;
      } else {
        // Not the last item, should be a Map to continue traversal
        if (value is! Map<String, dynamic>) {
          return false;
        }
        current = value;
      }
    }
    
    return true;
  }
}
