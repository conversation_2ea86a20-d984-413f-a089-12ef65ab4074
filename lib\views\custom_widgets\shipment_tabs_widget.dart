import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/current_shipments_widget.dart';
import 'package:rideoon/views/custom_widgets/shipment_card.dart';

/// Tabbed shipment widget with Current Shipment and Shipment History tabs
/// 
/// A reusable widget that provides a clean tabbed interface for viewing
/// current shipments and shipment history with proper state management.
class ShipmentTabsWidget extends StatefulWidget {
  /// Current shipments data
  final List<ShipmentData> currentShipments;
  
  /// Shipment history data
  final List<ShipmentData> shipmentHistory;
  
  /// Callback when a current shipment is tapped
  final Function(ShipmentData shipment)? onCurrentShipmentTap;
  
  /// Callback when a history shipment is tapped
  final Function(ShipmentData shipment)? onHistoryShipmentTap;
  
  /// Custom horizontal padding
  final double? horizontalPadding;
  
  /// Whether to show tracking timeline in current shipments
  final bool showTrackingTimeline;

  const ShipmentTabsWidget({
    super.key,
    required this.currentShipments,
    required this.shipmentHistory,
    this.onCurrentShipmentTap,
    this.onHistoryShipmentTap,
    this.horizontalPadding,
    this.showTrackingTimeline = true,
  });

  @override
  State<ShipmentTabsWidget> createState() => _ShipmentTabsWidgetState();
}

class _ShipmentTabsWidgetState extends State<ShipmentTabsWidget> {
  int _selectedTabIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: widget.horizontalPadding ?? _getHorizontalPadding(context),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tab buttons
          _buildTabButtons(context),
          
          SizedBox(height: _getSpacing(context, 24)),
          
          // Tab content
          _buildTabContent(context),
        ],
      ),
    );
  }

  Widget _buildTabButtons(BuildContext context) {
    return Row(
      children: [
        // Current Shipment tab
        Expanded(
          child: _buildTabButton(
            context,
            title: 'Current Shipment',
            isSelected: _selectedTabIndex == 0,
            onTap: () => setState(() => _selectedTabIndex = 0),
          ),
        ),
        
        SizedBox(width: _getSpacing(context, 8)),
        
        // Shipment History tab
        Expanded(
          child: _buildTabButton(
            context,
            title: 'Shipment History',
            isSelected: _selectedTabIndex == 1,
            onTap: () => setState(() => _selectedTabIndex = 1),
          ),
        ),
      ],
    );
  }

  Widget _buildTabButton(
    BuildContext context, {
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: _getSpacing(context, 15)),
        decoration: ShapeDecoration(
          color: isSelected ? AppColors.primary : AppColors.white,
          shape: RoundedRectangleBorder(
            side: BorderSide(
              width: 1,
              color: isSelected 
                  ? AppColors.primary 
                  : const Color(0x0A1E1E1E),
            ),
            borderRadius: BorderRadius.circular(_getBorderRadius(context, 18)),
          ),
        ),
        child: Center(
          child: Text(
            title,
            style: TextStyle(
              color: isSelected ? AppColors.white : const Color(0xFF111111),
              fontSize: _getFontSize(context, 12),
              fontFamily: 'Inter',
              fontWeight: FontWeight.w400,
              height: 2.09,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabContent(BuildContext context) {
    switch (_selectedTabIndex) {
      case 0:
        return _buildCurrentShipmentsTab(context);
      case 1:
        return _buildShipmentHistoryTab(context);
      default:
        return _buildCurrentShipmentsTab(context);
    }
  }

  Widget _buildCurrentShipmentsTab(BuildContext context) {
    if (widget.currentShipments.isEmpty) {
      return _buildEmptyState(
        context,
        title: 'Currently Empty',
        subtitle: 'No current shipments available',
        icon: Icons.inventory_2_outlined,
      );
    }

    return CurrentShipmentsWidget(
      title: '', // No title since we have tabs
      shipments: widget.currentShipments,
      showSeeAll: false, // No "See all" in tab view
      showTrackingTimeline: widget.showTrackingTimeline,
      horizontalPadding: 0, // Already handled by parent
      onShipmentTap: widget.onCurrentShipmentTap,
    );
  }

  Widget _buildShipmentHistoryTab(BuildContext context) {
    if (widget.shipmentHistory.isEmpty) {
      return _buildEmptyState(
        context,
        title: 'No History',
        subtitle: 'No shipment history available',
        icon: Icons.history,
      );
    }

    return Column(
      children: widget.shipmentHistory.map((shipment) {
        return Padding(
          padding: EdgeInsets.only(bottom: _getSpacing(context, 16)),
          child: ShipmentCard(
            shipment: shipment,
            showTrackingTimeline: false, // History doesn't need timeline
            onCardTap: widget.onHistoryShipmentTap != null
                ? () => widget.onHistoryShipmentTap!(shipment)
                : null,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildEmptyState(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
  }) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 40)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Empty state illustration placeholder
          Container(
            width: _getSpacing(context, 149),
            height: _getSpacing(context, 155),
            decoration: BoxDecoration(
              color: AppColors.black.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
            ),
            child: Icon(
              icon,
              size: _getSpacing(context, 48),
              color: AppColors.black.withValues(alpha: 0.3),
            ),
          ),
          
          SizedBox(height: _getSpacing(context, 24)),
          
          Text(
            title,
            style: TextStyle(
              color: AppColors.black.withValues(alpha: 0.74),
              fontSize: _getFontSize(context, 16),
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
              height: 0.95,
              letterSpacing: -0.80,
            ),
          ),
          
          SizedBox(height: _getSpacing(context, 8)),
          
          Text(
            subtitle,
            style: TextStyle(
              color: AppColors.black.withValues(alpha: 0.5),
              fontSize: _getFontSize(context, 14),
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16;
    } else if (screenWidth > 600) {
      basePadding = 40;
    } else {
      basePadding = 24;
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8;
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2;
    } else {
      fontSize = baseFontSize;
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6;
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2;
    } else {
      borderRadius = baseBorderRadius;
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }
}
