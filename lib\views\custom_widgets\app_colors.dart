import 'package:flutter/material.dart';

/// Main color palette for the RideOn app
class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF9713E7);
  static const Color primaryLight = Color(0xff9713e7b8); // track order banner
  static const Color primaryLightest = Color(0xff9713e721); // signout

  // Status Colors
  static const Color success = Color(0xFF13E78F); // completed
  static const Color warning = Color(0xFFFFC300); // in_progress
  static const Color pending = Color(0xFFFF9D41); // onprogress
  static const Color error = Color(0xFFFF0000); // delete actions
  static const Color accept = Color(0xFF14AE5C); // rider accept
  static const Color rating = Color(0xFFFDD835); // ratings

  // Base Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
}

/// Light mode color scheme
class LightModeScheme {
  static const ColorScheme scheme = ColorScheme(
    primary: AppColors.primary,
    secondary: AppColors.success,
    surface: AppColors.white,
    error: AppColors.error,
    onPrimary: AppColors.white,
    onSecondary: AppColors.black,
    onSurface: AppColors.black,
    onError: AppColors.white,
    brightness: Brightness.light,
  );
}

/// Dark mode color scheme
class DarkModeScheme {
  static const ColorScheme scheme = ColorScheme(
    primary: AppColors.primary,
    secondary: AppColors.success,
    surface: Color(0xFF121212), // Material dark background
    error: AppColors.error,
    onPrimary: AppColors.white,
    onSecondary: AppColors.white,
    onSurface: AppColors.white,
    onError: AppColors.white,
    brightness: Brightness.dark,
  );
}

/// High Contrast mode color scheme for accessibility
class HighContrastScheme {
  static const ColorScheme scheme = ColorScheme(
    primary: Color(0xFF8000FF), // Higher contrast purple
    secondary: Color(0xFF00CC66), // Higher contrast green
    surface: AppColors.black,
    error: Color(0xFFFF0000),
    onPrimary: AppColors.white,
    onSecondary: AppColors.white,
    onSurface: AppColors.white,
    onError: AppColors.white,
    brightness: Brightness.dark,
  );
}

/// Extension methods for easy access to status colors
extension StatusColors on ColorScheme {
  Color get completed => AppColors.success;
  Color get inProgress => AppColors.warning;
  Color get onProgress => AppColors.pending;
  Color get delete => AppColors.error;
  Color get riderAccept => AppColors.accept;
  Color get rating => AppColors.rating;
  Color get trackOrderBanner => AppColors.primaryLight;
  Color get signOut => AppColors.primaryLightest;
}