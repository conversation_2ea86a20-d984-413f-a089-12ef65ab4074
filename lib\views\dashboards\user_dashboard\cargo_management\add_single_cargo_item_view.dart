import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/add_cargo.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/providers/order_provider.dart';
import 'package:rideoon/providers/package_provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

/// Add Single Cargo Item view page for adding or editing individual cargo items
///
/// This page provides a full-screen interface for adding or editing a single cargo item
/// with comprehensive form fields, image support, and validation.
///
/// Features:
/// - Add or edit individual cargo items
/// - Image picker with multiple image support
/// - Category and durability selection
/// - Form validation and error handling
/// - Responsive design for mobile, tablet, and smartwatch
/// - Consistent styling with app theme
class AddSingleCargoItemView extends StatefulWidget {
  const AddSingleCargoItemView({super.key});

  @override
  State<AddSingleCargoItemView> createState() => _AddSingleCargoItemViewState();
}

class _AddSingleCargoItemViewState extends State<AddSingleCargoItemView> {
  final _formKey = GlobalKey<FormState>();
  final ImagePicker _picker = ImagePicker();

  // Form controllers
  final TextEditingController _itemNameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _itemTypeController = TextEditingController();
  final TextEditingController _weightController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _valueController = TextEditingController();

  // Form state
  String _selectedCategory = 'General';
  ItemDurability _selectedDurability = ItemDurability.average;
  List<String> _selectedImages = [];
  bool _isLoading = false;

  // Existing item data (for editing)
  CargoItem? _existingItem;
  int _itemIndex = -1;

  // Available categories for cargo items
  static const List<String> _availableCategories = [
    'Electronics',
    'Computer Accessories',
    'Clothing & Fashion',
    'Books & Documents',
    'Food & Beverages',
    'Home & Garden',
    'Sports & Outdoors',
    'Health & Beauty',
    'Toys & Games',
    'Automotive',
    'Jewelry & Accessories',
    'Art & Crafts',
    'Musical Instruments',
    'Office Supplies',
    'General',
  ];

  @override
  void initState() {
    super.initState();
    // Get arguments passed from previous screen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
      if (args != null) {
        _existingItem = args['existingItem'] as CargoItem?;
        _itemIndex = args['itemIndex'] as int? ?? -1;
        
        if (_existingItem != null) {
          _populateFormWithExistingData();
        }
      }
    });
  }

  void _populateFormWithExistingData() {
    if (_existingItem != null) {
      setState(() {
        _itemNameController.text = _existingItem!.itemName;
        _descriptionController.text = _existingItem!.description ?? '';
        _itemTypeController.text = _existingItem!.itemType;
        _weightController.text = _existingItem!.weight.toString();
        _quantityController.text = _existingItem!.quantity.toString();
        _valueController.text = _existingItem!.value?.toString() ?? '';
        _selectedCategory = _existingItem!.category;
        _selectedDurability = _existingItem!.durability;
        _selectedImages = List<String>.from(_existingItem!.imagePaths);
      });
    }
  }

  @override
  void dispose() {
    _itemNameController.dispose();
    _descriptionController.dispose();
    _itemTypeController.dispose();
    _weightController.dispose();
    _quantityController.dispose();
    _valueController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(context),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(_getHorizontalPadding(context)),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: _getSpacing(context, 24)),

                      // Item Name
                      _buildFormField(
                        'Item Name',
                        _itemNameController,
                        'Enter item name',
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter item name';
                          }
                          return null;
                        },
                      ),

                      SizedBox(height: _getSpacing(context, 20)),

                      // Description
                      _buildFormField(
                        'Description (Optional)',
                        _descriptionController,
                        'Enter item description',
                        maxLines: 3,
                      ),

                      SizedBox(height: _getSpacing(context, 20)),

                      // Category
                      _buildCategoryField(),

                      SizedBox(height: _getSpacing(context, 20)),

                      // Item Type
                      _buildFormField(
                        'Item Type',
                        _itemTypeController,
                        'Enter item type',
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter item type';
                          }
                          return null;
                        },
                      ),

                      SizedBox(height: _getSpacing(context, 20)),

                      // Weight and Quantity in a row
                      Row(
                        children: [
                          Expanded(
                            child: _buildFormField(
                              'Weight (kg)',
                              _weightController,
                              '0.0',
                              keyboardType: TextInputType.numberWithOptions(decimal: true),
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'Enter weight';
                                }
                                final weight = double.tryParse(value);
                                if (weight == null || weight <= 0) {
                                  return 'Enter valid weight';
                                }
                                return null;
                              },
                            ),
                          ),
                          SizedBox(width: _getSpacing(context, 16)),
                          Expanded(
                            child: _buildFormField(
                              'Quantity',
                              _quantityController,
                              '1',
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'Enter quantity';
                                }
                                final quantity = int.tryParse(value);
                                if (quantity == null || quantity <= 0) {
                                  return 'Enter valid quantity';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: _getSpacing(context, 20)),

                      // Value (Optional)
                      _buildFormField(
                        'Value (₦) - Optional',
                        _valueController,
                        'Enter item value for insurance',
                        keyboardType: TextInputType.numberWithOptions(decimal: true),
                        validator: (value) {
                          if (value != null && value.trim().isNotEmpty) {
                            final val = double.tryParse(value);
                            if (val == null || val < 0) {
                              return 'Enter valid value';
                            }
                          }
                          return null;
                        },
                      ),

                      SizedBox(height: _getSpacing(context, 20)),

                      // Durability
                      _buildDurabilityField(),

                      SizedBox(height: _getSpacing(context, 20)),

                      // Images section
                      _buildImagesSection(),

                      SizedBox(height: _getSpacing(context, 100)), // Extra space for bottom buttons
                    ],
                  ),
                ),
              ),
            ),

            // Bottom Action Buttons
            _buildBottomActions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(_getHorizontalPadding(context)),
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Back button
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              width: _getIconSize(context, 40),
              height: _getIconSize(context, 40),
              decoration: BoxDecoration(
                color: AppColors.black.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
              ),
              child: Icon(
                Icons.arrow_back_ios_new,
                size: _getIconSize(context, 20),
                color: AppColors.black,
              ),
            ),
          ),
          SizedBox(width: _getSpacing(context, 16)),

          // Title
          Expanded(
            child: Text(
              _existingItem != null ? 'Edit Cargo Item' : 'Add Cargo Item',
              style: TextStyle(
                color: AppColors.black,
                fontSize: _getTitleFontSize(context),
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w600,
                letterSpacing: -0.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormField(
    String label,
    TextEditingController controller,
    String hintText, {
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    int? maxLines,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: AppColors.black,
            fontSize: _getBodyFontSize(context) + 1,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: _getSpacing(context, 8)),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          validator: validator,
          maxLines: maxLines ?? 1,
          style: TextStyle(
            color: AppColors.black,
            fontSize: _getBodyFontSize(context),
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w400,
          ),
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: TextStyle(
              color: AppColors.black.withValues(alpha: 0.5),
              fontSize: _getBodyFontSize(context),
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w400,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
              borderSide: BorderSide(
                color: AppColors.black.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
              borderSide: BorderSide(
                color: AppColors.primary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
              borderSide: BorderSide(
                color: Colors.red,
                width: 1,
              ),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: _getSpacing(context, 16),
              vertical: _getSpacing(context, 12),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomActions(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(_getHorizontalPadding(context)),
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Cancel button
          Expanded(
            child: OutlinedButton(
              onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
              style: OutlinedButton.styleFrom(
                backgroundColor: AppColors.white,
                foregroundColor: AppColors.black.withValues(alpha: 0.7),
                side: BorderSide(
                  width: 1.5,
                  color: AppColors.black.withValues(alpha: 0.2),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
                ),
                minimumSize: Size(0, _getButtonHeight(context)),
              ),
              child: Text(
                'Cancel',
                style: TextStyle(
                  fontSize: _getButtonFontSize(context),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),

          SizedBox(width: _getSpacing(context, 12)),

          // Save button
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _handleSaveItem,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
                ),
                minimumSize: Size(0, _getButtonHeight(context)),
                elevation: 0,
              ),
              child: _isLoading
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      _existingItem != null ? 'Update Item' : 'Save Item',
                      style: TextStyle(
                        fontSize: _getButtonFontSize(context),
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  // Event handlers
  void _handleSaveItem() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final itemName = _itemNameController.text.trim();
      final itemType = _itemTypeController.text.trim();
      final weight = double.parse(_weightController.text.trim());
      final quantity = int.parse(_quantityController.text.trim());

      final newItem = CargoItem(
        itemName: itemName,
        category: _selectedCategory,
        itemType: itemType,
        weight: weight,
        quantity: quantity,
        durability: _selectedDurability,
        imagePaths: _selectedImages,
        canEdit: true,
        canDelete: true,
      );

      // Return the item data to the previous screen
      Navigator.of(context).pop({
        'item': newItem,
        'index': _itemIndex,
        'isEdit': _existingItem != null,
      });

    } catch (e) {
      Toast.error('Failed to save item. Please check your input.');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _handleAddImage() async {
    if (_selectedImages.length >= 5) {
      Toast.warning('Maximum 5 images allowed');
      return;
    }

    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80,
      );

      if (image != null) {
        setState(() {
          _selectedImages.add(image.path);
        });
        Toast.success('Image added successfully');
      }
    } catch (e) {
      Toast.error('Failed to pick image');
    }
  }

  void _handleRemoveImage(int index) {
    if (index < _selectedImages.length) {
      setState(() {
        _selectedImages.removeAt(index);
      });
      Toast.success('Image removed');
    }
  }

  void _showCategoryBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                margin: EdgeInsets.only(top: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: AppColors.black.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: EdgeInsets.all(20),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        'Select Category',
                        style: TextStyle(
                          color: AppColors.black,
                          fontSize: 18,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Icon(
                        Icons.close,
                        color: AppColors.black.withValues(alpha: 0.6),
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ),

              // Category options
              ...(_availableCategories.map((category) {
                final isSelected = category == _selectedCategory;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedCategory = category;
                    });
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    decoration: BoxDecoration(
                      color: isSelected ? AppColors.primary.withValues(alpha: 0.1) : Colors.transparent,
                    ),
                    child: Text(
                      category,
                      style: TextStyle(
                        color: isSelected ? AppColors.primary : AppColors.black,
                        fontSize: 16,
                        fontFamily: 'Poppins',
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                      ),
                    ),
                  ),
                );
              })),

              SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  void _showDurabilityBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                margin: EdgeInsets.only(top: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: AppColors.black.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: EdgeInsets.all(20),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        'Select Durability',
                        style: TextStyle(
                          color: AppColors.black,
                          fontSize: 18,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Icon(
                        Icons.close,
                        color: AppColors.black.withValues(alpha: 0.6),
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ),

              // Durability options
              ...(ItemDurability.values.map((durability) {
                final isSelected = durability == _selectedDurability;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedDurability = durability;
                    });
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    decoration: BoxDecoration(
                      color: isSelected ? AppColors.primary.withValues(alpha: 0.1) : Colors.transparent,
                    ),
                    child: Text(
                      durability.displayName,
                      style: TextStyle(
                        color: isSelected ? AppColors.primary : AppColors.black,
                        fontSize: 16,
                        fontFamily: 'Poppins',
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                      ),
                    ),
                  ),
                );
              })),

              SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCategoryField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Category',
          style: TextStyle(
            color: AppColors.black,
            fontSize: _getBodyFontSize(context) + 1,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: _getSpacing(context, 8)),
        GestureDetector(
          onTap: _showCategoryBottomSheet,
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
              horizontal: _getSpacing(context, 16),
              vertical: _getSpacing(context, 12),
            ),
            decoration: BoxDecoration(
              border: Border.all(
                color: AppColors.black.withValues(alpha: 0.2),
                width: 1,
              ),
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _selectedCategory,
                  style: TextStyle(
                    color: AppColors.black,
                    fontSize: _getBodyFontSize(context),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w400,
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: AppColors.black.withValues(alpha: 0.6),
                  size: _getIconSize(context, 20),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDurabilityField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Durability',
          style: TextStyle(
            color: AppColors.black,
            fontSize: _getBodyFontSize(context) + 1,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: _getSpacing(context, 8)),
        GestureDetector(
          onTap: _showDurabilityBottomSheet,
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
              horizontal: _getSpacing(context, 16),
              vertical: _getSpacing(context, 12),
            ),
            decoration: BoxDecoration(
              border: Border.all(
                color: AppColors.black.withValues(alpha: 0.2),
                width: 1,
              ),
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _selectedDurability.displayName,
                  style: TextStyle(
                    color: AppColors.black,
                    fontSize: _getBodyFontSize(context),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w400,
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: AppColors.black.withValues(alpha: 0.6),
                  size: _getIconSize(context, 20),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImagesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Images (Optional)',
              style: TextStyle(
                color: AppColors.black,
                fontSize: _getBodyFontSize(context) + 1,
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w600,
              ),
            ),
            if (_selectedImages.length < 5)
              GestureDetector(
                onTap: _handleAddImage,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: _getSpacing(context, 12),
                    vertical: _getSpacing(context, 6),
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.add_photo_alternate,
                        color: AppColors.white,
                        size: _getIconSize(context, 16),
                      ),
                      SizedBox(width: _getSpacing(context, 4)),
                      Text(
                        'Add Photo',
                        style: TextStyle(
                          color: AppColors.white,
                          fontSize: _getBodyFontSize(context) - 1,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),

        SizedBox(height: _getSpacing(context, 12)),

        // Images preview
        if (_selectedImages.isNotEmpty) ...[
          Container(
            height: _getSpacing(context, 100),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _selectedImages.length,
              itemBuilder: (context, index) {
                return Container(
                  margin: EdgeInsets.only(right: _getSpacing(context, 12)),
                  child: Stack(
                    children: [
                      // Image
                      Container(
                        width: _getSpacing(context, 100),
                        height: _getSpacing(context, 100),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
                          border: Border.all(
                            color: AppColors.black.withValues(alpha: 0.1),
                            width: 1,
                          ),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
                          child: Image.file(
                            File(_selectedImages[index]),
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: AppColors.black.withValues(alpha: 0.1),
                                child: Icon(
                                  Icons.broken_image,
                                  color: AppColors.black.withValues(alpha: 0.5),
                                  size: _getIconSize(context, 32),
                                ),
                              );
                            },
                          ),
                        ),
                      ),

                      // Remove button
                      Positioned(
                        top: _getSpacing(context, 4),
                        right: _getSpacing(context, 4),
                        child: GestureDetector(
                          onTap: () => _handleRemoveImage(index),
                          child: Container(
                            width: _getIconSize(context, 24),
                            height: _getIconSize(context, 24),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
                            ),
                            child: Icon(
                              Icons.close,
                              color: AppColors.white,
                              size: _getIconSize(context, 16),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ] else ...[
          // Empty state
          Container(
            width: double.infinity,
            height: _getSpacing(context, 100),
            decoration: BoxDecoration(
              color: AppColors.black.withValues(alpha: 0.02),
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
              border: Border.all(
                color: AppColors.black.withValues(alpha: 0.1),
                width: 1,
                style: BorderStyle.solid,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.add_photo_alternate_outlined,
                  color: AppColors.black.withValues(alpha: 0.4),
                  size: _getIconSize(context, 32),
                ),
                SizedBox(height: _getSpacing(context, 8)),
                Text(
                  'Tap "Add Photo" to include images',
                  style: TextStyle(
                    color: AppColors.black.withValues(alpha: 0.5),
                    fontSize: _getBodyFontSize(context) - 1,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
        ],

        if (_selectedImages.isNotEmpty) ...[
          SizedBox(height: _getSpacing(context, 8)),
          Text(
            '${_selectedImages.length}/5 images added',
            style: TextStyle(
              color: AppColors.black.withValues(alpha: 0.6),
              fontSize: _getBodyFontSize(context) - 1,
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ],
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 16;
    if (screenWidth > 600) return 40;
    return 24;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getBorderRadius(BuildContext context, double baseRadius) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return baseRadius * 0.8;
    } else if (screenWidth > 600) {
      return baseRadius * 1.2;
    } else {
      return baseRadius;
    }
  }

  double _getButtonBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 6;
    } else if (screenWidth > 600) {
      return 10;
    } else {
      return 8;
    }
  }

  double _getIconSize(BuildContext context, double baseSize) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return baseSize * 0.8;
    } else if (screenWidth > 600) {
      return baseSize * 1.2;
    } else {
      return baseSize;
    }
  }

  double _getTitleFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 16;
    } else if (screenWidth > 600) {
      baseSize = 24;
    } else {
      baseSize = 20;
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getBodyFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10;
    } else if (screenWidth > 600) {
      baseSize = 16;
    } else {
      baseSize = 14;
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getButtonFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 12;
    } else if (screenWidth > 600) {
      baseSize = 18;
    } else {
      baseSize = 16;
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getButtonHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseHeight;
    if (screenWidth < 300) {
      baseHeight = 40;
    } else if (screenWidth > 600) {
      baseHeight = 56;
    } else {
      baseHeight = 48;
    }

    if (isShortScreen) {
      baseHeight = baseHeight * 0.9;
    }

    return baseHeight;
  }
}
