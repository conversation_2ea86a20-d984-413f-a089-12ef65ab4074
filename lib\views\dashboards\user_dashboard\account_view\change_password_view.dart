import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/providers/toast_provider.dart';

/// Change Password view for user account security
class ChangePasswordView extends StatefulWidget {
  const ChangePasswordView({super.key});

  @override
  State<ChangePasswordView> createState() => _ChangePasswordViewState();
}

class _ChangePasswordViewState extends State<ChangePasswordView> {
  final _formKey = GlobalKey<FormState>();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _isLoading = false;
  bool _obscureCurrentPassword = true;
  bool _obscureNewPassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5FF),
      appBar: _buildAppBar(context),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(_getHorizontalPadding(context)),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: _getSpacing(context, 20)),

                // Security info section
                _buildSecurityInfoSection(context),

                SizedBox(height: _getSpacing(context, 32)),

                // Password form
                _buildPasswordForm(context),

                SizedBox(height: _getSpacing(context, 32)),

                // Change password button
                _buildChangePasswordButton(context),

                SizedBox(height: _getSpacing(context, 20)),
              ],
            ),
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: const Color(0xFFF5F5FF),
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: AppColors.black,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        'Change Password',
        style: TextStyle(
          fontSize: _getFontSize(context, 20),
          fontWeight: FontWeight.bold,
          fontFamily: 'Poppins',
          color: AppColors.black,
        ),
      ),
    );
  }

  Widget _buildSecurityInfoSection(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 20)),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withValues(alpha: 0.1),
            AppColors.primary.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 16)),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.security,
            size: _getIconSize(context, 48),
            color: AppColors.primary,
          ),
          SizedBox(height: _getSpacing(context, 16)),
          Text(
            'Password Security',
            style: TextStyle(
              fontSize: _getFontSize(context, 20),
              fontWeight: FontWeight.bold,
              fontFamily: 'Poppins',
              color: AppColors.black,
            ),
          ),
          SizedBox(height: _getSpacing(context, 8)),
          Text(
            'Keep your account secure by using a strong password. Your password should be at least 8 characters long and include a mix of letters, numbers, and symbols.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: _getFontSize(context, 14),
              fontFamily: 'Poppins',
              color: AppColors.black.withValues(alpha: 0.7),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordForm(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Password Information',
          style: TextStyle(
            fontSize: _getFontSize(context, 18),
            fontWeight: FontWeight.bold,
            fontFamily: 'Poppins',
            color: AppColors.black,
          ),
        ),
        SizedBox(height: _getSpacing(context, 16)),

        // Current Password
        _buildPasswordField(
          context,
          controller: _currentPasswordController,
          label: 'Current Password',
          obscureText: _obscureCurrentPassword,
          onToggleVisibility: () {
            setState(() {
              _obscureCurrentPassword = !_obscureCurrentPassword;
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your current password';
            }
            return null;
          },
        ),

        SizedBox(height: _getSpacing(context, 16)),

        // New Password
        _buildPasswordField(
          context,
          controller: _newPasswordController,
          label: 'New Password',
          obscureText: _obscureNewPassword,
          onToggleVisibility: () {
            setState(() {
              _obscureNewPassword = !_obscureNewPassword;
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter a new password';
            }
            if (value.length < 8) {
              return 'Password must be at least 8 characters long';
            }
            if (value == _currentPasswordController.text) {
              return 'New password must be different from current password';
            }
            return null;
          },
        ),

        SizedBox(height: _getSpacing(context, 16)),

        // Confirm Password
        _buildPasswordField(
          context,
          controller: _confirmPasswordController,
          label: 'Confirm New Password',
          obscureText: _obscureConfirmPassword,
          onToggleVisibility: () {
            setState(() {
              _obscureConfirmPassword = !_obscureConfirmPassword;
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please confirm your new password';
            }
            if (value != _newPasswordController.text) {
              return 'Passwords do not match';
            }
            return null;
          },
        ),

        SizedBox(height: _getSpacing(context, 16)),

        // Password requirements
        _buildPasswordRequirements(context),
      ],
    );
  }

  Widget _buildPasswordField(
    BuildContext context, {
    required TextEditingController controller,
    required String label,
    required bool obscureText,
    required VoidCallback onToggleVisibility,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: obscureText,
      validator: validator,
      style: TextStyle(
        fontSize: _getFontSize(context, 16),
        fontFamily: 'Poppins',
        color: AppColors.black,
      ),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(
          fontSize: _getFontSize(context, 14),
          fontFamily: 'Poppins',
          color: AppColors.black.withValues(alpha: 0.6),
        ),
        prefixIcon: Icon(
          Icons.lock_outline,
          size: _getIconSize(context, 20),
          color: AppColors.primary,
        ),
        suffixIcon: IconButton(
          icon: Icon(
            obscureText ? Icons.visibility_off : Icons.visibility,
            size: _getIconSize(context, 20),
            color: AppColors.black.withValues(alpha: 0.6),
          ),
          onPressed: onToggleVisibility,
        ),
        filled: true,
        fillColor: AppColors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
          borderSide: BorderSide(
            color: AppColors.black.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
          borderSide: BorderSide(
            color: AppColors.black.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
          borderSide: BorderSide(
            color: AppColors.primary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
          borderSide: BorderSide(
            color: AppColors.error,
            width: 1,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
          borderSide: BorderSide(
            color: AppColors.error,
            width: 2,
          ),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: _getSpacing(context, 16),
          vertical: _getSpacing(context, 16),
        ),
      ),
    );
  }

  Widget _buildPasswordRequirements(BuildContext context) {
    final newPassword = _newPasswordController.text;
    final hasMinLength = newPassword.length >= 8;
    final hasUppercase = newPassword.contains(RegExp(r'[A-Z]'));
    final hasLowercase = newPassword.contains(RegExp(r'[a-z]'));
    final hasNumbers = newPassword.contains(RegExp(r'[0-9]'));
    final hasSpecialChars = newPassword.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));

    return Container(
      padding: EdgeInsets.all(_getSpacing(context, 16)),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
        border: Border.all(
          color: AppColors.black.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Password Requirements:',
            style: TextStyle(
              fontSize: _getFontSize(context, 14),
              fontWeight: FontWeight.w600,
              fontFamily: 'Poppins',
              color: AppColors.black,
            ),
          ),
          SizedBox(height: _getSpacing(context, 8)),
          _buildRequirementItem(context, 'At least 8 characters', hasMinLength),
          _buildRequirementItem(context, 'Uppercase letter (A-Z)', hasUppercase),
          _buildRequirementItem(context, 'Lowercase letter (a-z)', hasLowercase),
          _buildRequirementItem(context, 'Number (0-9)', hasNumbers),
          _buildRequirementItem(context, 'Special character (!@#\$%^&*)', hasSpecialChars),
        ],
      ),
    );
  }

  Widget _buildRequirementItem(BuildContext context, String requirement, bool isMet) {
    return Padding(
      padding: EdgeInsets.only(bottom: _getSpacing(context, 4)),
      child: Row(
        children: [
          Icon(
            isMet ? Icons.check_circle : Icons.radio_button_unchecked,
            size: _getIconSize(context, 16),
            color: isMet ? AppColors.success : AppColors.black.withValues(alpha: 0.4),
          ),
          SizedBox(width: _getSpacing(context, 8)),
          Text(
            requirement,
            style: TextStyle(
              fontSize: _getFontSize(context, 12),
              fontFamily: 'Poppins',
              color: isMet ? AppColors.success : AppColors.black.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChangePasswordButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : () => _changePassword(context),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
          padding: EdgeInsets.symmetric(
            vertical: _getSpacing(context, 16),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
          ),
          elevation: 0,
        ),
        child: _isLoading
            ? SizedBox(
                width: _getIconSize(context, 20),
                height: _getIconSize(context, 20),
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                ),
              )
            : Text(
                'Change Password',
                style: TextStyle(
                  fontSize: _getFontSize(context, 16),
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Poppins',
                ),
              ),
      ),
    );
  }

  void _changePassword(BuildContext context) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isLoading = false;
    });

    Toast.success('Password changed successfully');
    Navigator.pop(context);
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16;
    } else if (screenWidth > 600) {
      basePadding = 40;
    } else {
      basePadding = 24;
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8;
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2;
    } else {
      fontSize = baseFontSize;
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8;
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2;
    } else {
      iconSize = baseIconSize;
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6;
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2;
    } else {
      borderRadius = baseBorderRadius;
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }
}
