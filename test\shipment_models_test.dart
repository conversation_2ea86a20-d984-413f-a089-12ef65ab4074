import 'package:flutter_test/flutter_test.dart';
import 'package:rideoon/models/core/address.dart';
import 'package:rideoon/models/core/contact.dart';
import 'package:rideoon/models/shipment/package.dart';
import 'package:rideoon/models/shipment/shipment_status.dart';
import 'package:rideoon/models/shipment/tracking.dart';
import 'package:rideoon/models/shipment/shipment.dart';
import 'package:rideoon/models/payment/payment.dart';

void main() {
  group('Shipment Models Tests', () {
    test('Address model should work correctly', () {
      final address = Address(
        street: '123 Main Street',
        landmark: 'Near Central Mall',
        city: 'Lagos',
        state: 'Lagos',
        postalCode: '100001',
        country: 'Nigeria',
        latitude: 6.5244,
        longitude: 3.3792,
      );

      expect(address.fullAddress, contains('123 Main Street'));
      expect(address.fullAddress, contains('Lagos'));
      expect(address.shortAddress, equals('123 Main Street, Lagos, Lagos'));
      expect(address.hasCoordinates, isTrue);

      final json = address.toJson();
      final fromJson = Address.from<PERSON>son(json);
      expect(fromJson.street, equals(address.street));
      expect(fromJson.city, equals(address.city));
    });

    test('Contact model should work correctly', () {
      final contact = Contact(
        name: '<PERSON>e',
        phoneNumber: '08012345678',
        email: '<EMAIL>',
        alternatePhone: '08087654321',
      );

      expect(contact.displayName, equals('John'));
      expect(contact.initials, equals('JD'));
      expect(contact.formattedPhone, equals('+2348012345678'));
      expect(contact.hasEmail, isTrue);
      expect(contact.hasAlternatePhone, isTrue);

      final json = contact.toJson();
      final fromJson = Contact.fromJson(json);
      expect(fromJson.name, equals(contact.name));
      expect(fromJson.phoneNumber, equals(contact.phoneNumber));
    });

    test('Package model should work correctly', () {
      final package = Package(
        itemName: 'Laptop',
        description: 'Gaming laptop',
        category: PackageCategory.electronics,
        itemType: 'Computer',
        weight: 2.5,
        quantity: 2,
        durability: PackageDurability.fragile,
        deliveryType: DeliveryType.express,
        imagePaths: ['image1.jpg', 'image2.jpg'],
        value: 500000.0,
      );

      expect(package.totalWeight, equals(5.0)); // 2.5 * 2
      expect(package.hasImages, isTrue);
      expect(package.hasValue, isTrue);
      expect(package.summary, contains('Laptop'));
      expect(package.summary, contains('5.0kg'));

      final json = package.toJson();
      final fromJson = Package.fromJson(json);
      expect(fromJson.itemName, equals(package.itemName));
      expect(fromJson.category, equals(package.category));
    });

    test('ShipmentStatus should have correct properties', () {
      expect(ShipmentStatus.pending.isInProgress, isFalse);
      expect(ShipmentStatus.inTransit.isInProgress, isTrue);
      expect(ShipmentStatus.delivered.isCompleted, isTrue);
      expect(ShipmentStatus.cancelled.hasIssues, isTrue);
      expect(ShipmentStatus.pending.canBeCancelled, isTrue);
      expect(ShipmentStatus.delivered.canBeCancelled, isFalse);
      expect(ShipmentStatus.inTransit.canBeTracked, isTrue);
    });

    test('TrackingEvent should work correctly', () {
      final event = TrackingEvent(
        id: 'EVT001',
        status: ShipmentStatus.pickedUp,
        title: 'Package Picked Up',
        description: 'Package picked up from sender',
        timestamp: DateTime.now().subtract(Duration(hours: 2)),
        location: Address(
          street: '123 Main Street',
          city: 'Lagos',
          state: 'Lagos',
        ),
        driverName: 'Mike Johnson',
        driverPhone: '08011111111',
      );

      expect(event.hasLocation, isTrue);
      expect(event.hasDriverInfo, isTrue);
      expect(event.formattedTime, contains('hour'));

      final json = event.toJson();
      final fromJson = TrackingEvent.fromJson(json);
      expect(fromJson.title, equals(event.title));
      expect(fromJson.status, equals(event.status));
    });

    test('PaymentBreakdown should calculate correctly', () {
      final breakdown = PaymentBreakdown(
        baseShippingCost: 5000.0,
        vat: 750.0,
        insurance: 500.0,
        pickupCharge: 1000.0,
        deliveryCharge: 1500.0,
        serviceFee: 250.0,
        discount: 500.0,
        isInsuranceFree: true,
        isPickupFree: true,
        isDeliveryFree: false,
      );

      expect(breakdown.subtotal, equals(6500.0)); // 5000 + 0 + 1500 (pickup free)
      expect(breakdown.totalInsurance, equals(0.0)); // Free
      expect(breakdown.totalBeforeDiscount, equals(7500.0)); // 6500 + 750 + 0 + 250
      expect(breakdown.total, equals(7000.0)); // 7500 - 500
      expect(breakdown.totalSavings, equals(2000.0)); // 500 + 1000 + 0 + 500

      final json = breakdown.toJson();
      final fromJson = PaymentBreakdown.fromJson(json);
      expect(fromJson.total, equals(breakdown.total));
    });

    test('Shipment model should work correctly', () {
      final shipment = Shipment(
        id: 'SHP001',
        trackingNumber: 'RO123456789',
        sender: Contact(
          name: 'John Doe',
          phoneNumber: '08012345678',
        ),
        pickupAddress: Address(
          street: '123 Main Street',
          city: 'Lagos',
          state: 'Lagos',
        ),
        receiver: Contact(
          name: 'Jane Smith',
          phoneNumber: '08087654321',
        ),
        deliveryAddress: Address(
          street: '456 Oak Avenue',
          city: 'Abuja',
          state: 'FCT',
        ),
        packages: [
          Package(
            itemName: 'Laptop',
            category: PackageCategory.electronics,
            itemType: 'Computer',
            weight: 2.5,
            durability: PackageDurability.fragile,
            deliveryType: DeliveryType.express,
          ),
          Package(
            itemName: 'Mouse',
            category: PackageCategory.computerAccessories,
            itemType: 'Accessory',
            weight: 0.2,
            durability: PackageDurability.durable,
            deliveryType: DeliveryType.express,
          ),
        ],
        status: ShipmentStatus.pending,
        priority: ShipmentPriority.high,
        deliveryType: DeliveryType.express,
        createdAt: DateTime.now(),
      );

      expect(shipment.totalWeight, equals(2.7)); // 2.5 + 0.2
      expect(shipment.totalItems, equals(2));
      expect(shipment.title, equals('2 items'));
      expect(shipment.summary, contains('2 items'));
      expect(shipment.summary, contains('2.7kg'));
      expect(shipment.canBeCancelled, isTrue);
      expect(shipment.hasDriver, isFalse);

      final primaryPackage = shipment.primaryPackage;
      expect(primaryPackage.itemName, equals('Laptop')); // Heaviest package

      final json = shipment.toJson();
      final fromJson = Shipment.fromJson(json);
      expect(fromJson.id, equals(shipment.id));
      expect(fromJson.trackingNumber, equals(shipment.trackingNumber));
      expect(fromJson.packages.length, equals(2));
    });

    test('TrackingTimeline should work correctly', () {
      final timeline = TrackingTimeline(
        shipmentId: 'SHP001',
        events: [
          TrackingEvent(
            id: 'EVT001',
            status: ShipmentStatus.confirmed,
            title: 'Order Confirmed',
            timestamp: DateTime.now().subtract(Duration(hours: 4)),
          ),
          TrackingEvent(
            id: 'EVT002',
            status: ShipmentStatus.pickedUp,
            title: 'Package Picked Up',
            timestamp: DateTime.now().subtract(Duration(hours: 2)),
          ),
          TrackingEvent(
            id: 'EVT003',
            status: ShipmentStatus.inTransit,
            title: 'In Transit',
            timestamp: DateTime.now(),
          ),
        ],
        currentStatus: ShipmentStatus.inTransit,
        lastUpdated: DateTime.now(),
      );

      expect(timeline.isActive, isTrue);
      expect(timeline.progressPercentage, equals(60)); // inTransit = 60%
      expect(timeline.latestEvent?.title, equals('In Transit'));
      expect(timeline.sortedEvents.first.title, equals('In Transit')); // Newest first
      expect(timeline.chronologicalEvents.first.title, equals('Order Confirmed')); // Oldest first

      final json = timeline.toJson();
      final fromJson = TrackingTimeline.fromJson(json);
      expect(fromJson.shipmentId, equals(timeline.shipmentId));
      expect(fromJson.events.length, equals(3));
    });
  });
}
