import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/providers/toast_provider.dart';

/// Individual chat view for user conversations
///
/// This page displays the chat interface for individual conversations
/// with riders or support team from the user perspective.
class ChatView extends StatefulWidget {
  final String conversationId;
  final String conversationName;
  final String conversationType;

  const ChatView({
    super.key,
    required this.conversationId,
    required this.conversationName,
    required this.conversationType,
  });

  @override
  State<ChatView> createState() => _ChatViewState();
}

class _ChatViewState extends State<ChatView> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // Sample messages data
  final List<Map<String, dynamic>> _messages = [
    {
      'id': '1',
      'sender': 'Customer Support',
      'message': 'Hello! Welcome to RideOn. How can I help you today?',
      'timestamp': '2024-12-17 09:00:00',
      'isFromUser': false,
      'type': 'support',
    },
    {
      'id': '2',
      'sender': 'You',
      'message': 'Hi! I have a question about my recent delivery.',
      'timestamp': '2024-12-17 09:02:00',
      'isFromUser': true,
      'type': 'user',
    },
    {
      'id': '3',
      'sender': 'Customer Support',
      'message': 'I\'d be happy to help you with that! Could you please provide me with your order number?',
      'timestamp': '2024-12-17 09:03:00',
      'isFromUser': false,
      'type': 'support',
    },
    {
      'id': '4',
      'sender': 'You',
      'message': 'Sure, it\'s #RO123456',
      'timestamp': '2024-12-17 09:05:00',
      'isFromUser': true,
      'type': 'user',
    },
    {
      'id': '5',
      'sender': 'Customer Support',
      'message': 'Thank you! I can see your order here. What specific question did you have about this delivery?',
      'timestamp': '2024-12-17 09:06:00',
      'isFromUser': false,
      'type': 'support',
    },
  ];

  @override
  void initState() {
    super.initState();
    // Scroll to bottom when chat opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      }
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5FF),
      appBar: _buildAppBar(context),
      body: SafeArea(
        child: Column(
          children: [
            // Messages list
            Expanded(
              child: _buildMessagesList(context),
            ),

            // Message input
            _buildMessageInput(context),

            SizedBox(height: _getSpacing(context, 16)),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: const Color(0xFFF5F5FF),
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: AppColors.black,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getAvatarColor(),
              shape: BoxShape.circle,
            ),
            child: Icon(
              _getAvatarIcon(),
              size: 20,
              color: AppColors.white,
            ),
          ),
          SizedBox(width: _getSpacing(context, 12)),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.conversationName,
                  style: TextStyle(
                    fontSize: _getFontSize(context, 16),
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Poppins',
                    color: AppColors.black,
                  ),
                ),
                Text(
                  widget.conversationType == 'support' ? 'Online' : 'Last seen recently',
                  style: TextStyle(
                    fontSize: _getFontSize(context, 12),
                    fontFamily: 'Poppins',
                    color: AppColors.black.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessagesList(BuildContext context) {
    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.all(_getSpacing(context, 16)),
      itemCount: _messages.length,
      itemBuilder: (context, index) {
        final message = _messages[index];
        return Padding(
          padding: EdgeInsets.only(bottom: _getSpacing(context, 12)),
          child: _buildMessageBubble(context, message),
        );
      },
    );
  }

  Widget _buildMessageBubble(BuildContext context, Map<String, dynamic> message) {
    final isFromUser = message['isFromUser'] as bool;
    final messageText = message['message'] as String;
    final timestamp = message['timestamp'] as String;

    return Row(
      mainAxisAlignment: isFromUser ? MainAxisAlignment.end : MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        if (!isFromUser) ...[
          Container(
            width: _getIconSize(context, 32),
            height: _getIconSize(context, 32),
            decoration: BoxDecoration(
              color: _getAvatarColor(),
              shape: BoxShape.circle,
            ),
            child: Icon(
              _getAvatarIcon(),
              size: _getIconSize(context, 16),
              color: AppColors.white,
            ),
          ),
          SizedBox(width: _getSpacing(context, 8)),
        ],
        Flexible(
          child: Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.75,
            ),
            padding: EdgeInsets.symmetric(
              horizontal: _getSpacing(context, 16),
              vertical: _getSpacing(context, 12),
            ),
            decoration: BoxDecoration(
              color: isFromUser ? AppColors.primary : AppColors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(_getBorderRadius(context, 16)),
                topRight: Radius.circular(_getBorderRadius(context, 16)),
                bottomLeft: Radius.circular(isFromUser ? _getBorderRadius(context, 16) : _getBorderRadius(context, 4)),
                bottomRight: Radius.circular(isFromUser ? _getBorderRadius(context, 4) : _getBorderRadius(context, 16)),
              ),
              border: isFromUser
                  ? null
                  : Border.all(
                      color: AppColors.black.withValues(alpha: 0.1),
                      width: 1,
                    ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  messageText,
                  style: TextStyle(
                    fontSize: _getFontSize(context, 14),
                    fontFamily: 'Poppins',
                    color: isFromUser ? AppColors.white : AppColors.black,
                    height: 1.4,
                  ),
                ),
                SizedBox(height: _getSpacing(context, 4)),
                Text(
                  _formatTimestamp(timestamp),
                  style: TextStyle(
                    fontSize: _getFontSize(context, 10),
                    fontFamily: 'Poppins',
                    color: isFromUser 
                        ? AppColors.white.withValues(alpha: 0.8)
                        : AppColors.black.withValues(alpha: 0.5),
                  ),
                ),
              ],
            ),
          ),
        ),
        if (isFromUser) ...[
          SizedBox(width: _getSpacing(context, 8)),
          Container(
            width: _getIconSize(context, 32),
            height: _getIconSize(context, 32),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
              border: Border.all(
                color: AppColors.primary.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Icon(
              Icons.person,
              size: _getIconSize(context, 16),
              color: AppColors.primary,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildMessageInput(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: _getSpacing(context, 16),
        vertical: _getSpacing(context, 8),
      ),
      decoration: BoxDecoration(
        color: AppColors.white,
        border: Border(
          top: BorderSide(
            color: AppColors.black.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: 'Type a message...',
                hintStyle: TextStyle(
                  fontSize: _getFontSize(context, 14),
                  fontFamily: 'Poppins',
                  color: AppColors.black.withValues(alpha: 0.5),
                ),
                filled: true,
                fillColor: const Color(0xFFF5F5FF),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(_getBorderRadius(context, 24)),
                  borderSide: BorderSide.none,
                ),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: _getSpacing(context, 16),
                  vertical: _getSpacing(context, 12),
                ),
              ),
              style: TextStyle(
                fontSize: _getFontSize(context, 14),
                fontFamily: 'Poppins',
                color: AppColors.black,
              ),
              maxLines: null,
              textCapitalization: TextCapitalization.sentences,
            ),
          ),
          SizedBox(width: _getSpacing(context, 8)),
          GestureDetector(
            onTap: _sendMessage,
            child: Container(
              width: _getIconSize(context, 44),
              height: _getIconSize(context, 44),
              decoration: BoxDecoration(
                color: AppColors.primary,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.send,
                size: _getIconSize(context, 20),
                color: AppColors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getAvatarColor() {
    switch (widget.conversationType) {
      case 'support':
        return AppColors.primary;
      case 'rider':
        return AppColors.success;
      default:
        return AppColors.black.withValues(alpha: 0.6);
    }
  }

  IconData _getAvatarIcon() {
    switch (widget.conversationType) {
      case 'support':
        return Icons.support_agent;
      case 'rider':
        return Icons.delivery_dining;
      default:
        return Icons.person;
    }
  }

  String _formatTimestamp(String timestamp) {
    final dateTime = DateTime.parse(timestamp);
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _sendMessage() {
    final messageText = _messageController.text.trim();
    if (messageText.isEmpty) {
      Toast.warning('Please enter a message');
      return;
    }

    setState(() {
      _messages.add({
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'sender': 'You',
        'message': messageText,
        'timestamp': DateTime.now().toString(),
        'isFromUser': true,
        'type': 'user',
      });
    });

    _messageController.clear();
    Toast.success('Message sent successfully');

    // Scroll to bottom
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  // Responsive helper methods
  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8;
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2;
    } else {
      fontSize = baseFontSize;
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8;
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2;
    } else {
      iconSize = baseIconSize;
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6;
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2;
    } else {
      borderRadius = baseBorderRadius;
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }
}
