import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/providers/toast_provider.dart';

/// Individual chat view for conversations
///
/// This page displays the chat interface for individual conversations
/// with customers or support team.
class ChatView extends StatefulWidget {
  final String conversationId;
  final String conversationName;
  final String conversationType;

  const ChatView({
    super.key,
    required this.conversationId,
    required this.conversationName,
    required this.conversationType,
  });

  @override
  State<ChatView> createState() => _ChatViewState();
}

class _ChatViewState extends State<ChatView> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // Sample messages data for the conversation
  late List<Map<String, dynamic>> _messages;

  @override
  void initState() {
    super.initState();
    _loadMessages();
  }

  void _loadMessages() {
    // Sample messages based on conversation type
    if (widget.conversationType == 'support') {
      _messages = [
        {
          'id': '1',
          'sender': 'Customer Support',
          'message': 'Welcome to RideOn! How can we help you today?',
          'timestamp': '2024-12-17 09:00:00',
          'isFromUser': false,
          'type': 'support',
        },
      ];
    } else {
      _messages = [
        {
          'id': '1',
          'sender': widget.conversationName,
          'message': 'Hi, I need help with my delivery order #12345',
          'timestamp': '2024-12-17 10:30:00',
          'isFromUser': false,
          'type': 'customer',
        },
        {
          'id': '2',
          'sender': 'You',
          'message': 'Hello! I\'m on my way to pick up your package. ETA 10 minutes.',
          'timestamp': '2024-12-17 10:35:00',
          'isFromUser': true,
          'type': 'rider',
        },
      ];
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5FF),
      appBar: _buildAppBar(context),
      body: SafeArea(
        child: Column(
          children: [
            // Messages list
            Expanded(
              child: _buildMessagesList(context),
            ),

            // Message input
            _buildMessageInput(context),

            SizedBox(height: _getSpacing(context, 16)),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: const Color(0xFFF5F5FF),
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: AppColors.black,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getAvatarColor(),
              shape: BoxShape.circle,
            ),
            child: Icon(
              _getAvatarIcon(),
              size: 20,
              color: AppColors.white,
            ),
          ),
          SizedBox(width: _getSpacing(context, 12)),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.conversationName,
                  style: TextStyle(
                    fontSize: _getFontSize(context, 16),
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Poppins',
                    color: AppColors.black,
                  ),
                ),
                Text(
                  widget.conversationType == 'support' ? 'Online' : 'Last seen recently',
                  style: TextStyle(
                    fontSize: _getFontSize(context, 12),
                    fontFamily: 'Poppins',
                    color: AppColors.black.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessagesList(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: ListView.builder(
        controller: _scrollController,
        itemCount: _messages.length,
        itemBuilder: (context, index) {
          final message = _messages[index];
          return Padding(
            padding: EdgeInsets.only(bottom: _getSpacing(context, 12)),
            child: _buildMessageBubble(context, message),
          );
        },
      ),
    );
  }

  Widget _buildMessageBubble(BuildContext context, Map<String, dynamic> message) {
    final isFromUser = message['isFromUser'] as bool;
    final messageType = message['type'] as String;
    
    Color bubbleColor;
    Color textColor;
    
    switch (messageType) {
      case 'support':
        bubbleColor = AppColors.primary.withValues(alpha: 0.1);
        textColor = AppColors.primary;
        break;
      case 'customer':
        bubbleColor = AppColors.warning.withValues(alpha: 0.1);
        textColor = AppColors.black;
        break;
      case 'system':
        bubbleColor = AppColors.black.withValues(alpha: 0.05);
        textColor = AppColors.black.withValues(alpha: 0.7);
        break;
      default: // rider
        bubbleColor = AppColors.success;
        textColor = AppColors.white;
    }

    return Align(
      alignment: isFromUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        padding: EdgeInsets.all(_getSpacing(context, 16)),
        decoration: BoxDecoration(
          color: bubbleColor,
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 16)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (!isFromUser) ...[
              Text(
                message['sender'] as String,
                style: TextStyle(
                  fontSize: _getFontSize(context, 12),
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Poppins',
                  color: textColor.withValues(alpha: 0.8),
                ),
              ),
              SizedBox(height: _getSpacing(context, 4)),
            ],
            Text(
              message['message'] as String,
              style: TextStyle(
                fontSize: _getFontSize(context, 14),
                fontFamily: 'Poppins',
                color: textColor,
              ),
            ),
            SizedBox(height: _getSpacing(context, 8)),
            Text(
              _formatTimestamp(message['timestamp'] as String),
              style: TextStyle(
                fontSize: _getFontSize(context, 10),
                fontFamily: 'Poppins',
                color: textColor.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageInput(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Container(
        padding: EdgeInsets.all(_getSpacing(context, 8)),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 24)),
          border: Border.all(
            color: AppColors.black.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                controller: _messageController,
                decoration: InputDecoration(
                  hintText: 'Type a message...',
                  hintStyle: TextStyle(
                    color: AppColors.black.withValues(alpha: 0.5),
                    fontFamily: 'Poppins',
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: _getSpacing(context, 16),
                    vertical: _getSpacing(context, 12),
                  ),
                ),
                style: TextStyle(
                  fontFamily: 'Poppins',
                  color: AppColors.black,
                ),
                maxLines: null,
              ),
            ),
            SizedBox(width: _getSpacing(context, 8)),
            GestureDetector(
              onTap: _sendMessage,
              child: Container(
                padding: EdgeInsets.all(_getSpacing(context, 12)),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.send,
                  color: AppColors.white,
                  size: _getIconSize(context, 20),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _sendMessage() {
    final messageText = _messageController.text.trim();
    if (messageText.isEmpty) {
      Toast.warning('Please enter a message');
      return;
    }

    setState(() {
      _messages.add({
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'sender': 'You',
        'message': messageText,
        'timestamp': DateTime.now().toString(),
        'isFromUser': true,
        'type': 'rider',
      });
    });

    _messageController.clear();
    Toast.success('Message sent successfully');

    // Scroll to bottom
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Color _getAvatarColor() {
    switch (widget.conversationType) {
      case 'support':
        return AppColors.primary;
      case 'customer':
        return AppColors.success;
      default:
        return AppColors.black.withValues(alpha: 0.6);
    }
  }

  IconData _getAvatarIcon() {
    switch (widget.conversationType) {
      case 'support':
        return Icons.support_agent;
      case 'customer':
        return Icons.person;
      default:
        return Icons.person;
    }
  }

  String _formatTimestamp(String timestamp) {
    try {
      final dateTime = DateTime.parse(timestamp);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inDays > 0) {
        return '${difference.inDays}d ago';
      } else if (difference.inHours > 0) {
        return '${difference.inHours}h ago';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes}m ago';
      } else {
        return 'Just now';
      }
    } catch (e) {
      return 'Unknown';
    }
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 40; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet
    } else {
      spacing = baseSpacing; // Mobile
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2; // Tablet
    } else {
      fontSize = baseFontSize; // Mobile
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2; // Tablet
    } else {
      iconSize = baseIconSize; // Mobile
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2; // Tablet
    } else {
      borderRadius = baseBorderRadius; // Mobile
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.9;
    }

    return borderRadius;
  }
}
