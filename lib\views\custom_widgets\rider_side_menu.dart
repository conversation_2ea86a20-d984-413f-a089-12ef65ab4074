import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';

/// Data class for side menu items
class SideMenuItem {
  /// The icon to display for the menu item
  final IconData icon;

  /// The label text for the menu item
  final String label;

  /// Optional badge count to display
  final int? badgeCount;

  /// Color of the icon and text
  final Color? color;

  /// Callback function when the item is tapped
  final VoidCallback? onTap;

  /// Whether this item is currently selected
  final bool isSelected;

  const SideMenuItem({
    required this.icon,
    required this.label,
    this.badgeCount,
    this.color,
    this.onTap,
    this.isSelected = false,
  });
}

/// Data class for user profile information
class UserProfile {
  /// User's display name
  final String name;

  /// User's email address
  final String email;

  /// URL or path to user's avatar image
  final String? avatarUrl;

  /// Callback when profile is tapped
  final VoidCallback? onProfileTap;

  const UserProfile({
    required this.name,
    required this.email,
    this.avatarUrl,
    this.onProfileTap,
  });
}

/// A reusable side menu widget for rider screens
///
/// This widget provides a customizable side drawer menu with
/// responsive design and consistent styling throughout the app.
///
/// Features:
/// - Responsive design for mobile, tablet, and smartwatch
/// - Customizable user profile section
/// - Reusable menu items with icons, labels, and badges
/// - Navigation callbacks for each menu item
/// - Consistent styling with app theme
/// - Accessibility support
class RiderSideMenu extends StatelessWidget {
  /// User profile information to display at the top
  final UserProfile userProfile;

  /// List of menu items to display
  final List<SideMenuItem> menuItems;

  /// Callback function when the close button is pressed
  final VoidCallback? onClose;

  /// Background color of the menu
  final Color? backgroundColor;

  /// Width of the menu (will be adjusted responsively)
  final double? width;

  const RiderSideMenu({
    super.key,
    required this.userProfile,
    required this.menuItems,
    this.onClose,
    this.backgroundColor,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width ?? _getMenuWidth(context),
      height: MediaQuery.of(context).size.height,
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.white,
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(_getBorderRadius(context)),
          bottomRight: Radius.circular(_getBorderRadius(context)),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.15),
            blurRadius: 20,
            offset: const Offset(5, 0),
            spreadRadius: 2,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: Column(
          children: [
            SizedBox(height: MediaQuery.of(context).padding.top + _getSpacing(context, 32)),

            // User profile section
            _buildUserProfile(context),

            SizedBox(height: _getSpacing(context, 40)),

            // Menu items
            Expanded(
              child: _buildMenuItems(context),
            ),

            SizedBox(height: _getSpacing(context, 40)), // Bottom padding
          ],
        ),
      ),
    );
  }

  Widget _buildUserProfile(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: _getSpacing(context, 20)),
      padding: EdgeInsets.all(_getSpacing(context, 20)),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(_getSpacing(context, 20)),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: GestureDetector(
        onTap: userProfile.onProfileTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Avatar with enhanced design
            _buildAvatar(context),

            SizedBox(height: _getSpacing(context, 16)),

            // Name
            Text(
              userProfile.name,
              style: TextStyle(
                color: AppColors.black,
                fontSize: _getNameFontSize(context),
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: _getSpacing(context, 4)),

            // Email
            Text(
              userProfile.email,
              style: TextStyle(
                color: AppColors.black.withValues(alpha: 0.6),
                fontSize: _getEmailFontSize(context),
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatar(BuildContext context) {
    final avatarSize = _getAvatarSize(context);

    return Container(
      width: avatarSize,
      height: avatarSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withValues(alpha: 0.1),
            AppColors.primary.withValues(alpha: 0.05),
          ],
        ),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipOval(
        child: userProfile.avatarUrl != null
            ? Image.network(
                userProfile.avatarUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildDefaultAvatar(context),
              )
            : _buildDefaultAvatar(context),
      ),
    );
  }

  Widget _buildDefaultAvatar(BuildContext context) {
    return Container(
      color: AppColors.primary.withValues(alpha: 0.1),
      child: Icon(
        Icons.person,
        size: _getAvatarSize(context) * 0.6,
        color: AppColors.primary,
      ),
    );
  }

  Widget _buildMenuItems(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getSpacing(context, 24)),
      child: Column(
        children: [
          ...menuItems.map((item) => _buildMenuItem(context, item)),
        ],
      ),
    );
  }

  Widget _buildMenuItem(BuildContext context, SideMenuItem item) {
    return GestureDetector(
      onTap: item.onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        margin: EdgeInsets.only(bottom: _getSpacing(context, 8)),
        padding: EdgeInsets.symmetric(
          horizontal: _getSpacing(context, 16),
          vertical: _getSpacing(context, 14),
        ),
        decoration: BoxDecoration(
          color: item.isSelected
              ? AppColors.primary.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(_getSpacing(context, 12)),
          border: item.isSelected
              ? Border.all(
                  color: AppColors.primary.withValues(alpha: 0.2),
                  width: 1,
                )
              : null,
        ),
        child: Row(
          children: [
            // Icon with enhanced design
            Container(
              padding: EdgeInsets.all(_getSpacing(context, 8)),
              decoration: BoxDecoration(
                color: item.isSelected
                    ? AppColors.primary.withValues(alpha: 0.1)
                    : AppColors.black.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(_getSpacing(context, 8)),
              ),
              child: Icon(
                item.icon,
                size: _getMenuIconSize(context),
                color: item.isSelected
                    ? (item.color ?? AppColors.primary)
                    : (item.color ?? AppColors.black.withValues(alpha: 0.7)),
              ),
            ),

            SizedBox(width: _getSpacing(context, 12)),

            // Label
            Expanded(
              child: Text(
                item.label,
                style: TextStyle(
                  color: item.isSelected
                      ? (item.color ?? AppColors.primary)
                      : (item.color ?? AppColors.black.withValues(alpha: 0.7)),
                  fontSize: _getMenuFontSize(context),
                  fontFamily: 'Poppins',
                  fontWeight: item.isSelected ? FontWeight.w600 : FontWeight.w500,
                ),
              ),
            ),

            // Badge (if present)
            if (item.badgeCount != null && item.badgeCount! > 0)
              _buildBadge(context, item.badgeCount!),
          ],
        ),
      ),
    );
  }

  Widget _buildBadge(BuildContext context, int count) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: _getSpacing(context, 6),
        vertical: _getSpacing(context, 2),
      ),
      decoration: BoxDecoration(
        color: AppColors.error,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Text(
        count.toString(),
        style: TextStyle(
          color: AppColors.white,
          fontSize: _getBadgeFontSize(context),
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  // Responsive helper methods
  double _getMenuWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return screenWidth * 0.85; // Smartwatch - 85% of screen width
    } else if (screenWidth > 600) {
      return 300; // Tablet - fixed width
    } else {
      return 260; // Mobile - standard sidebar width
    }
  }

  double _getBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 20; // Smartwatch - moderate curve
    } else if (screenWidth > 600) {
      return 30; // Tablet - larger curve
    } else {
      return 25; // Mobile - smooth curve
    }
  }



  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet
    } else {
      spacing = baseSpacing; // Mobile
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getMenuIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 16; // Smartwatch
    } else if (screenWidth > 600) {
      return 20; // Tablet
    } else {
      return 18; // Mobile
    }
  }

  double _getAvatarSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 50; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 80; // Tablet
    } else {
      baseSize = 70; // Mobile (matching original design)
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getNameFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 14; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 20; // Tablet
    } else {
      baseSize = 18; // Mobile (matching original design)
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getEmailFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 14; // Tablet
    } else {
      baseSize = 12; // Mobile (matching original design)
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getMenuFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 14; // Tablet
    } else {
      baseSize = 12; // Mobile (matching original design)
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getBadgeFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 9; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 13; // Tablet
    } else {
      baseSize = 12; // Mobile (matching original design)
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }
}