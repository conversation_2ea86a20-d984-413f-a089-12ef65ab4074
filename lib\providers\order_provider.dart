import 'package:flutter/foundation.dart';
import 'package:rideoon/services/order_api_service.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/models/order/collection.dart';
import 'package:rideoon/models/order/package.dart';

/// Provider for managing order state across the application
///
/// This provider handles:
/// - Loading and managing orders (collections)
/// - Creating new orders
/// - Updating existing orders
/// - Managing packages within orders
/// - Order pricing and address management
/// - Notifying listeners when order data changes
class OrderProvider extends ChangeNotifier {
  List<Collection> _orders = [];
  Collection? _currentOrder;
  List<Package> _currentOrderPackages = [];
  Map<String, dynamic>? _currentOrderPricing;
  bool _isLoading = false;
  String? _error;

  /// Get all orders
  List<Collection> get orders => List.unmodifiable(_orders);

  /// Get current order being worked on
  Collection? get currentOrder => _currentOrder;

  /// Get packages for current order
  List<Package> get currentOrderPackages => List.unmodifiable(_currentOrderPackages);

  /// Get pricing for current order
  Map<String, dynamic>? get currentOrderPricing => _currentOrderPricing;

  /// Check if currently loading
  bool get isLoading => _isLoading;

  /// Get current error message
  String? get error => _error;

  /// Get orders by status
  List<Collection> getOrdersByStatus(String status) {
    return _orders.where((order) => order.status == status).toList();
  }

  /// Get pending orders
  List<Collection> get pendingOrders => getOrdersByStatus('pending');

  /// Get confirmed orders
  List<Collection> get confirmedOrders => getOrdersByStatus('confirmed');

  /// Get completed orders
  List<Collection> get completedOrders => getOrdersByStatus('completed');

  /// Load all orders from the API
  Future<void> loadOrders({Map<String, String>? queryParams}) async {
    try {
      _setLoading(true);
      _clearError();

      final authToken = await AuthService.getAuthToken();
      if (authToken != null) {
        final response = await OrderApiService.getOrders(
          queryParams: queryParams,
          authToken: authToken,
        );
        
        if (response.success && response.data != null) {
          _orders = response.data!;
        } else {
          _setError('Failed to load orders: ${response.message}');
          _orders = [];
        }
      } else {
        _setError('Authentication required to load orders');
        _orders = [];
      }
    } catch (e) {
      _setError('Failed to load orders: $e');
      _orders = [];
    } finally {
      _setLoading(false);
    }
  }

  /// Create a new order
  Future<bool> createOrder(CreateCollectionRequest request) async {
    try {
      _setLoading(true);
      _clearError();

      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        _setError('Authentication required to create order');
        return false;
      }

      final response = await OrderApiService.createOrder(request, authToken: authToken);
      
      if (response.success && response.data != null) {
        _currentOrder = response.data!;
        _orders.insert(0, _currentOrder!); // Add to beginning of list
        _currentOrderPackages = []; // Reset packages for new order
        _currentOrderPricing = null; // Reset pricing
        return true;
      } else {
        _setError('Failed to create order: ${response.message}');
        return false;
      }
    } catch (e) {
      _setError('Failed to create order: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing order
  Future<bool> updateOrder(String orderUuid, UpdateCollectionRequest request) async {
    try {
      _setLoading(true);
      _clearError();

      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        _setError('Authentication required to update order');
        return false;
      }

      final response = await OrderApiService.updateOrder(orderUuid, request, authToken: authToken);
      
      if (response.success && response.data != null) {
        final updatedOrder = response.data!;
        
        // Update in orders list
        final index = _orders.indexWhere((order) => order.uuid == orderUuid);
        if (index != -1) {
          _orders[index] = updatedOrder;
        }
        
        // Update current order if it's the same
        if (_currentOrder?.uuid == orderUuid) {
          _currentOrder = updatedOrder;
        }
        
        return true;
      } else {
        _setError('Failed to update order: ${response.message}');
        return false;
      }
    } catch (e) {
      _setError('Failed to update order: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Set current order by UUID
  Future<bool> setCurrentOrder(String orderUuid) async {
    try {
      _setLoading(true);
      _clearError();

      // First check if order is already in local list
      final existingOrder = _orders.firstWhere(
        (order) => order.uuid == orderUuid,
        orElse: () => throw StateError('Order not found'),
      );
      
      if (existingOrder != null) {
        _currentOrder = existingOrder;
        await loadCurrentOrderPackages();
        return true;
      }

      // If not found locally, fetch from API
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        _setError('Authentication required to load order');
        return false;
      }

      final response = await OrderApiService.getOrder(orderUuid, authToken: authToken);
      
      if (response.success && response.data != null) {
        _currentOrder = response.data!;
        await loadCurrentOrderPackages();
        return true;
      } else {
        _setError('Failed to load order: ${response.message}');
        return false;
      }
    } catch (e) {
      _setError('Failed to set current order: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Load packages for current order
  Future<void> loadCurrentOrderPackages() async {
    if (_currentOrder == null) return;

    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return;

      final response = await OrderApiService.getOrderPackages(
        _currentOrder!.uuid,
        authToken: authToken,
      );
      
      if (response.success && response.data != null) {
        _currentOrderPackages = response.data!;
      } else {
        _currentOrderPackages = [];
      }
    } catch (e) {
      _currentOrderPackages = [];
    }
    
    notifyListeners();
  }

  /// Load pricing for current order
  Future<void> loadCurrentOrderPricing() async {
    if (_currentOrder == null) return;

    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return;

      final response = await OrderApiService.getOrderPrice(
        _currentOrder!.uuid,
        authToken: authToken,
      );
      
      if (response.success && response.data != null) {
        _currentOrderPricing = response.data!;
      } else {
        _currentOrderPricing = null;
      }
    } catch (e) {
      _currentOrderPricing = null;
    }
    
    notifyListeners();
  }

  /// Clear current order
  void clearCurrentOrder() {
    _currentOrder = null;
    _currentOrderPackages = [];
    _currentOrderPricing = null;
    notifyListeners();
  }

  /// Refresh current order data
  Future<void> refreshCurrentOrder() async {
    if (_currentOrder != null) {
      await setCurrentOrder(_currentOrder!.uuid);
      await loadCurrentOrderPricing();
    }
  }

  /// Get order by UUID
  Collection? getOrderByUuid(String uuid) {
    try {
      return _orders.firstWhere((order) => order.uuid == uuid);
    } catch (e) {
      return null;
    }
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    _error = null;
  }

  /// Clear all data
  void clearAll() {
    _orders = [];
    _currentOrder = null;
    _currentOrderPackages = [];
    _currentOrderPricing = null;
    _error = null;
    _isLoading = false;
    notifyListeners();
  }
}
