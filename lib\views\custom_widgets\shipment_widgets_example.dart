import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/current_shipments_widget.dart';
import 'package:rideoon/views/custom_widgets/shipment_card.dart';
import 'package:rideoon/views/custom_widgets/shipment_tabs_widget.dart';

/// Example usage of the reusable shipment widgets
///
/// This file demonstrates how to use the ShipmentCard and CurrentShipmentsWidget
/// components in different scenarios.
class ShipmentWidgetsExample extends StatefulWidget {
  const ShipmentWidgetsExample({super.key});

  @override
  State<ShipmentWidgetsExample> createState() => _ShipmentWidgetsExampleState();
}

class _ShipmentWidgetsExampleState extends State<ShipmentWidgetsExample> {
  // Sample data for different shipment statuses
  List<ShipmentData> get _sampleShipments => [
    ShipmentData(
      id: '1',
      title: 'Electronics Package',
      trackingNumber: '#ELC123456789',
      status: ShipmentStatus.inProgress,
      trackingSteps: [
        TrackingStep(
          title: 'Package picked up from sender',
          address: 'Lagos Island, Victoria Island',
          time: '2024-12-18 10:24:02 AM',
          isCompleted: true,
        ),
        TrackingStep(
          title: 'Package arrived at sorting facility',
          time: '2024-12-18 02:15:30 PM',
          isCompleted: true,
        ),
        TrackingStep(
          title: 'Out for delivery',
          time: '2024-12-19 08:00:00 AM',
          isCompleted: false,
        ),
        TrackingStep(
          title: 'Package delivered',
          address: 'Ibadan, Mokola',
          isCompleted: false,
        ),
      ],
    ),
    ShipmentData(
      id: '2',
      title: 'Documents Envelope',
      trackingNumber: '#DOC987654321',
      status: ShipmentStatus.completed,
      trackingSteps: [
        TrackingStep(
          title: 'Package picked up',
          time: '2024-12-17 09:00:00 AM',
          isCompleted: true,
        ),
        TrackingStep(
          title: 'Package delivered',
          address: 'Abuja, Wuse 2',
          time: '2024-12-17 04:30:00 PM',
          isCompleted: true,
        ),
      ],
    ),
    ShipmentData(
      id: '3',
      title: 'Fragile Items',
      trackingNumber: '#FRG456789123',
      status: ShipmentStatus.pending,
      trackingSteps: [
        TrackingStep(
          title: 'Awaiting pickup',
          isCompleted: false,
        ),
      ],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Shipment Widgets Example'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
      ),
      backgroundColor: AppColors.white,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Example 1: Current Shipments Widget with all features
            _buildSectionTitle('Current Shipments Widget - Full Features'),
            CurrentShipmentsWidget(
              shipments: _sampleShipments,
              onSeeAllTap: () => _showSnackBar('See all tapped'),
              onShipmentTap: (shipment) => _showSnackBar('Tapped: ${shipment.title}'),
            ),

            const SizedBox(height: 32),

            // Example 2: Current Shipments Widget with limited shipments
            _buildSectionTitle('Current Shipments Widget - Max 1 Shipment'),
            CurrentShipmentsWidget(
              title: 'Recent Shipment',
              shipments: _sampleShipments,
              maxShipments: 1,
              seeAllText: 'View More',
              onSeeAllTap: () => _showSnackBar('View more tapped'),
            ),

            const SizedBox(height: 32),

            // Example 3: Empty state
            _buildSectionTitle('Current Shipments Widget - Empty State'),
            CurrentShipmentsWidget(
              title: 'No Active Shipments',
              shipments: const [],
              showSeeAll: false,
            ),

            const SizedBox(height: 32),

            // Example 4: Individual Shipment Cards
            _buildSectionTitle('Individual Shipment Cards'),
            ...(_sampleShipments.map((shipment) => Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: ShipmentCard(
                shipment: shipment,
                onExpandToggle: () => _showSnackBar('Toggle expansion for ${shipment.title}'),
                onCardTap: () => _showSnackBar('Card tapped: ${shipment.title}'),
              ),
            ))),

            const SizedBox(height: 32),

            // Example 5: Tabbed Shipments Widget
            _buildSectionTitle('Tabbed Shipments Widget'),
            ShipmentTabsWidget(
              currentShipments: _sampleShipments.where((s) =>
                s.status == ShipmentStatus.inProgress ||
                s.status == ShipmentStatus.pending
              ).toList(),
              shipmentHistory: _sampleShipments.where((s) =>
                s.status == ShipmentStatus.completed
              ).toList(),
              onCurrentShipmentTap: (shipment) => _showSnackBar('Current: ${shipment.title}'),
              onHistoryShipmentTap: (shipment) => _showSnackBar('History: ${shipment.title}'),
            ),

            const SizedBox(height: 32),

            // Example 6: Custom styled shipment card
            _buildSectionTitle('Custom Styled Shipment Card'),
            ShipmentCard(
              shipment: _sampleShipments.first,
              customIcon: Icons.local_shipping,
              backgroundColor: AppColors.primary.withValues(alpha: 0.05),
              showTrackingTimeline: false,
              onCardTap: () => _showSnackBar('Custom card tapped'),
            ),

            const SizedBox(height: 100), // Extra space for scrolling
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          fontFamily: 'Poppins',
          color: AppColors.black,
        ),
      ),
    );
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.primary,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

/// Example of how to create custom shipment data
class ShipmentDataFactory {
  static ShipmentData createSampleShipment({
    required String id,
    required String title,
    required String trackingNumber,
    required ShipmentStatus status,
  }) {
    return ShipmentData(
      id: id,
      title: title,
      trackingNumber: trackingNumber,
      status: status,
      trackingSteps: _getDefaultTrackingSteps(status),
    );
  }

  static List<TrackingStep> _getDefaultTrackingSteps(ShipmentStatus status) {
    switch (status) {
      case ShipmentStatus.pending:
        return [
          TrackingStep(
            title: 'Awaiting pickup',
            isCompleted: false,
          ),
        ];

      case ShipmentStatus.inProgress:
        return [
          TrackingStep(
            title: 'Package picked up',
            time: DateTime.now().subtract(Duration(hours: 2)).toString(),
            isCompleted: true,
          ),
          TrackingStep(
            title: 'In transit',
            isCompleted: false,
          ),
          TrackingStep(
            title: 'Out for delivery',
            isCompleted: false,
          ),
        ];

      case ShipmentStatus.completed:
        return [
          TrackingStep(
            title: 'Package picked up',
            time: DateTime.now().subtract(Duration(days: 1)).toString(),
            isCompleted: true,
          ),
          TrackingStep(
            title: 'Package delivered',
            time: DateTime.now().subtract(Duration(hours: 4)).toString(),
            isCompleted: true,
          ),
        ];

      case ShipmentStatus.cancelled:
        return [
          TrackingStep(
            title: 'Shipment cancelled',
            time: DateTime.now().subtract(Duration(hours: 1)).toString(),
            isCompleted: true,
          ),
        ];
    }
  }
}
