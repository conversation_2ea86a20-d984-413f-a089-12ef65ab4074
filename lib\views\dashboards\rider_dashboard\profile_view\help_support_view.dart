import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/views/dashboards/rider_dashboard/messages_view/chat_view.dart';

/// Help & Support view with social links and customer support access
class HelpSupportView extends StatefulWidget {
  const HelpSupportView({super.key});

  @override
  State<HelpSupportView> createState() => _HelpSupportViewState();
}

class _HelpSupportViewState extends State<HelpSupportView> {
  // Social media links
  final List<Map<String, dynamic>> _socialLinks = [
    {
      'name': 'Facebook',
      'icon': Icons.facebook,
      'url': 'https://facebook.com/rideon',
      'color': const Color(0xFF1877F2),
    },
    {
      'name': 'Twitter',
      'icon': Icons.alternate_email,
      'url': 'https://twitter.com/rideon',
      'color': const Color(0xFF1DA1F2),
    },
    {
      'name': 'Instagram',
      'icon': Icons.camera_alt,
      'url': 'https://instagram.com/rideon',
      'color': const Color(0xFFE4405F),
    },
    {
      'name': 'LinkedIn',
      'icon': Icons.business,
      'url': 'https://linkedin.com/company/rideon',
      'color': const Color(0xFF0A66C2),
    },
    {
      'name': 'WhatsApp',
      'icon': Icons.phone,
      'url': 'https://wa.me/1234567890',
      'color': const Color(0xFF25D366),
    },
    {
      'name': 'Email',
      'icon': Icons.email,
      'url': 'mailto:<EMAIL>',
      'color': const Color(0xFFEA4335),
    },
  ];

  // FAQ items
  final List<Map<String, dynamic>> _faqItems = [
    {
      'question': 'How do I accept delivery orders?',
      'answer': 'You can accept delivery orders from the Deliveries tab. New orders will appear in the Available section.',
    },
    {
      'question': 'How are payments calculated?',
      'answer': 'Payments are calculated based on distance, delivery time, and any additional fees. You can view detailed earnings in the Payment & Earnings section.',
    },
    {
      'question': 'What should I do if I can\'t find the pickup location?',
      'answer': 'Use the in-app navigation or contact the customer through the messaging feature. You can also contact support for assistance.',
    },
    {
      'question': 'How do I update my profile information?',
      'answer': 'Go to Profile > Personal Information to update your details, payment methods, and other account settings.',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5FF),
      appBar: _buildAppBar(context),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(_getHorizontalPadding(context)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: _getSpacing(context, 20)),

              // Customer support section
              _buildCustomerSupportSection(context),

              SizedBox(height: _getSpacing(context, 32)),

              // FAQ section
              _buildFAQSection(context),

              SizedBox(height: _getSpacing(context, 32)),

              // Social media links
              _buildSocialMediaSection(context),

              SizedBox(height: _getSpacing(context, 32)),

              // App information
              _buildAppInformation(context),

              SizedBox(height: _getSpacing(context, 20)),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: const Color(0xFFF5F5FF),
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: AppColors.black,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        'Help & Support',
        style: TextStyle(
          fontSize: _getFontSize(context, 18),
          fontWeight: FontWeight.w600,
          fontFamily: 'Poppins',
          color: AppColors.black,
        ),
      ),
      centerTitle: true,
    );
  }

  Widget _buildCustomerSupportSection(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 24)),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 16)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.support_agent,
            size: _getIconSize(context, 48),
            color: AppColors.white,
          ),
          SizedBox(height: _getSpacing(context, 16)),
          Text(
            'Need Help?',
            style: TextStyle(
              fontSize: _getFontSize(context, 24),
              fontWeight: FontWeight.bold,
              fontFamily: 'Poppins',
              color: AppColors.white,
            ),
          ),
          SizedBox(height: _getSpacing(context, 8)),
          Text(
            'Our support team is here to help you 24/7. Chat with us for quick assistance.',
            style: TextStyle(
              fontSize: _getFontSize(context, 14),
              fontFamily: 'Poppins',
              color: AppColors.white.withValues(alpha: 0.9),
            ),
          ),
          SizedBox(height: _getSpacing(context, 20)),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                _openCustomerSupport(context);
              },
              icon: Icon(
                Icons.chat,
                size: _getIconSize(context, 20),
                color: AppColors.primary,
              ),
              label: Text(
                'Chat with Support',
                style: TextStyle(
                  fontSize: _getFontSize(context, 16),
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Poppins',
                  color: AppColors.primary,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.white,
                foregroundColor: AppColors.primary,
                padding: EdgeInsets.symmetric(
                  vertical: _getSpacing(context, 16),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
                ),
                elevation: 0,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFAQSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Frequently Asked Questions',
          style: TextStyle(
            fontSize: _getFontSize(context, 18),
            fontWeight: FontWeight.w600,
            fontFamily: 'Poppins',
            color: AppColors.black,
          ),
        ),
        SizedBox(height: _getSpacing(context, 16)),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
            border: Border.all(
              color: AppColors.black.withValues(alpha: 0.1),
            ),
          ),
          child: Column(
            children: _faqItems.asMap().entries.map((entry) {
              final index = entry.key;
              final faq = entry.value;
              return _buildFAQItem(
                context,
                question: faq['question'],
                answer: faq['answer'],
                isLast: index == _faqItems.length - 1,
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildFAQItem(BuildContext context, {
    required String question,
    required String answer,
    required bool isLast,
  }) {
    return ExpansionTile(
      title: Text(
        question,
        style: TextStyle(
          fontSize: _getFontSize(context, 16),
          fontWeight: FontWeight.w500,
          fontFamily: 'Poppins',
          color: AppColors.black,
        ),
      ),
      children: [
        Padding(
          padding: EdgeInsets.fromLTRB(
            _getSpacing(context, 16),
            0,
            _getSpacing(context, 16),
            _getSpacing(context, 16),
          ),
          child: Text(
            answer,
            style: TextStyle(
              fontSize: _getFontSize(context, 14),
              fontFamily: 'Poppins',
              color: AppColors.black.withValues(alpha: 0.7),
            ),
          ),
        ),
      ],
      iconColor: AppColors.primary,
      collapsedIconColor: AppColors.black.withValues(alpha: 0.6),
    );
  }

  Widget _buildSocialMediaSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Connect with Us',
          style: TextStyle(
            fontSize: _getFontSize(context, 18),
            fontWeight: FontWeight.w600,
            fontFamily: 'Poppins',
            color: AppColors.black,
          ),
        ),
        SizedBox(height: _getSpacing(context, 16)),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(_getSpacing(context, 20)),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
            border: Border.all(
              color: AppColors.black.withValues(alpha: 0.1),
            ),
          ),
          child: Column(
            children: [
              Text(
                'Follow us on social media for updates and news',
                style: TextStyle(
                  fontSize: _getFontSize(context, 14),
                  fontFamily: 'Poppins',
                  color: AppColors.black.withValues(alpha: 0.6),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: _getSpacing(context, 20)),
              Wrap(
                spacing: _getSpacing(context, 16),
                runSpacing: _getSpacing(context, 16),
                children: _socialLinks.map((social) => _buildSocialButton(context, social)).toList(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSocialButton(BuildContext context, Map<String, dynamic> social) {
    return GestureDetector(
      onTap: () {
        Toast.info('Opening ${social['name']}...');
        // TODO: Implement URL launcher
      },
      child: Container(
        width: _getIconSize(context, 60),
        height: _getIconSize(context, 60),
        decoration: BoxDecoration(
          color: social['color'].withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
          border: Border.all(
            color: social['color'].withValues(alpha: 0.3),
          ),
        ),
        child: Icon(
          social['icon'],
          size: _getIconSize(context, 28),
          color: social['color'],
        ),
      ),
    );
  }

  Widget _buildAppInformation(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 20)),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
        border: Border.all(
          color: AppColors.black.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        children: [
          Text(
            'RideOn Rider App',
            style: TextStyle(
              fontSize: _getFontSize(context, 18),
              fontWeight: FontWeight.w600,
              fontFamily: 'Poppins',
              color: AppColors.black,
            ),
          ),
          SizedBox(height: _getSpacing(context, 8)),
          Text(
            'Version 1.0.0',
            style: TextStyle(
              fontSize: _getFontSize(context, 14),
              fontFamily: 'Poppins',
              color: AppColors.black.withValues(alpha: 0.6),
            ),
          ),
          SizedBox(height: _getSpacing(context, 16)),
          Text(
            '© 2024 RideOn. All rights reserved.',
            style: TextStyle(
              fontSize: _getFontSize(context, 12),
              fontFamily: 'Poppins',
              color: AppColors.black.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _openCustomerSupport(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ChatView(
          conversationId: 'support_chat',
          conversationName: 'Customer Support',
          conversationType: 'support',
        ),
      ),
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16;
    } else if (screenWidth > 600) {
      basePadding = 40;
    } else {
      basePadding = 24;
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8;
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2;
    } else {
      fontSize = baseFontSize;
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8;
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2;
    } else {
      iconSize = baseIconSize;
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6;
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2;
    } else {
      borderRadius = baseBorderRadius;
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }
}
