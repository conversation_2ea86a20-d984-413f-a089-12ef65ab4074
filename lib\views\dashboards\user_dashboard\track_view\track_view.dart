import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/current_shipments_widget.dart';
import 'package:rideoon/views/custom_widgets/shipment_card.dart';
import 'package:rideoon/views/custom_widgets/push_notfication.dart';
import 'package:rideoon/views/dashboards/user_dashboard/send_a_package/send_package_view.dart';
import 'package:rideoon/services/package_data_service.dart';

/// Track view page for user dashboard
///
/// This page will contain package tracking features,
/// including tracking number input, real-time tracking, and delivery status.
class TrackView extends StatefulWidget {
  /// Callback function to navigate back to home tab
  final VoidCallback? onNavigateToHome;

  /// Whether the parent dashboard is showing a header
  final bool hasHeader;

  const TrackView({super.key, this.onNavigateToHome, this.hasHeader = false});

  @override
  State<TrackView> createState() => _TrackViewState();
}

class _TrackViewState extends State<TrackView> {
  final TextEditingController _trackingController = TextEditingController();
  bool _isTracking = false;
  bool _isLoading = true;
  List<ShipmentData> _currentShipments = [];
  List<ShipmentData> _searchResults = [];
  bool _hasSearched = false;

  // State for search results expansion
  List<ShipmentData> _searchResultsState = [];

  @override
  void initState() {
    super.initState();
    _loadShipmentData();
  }

  @override
  void dispose() {
    _trackingController.dispose();
    super.dispose();
  }

  /// Load shipment data from local storage
  Future<void> _loadShipmentData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Load current shipments
      final currentShipmentsData = await PackageDataService.getCurrentShipments();
      final currentShipments = currentShipmentsData.map((data) => _convertToShipmentData(data)).toList();

      setState(() {
        _currentShipments = currentShipments;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading shipment data: $e');
      setState(() {
        _currentShipments = [];
        _isLoading = false;
      });
    }
  }

  /// Convert stored data to ShipmentData
  ShipmentData _convertToShipmentData(Map<String, dynamic> data) {
    final status = _getShipmentStatus(data['status'] ?? 'pending');
    final trackingSteps = _generateTrackingSteps(status, data);

    // Get title from cargo items or package data
    String title = 'Package';
    if (data['cargoItems'] != null && (data['cargoItems'] as List).isNotEmpty) {
      final firstItem = (data['cargoItems'] as List).first;
      title = firstItem['itemName'] ?? 'Package';
    } else if (data['packageData'] != null) {
      final packageData = data['packageData'] as Map<String, dynamic>;
      if (packageData['packageDetails'] != null) {
        final packageDetails = packageData['packageDetails'] as Map<String, dynamic>;
        title = packageDetails['itemName'] ?? packageDetails['category'] ?? 'Package';
      }
    }

    return ShipmentData(
      id: data['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      trackingNumber: data['trackingNumber'] ?? '#RO00000',
      status: status,
      trackingSteps: trackingSteps,
    );
  }

  /// Get shipment status from string
  ShipmentStatus _getShipmentStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return ShipmentStatus.pending;
      case 'in_progress':
      case 'inprogress':
        return ShipmentStatus.inProgress;
      case 'completed':
        return ShipmentStatus.completed;
      case 'cancelled':
        return ShipmentStatus.cancelled;
      default:
        return ShipmentStatus.pending;
    }
  }

  /// Generate tracking steps based on status and data
  List<TrackingStep> _generateTrackingSteps(ShipmentStatus status, Map<String, dynamic> data) {
    final steps = <TrackingStep>[];

    // Get pickup and receiver addresses
    String pickupAddress = '';
    String receiverAddress = '';

    if (data['pickupData'] != null) {
      final pickup = data['pickupData'] as Map<String, dynamic>;
      pickupAddress = '${pickup['fullAddress'] ?? ''}, ${pickup['state'] ?? ''}';
    }

    if (data['receiverData'] != null) {
      final receiver = data['receiverData'] as Map<String, dynamic>;
      receiverAddress = '${receiver['address'] ?? ''}, ${receiver['state'] ?? ''}';
    }

    // Add steps based on status
    steps.add(TrackingStep(
      title: 'Order placed',
      time: _formatTimestamp(data['timestamp']),
      isCompleted: true,
    ));

    if (status == ShipmentStatus.inProgress || status == ShipmentStatus.completed) {
      steps.add(TrackingStep(
        title: 'Rider picked up package from sender location',
        address: pickupAddress.isNotEmpty ? pickupAddress : null,
        isCompleted: true,
      ));

      steps.add(TrackingStep(
        title: 'Package in transit',
        isCompleted: status == ShipmentStatus.completed,
      ));
    }

    if (status == ShipmentStatus.completed) {
      steps.add(TrackingStep(
        title: 'Package delivered to destination',
        address: receiverAddress.isNotEmpty ? receiverAddress : null,
        isCompleted: true,
      ));
    } else if (status == ShipmentStatus.pending) {
      steps.add(TrackingStep(
        title: 'Awaiting pickup',
        isCompleted: false,
      ));
    }

    return steps;
  }

  /// Format timestamp for display
  String? _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return null;

    try {
      final dateTime = timestamp is int
          ? DateTime.fromMillisecondsSinceEpoch(timestamp)
          : DateTime.parse(timestamp.toString());

      return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Top spacing (adjust based on whether header is shown)
            SizedBox(height: widget.hasHeader
              ? MediaQuery.of(context).padding.top + 95 // Space for header + underline
              : MediaQuery.of(context).size.height * 0.07), // Original spacing

            // Divider (only show if no header)
            if (!widget.hasHeader) _buildDivider(context),

            SizedBox(height: _getSpacing(context, 28)),

            // Tracking banner
            _buildTrackingBanner(context),

            SizedBox(height: _getSpacing(context, 32)),

            // Action buttons section (Send Items and Receive Items)
            _buildActionButtonsSection(context),

            SizedBox(height: _getSpacing(context, 32)),

            // Current shipments section
            _buildCurrentShipmentsSection(context),

            SizedBox(height: _getSpacing(context, 100)), // Space for bottom nav
          ],
        ),
      ),
    );
  }





  Widget _buildDivider(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getResponsiveSpacing(context, 33)),
      child: Container(
        width: double.infinity,
        height: 1,
        decoration: BoxDecoration(
          color: AppColors.black.withValues(alpha: 0.1),
        ),
      ),
    );
  }

  Widget _buildTrackingBanner(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final bannerWidth = screenWidth - 32; // 16px padding on each side

    return Center(
      child: Container(
        width: bannerWidth,
        constraints: BoxConstraints(
          maxWidth: 400, // Maximum width for larger screens
          minHeight: 160, // Minimum height for better proportions
        ),
        margin: const EdgeInsets.symmetric(horizontal: 16),
        decoration: ShapeDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.primary.withValues(alpha: 0.9),
              AppColors.primary.withValues(alpha: 0.7),
            ],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          shadows: [
            BoxShadow(
              color: AppColors.primary.withValues(alpha: 0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header text section
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Want to track a package?',
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: _getResponsiveFontSize(context, 18),
                      fontFamily: 'Bricolage Grotesque',
                      fontWeight: FontWeight.w600,
                      height: 1.2,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Please enter your tracking number below',
                    style: TextStyle(
                      color: AppColors.white.withValues(alpha: 0.85),
                      fontSize: _getResponsiveFontSize(context, 14),
                      fontFamily: 'Bricolage Grotesque',
                      fontWeight: FontWeight.w400,
                      height: 1.3,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // Search input field
              Container(
                width: double.infinity,
                height: 52,
                decoration: ShapeDecoration(
                  color: AppColors.white,
                  shape: RoundedRectangleBorder(
                    side: BorderSide(
                      color: AppColors.black.withValues(alpha: 0.15),
                      width: 1.5,
                    ),
                    borderRadius: BorderRadius.circular(26),
                  ),
                  shadows: [
                    BoxShadow(
                      color: AppColors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    const SizedBox(width: 20),
                    Icon(
                      Icons.search,
                      color: const Color(0xFF696969),
                      size: _getResponsiveIconSize(context, 22),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextField(
                        controller: _trackingController,
                        style: TextStyle(
                          color: AppColors.black,
                          fontSize: _getResponsiveFontSize(context, 15),
                          fontFamily: 'Bricolage Grotesque',
                          fontWeight: FontWeight.w400,
                        ),
                        decoration: InputDecoration(
                          hintText: 'Enter tracking number',
                          hintStyle: TextStyle(
                            color: const Color(0xFF696969),
                            fontSize: _getResponsiveFontSize(context, 15),
                            fontFamily: 'Bricolage Grotesque',
                            fontWeight: FontWeight.w400,
                          ),
                          border: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          errorBorder: InputBorder.none,
                          disabledBorder: InputBorder.none,
                          contentPadding: EdgeInsets.zero,
                        ),
                        onSubmitted: (value) {
                          if (value.trim().isNotEmpty) {
                            _trackPackage();
                          }
                        },
                      ),
                    ),
                    // Submit button
                    Container(
                      width: 36,
                      height: 36,
                      margin: const EdgeInsets.only(right: 8),
                      decoration: ShapeDecoration(
                        color: AppColors.primary,
                        shape: const OvalBorder(),
                        shadows: [
                          BoxShadow(
                            color: AppColors.primary.withValues(alpha: 0.4),
                            blurRadius: 6,
                            offset: const Offset(0, 2),
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(18),
                          onTap: () {
                            if (_trackingController.text.trim().isNotEmpty) {
                              _trackPackage();
                            }
                          },
                          child: Icon(
                            Icons.arrow_forward,
                            color: AppColors.white,
                            size: _getResponsiveIconSize(context, 20),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtonsSection(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getResponsiveSpacing(context, 33)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Send Items button
          _buildActionButton(
            context,
            title: 'Send item',
            icon: Icons.send_outlined,
            onTap: _handleSendItemTap,
          ),

          SizedBox(width: _getSpacing(context, 16)),

          // Receive Items button
          _buildActionButton(
            context,
            title: 'Receive item',
            icon: Icons.inbox_outlined,
            onTap: _handleReceiveItemTap,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required String title,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: 90,
          padding: const EdgeInsets.all(10),
          decoration: ShapeDecoration(
            color: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(18),
            ),
            shadows: [
              BoxShadow(
                color: AppColors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 7, vertical: 6),
                    decoration: ShapeDecoration(
                      color: const Color(0xFFF5F5FF),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(40),
                      ),
                    ),
                    child: Icon(
                      icon,
                      size: 24,
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(width: 7),
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 17,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                        height: 1.29,
                        letterSpacing: -1,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentShipmentsSection(BuildContext context) {
    if (_hasSearched) {
      return _buildSearchResults(context);
    }

    if (_isLoading) {
      return _buildLoadingWidget(context);
    }

    return CurrentShipmentsWidget(
      title: 'Current Shipments',
      shipments: _currentShipments,
      maxShipments: 3, // Show more shipments in track view
      onSeeAllTap: () {
        // Navigate to shipment page (index 1 in bottom navigation)
        // This will trigger the parent dashboard to switch to shipment tab
        _navigateToShipmentView(context);
      },
      onShipmentTap: (shipment) {
        // Handle shipment tap - navigate to shipment details
        // TODO: Implement navigation to shipment details
      },
    );
  }

  Widget _buildSearchResults(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getResponsiveSpacing(context, 33)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Search Results',
                style: TextStyle(
                  color: AppColors.black,
                  fontSize: _getResponsiveFontSize(context, 18),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w600,
                ),
              ),
              GestureDetector(
                onTap: () {
                  setState(() {
                    _hasSearched = false;
                    _searchResults.clear();
                    _searchResultsState.clear();
                    _trackingController.clear();
                  });
                },
                child: Text(
                  'Clear',
                  style: TextStyle(
                    color: AppColors.primary,
                    fontSize: _getResponsiveFontSize(context, 14),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: _getSpacing(context, 16)),
          if (_searchResultsState.isEmpty)
            _buildEmptySearchState(context)
          else
            ..._searchResultsState.asMap().entries.map((entry) {
              final index = entry.key;
              final shipment = entry.value;

              return Padding(
                padding: EdgeInsets.only(bottom: _getSpacing(context, 16)),
                child: ShipmentCard(
                  shipment: shipment,
                  showTrackingTimeline: true,
                  onExpandToggle: () => _toggleSearchResultExpansion(index),
                  onCardTap: () {
                    // Handle shipment tap
                  },
                ),
              );
            }),
        ],
      ),
    );
  }

  Widget _buildEmptySearchState(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 32)),
      child: Column(
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: AppColors.black.withValues(alpha: 0.3),
          ),
          SizedBox(height: _getSpacing(context, 16)),
          Text(
            'No shipments found',
            style: TextStyle(
              color: AppColors.black.withValues(alpha: 0.7),
              fontSize: _getResponsiveFontSize(context, 16),
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: _getSpacing(context, 8)),
          Text(
            'Please check your tracking number and try again',
            style: TextStyle(
              color: AppColors.black.withValues(alpha: 0.5),
              fontSize: _getResponsiveFontSize(context, 14),
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getResponsiveSpacing(context, 33)),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(_getSpacing(context, 32)),
        child: Center(
          child: CircularProgressIndicator(
            color: AppColors.primary,
            strokeWidth: 2,
          ),
        ),
      ),
    );
  }

  void _trackPackage() async {
    final trackingNumber = _trackingController.text.trim();
    if (trackingNumber.isEmpty) return;

    setState(() {
      _isTracking = true;
    });

    try {
      // Search for shipments with matching tracking number
      // Use completed orders as the primary source to avoid duplicates
      final completedOrdersData = await PackageDataService.getCompletedOrders();

      // Remove duplicates by using a Set with tracking number as key
      final uniqueShipments = <String, Map<String, dynamic>>{};

      for (final shipment in completedOrdersData) {
        final trackingNum = shipment['trackingNumber']?.toString() ?? '';
        if (trackingNum.isNotEmpty) {
          uniqueShipments[trackingNum] = shipment;
        }
      }

      final matchingShipments = uniqueShipments.values
          .where((shipment) =>
            shipment['trackingNumber']?.toString().toLowerCase().contains(trackingNumber.toLowerCase()) == true)
          .map((data) => _convertToShipmentData(data))
          .toList();

      setState(() {
        _searchResults = matchingShipments;
        _searchResultsState = matchingShipments.map((shipment) => shipment.copyWith(isExpanded: false)).toList();
        _hasSearched = true;
        _isTracking = false;
      });
    } catch (e) {
      print('Error searching shipments: $e');
      setState(() {
        _searchResults = [];
        _searchResultsState = [];
        _hasSearched = true;
        _isTracking = false;
      });
    }
  }

  void _handleSendItemTap() {
    // Navigate to send package view
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SendPackageView(
          onNavigateToHome: widget.onNavigateToHome,
        ),
      ),
    );
  }

  void _handleReceiveItemTap() {
    // Show alert modal for receive items
    PushNotificationDialog.show(
      context,
      title: 'Feature Unavailable',
      description: 'This feature is currently unavailable. Would you like to send items first?',
      acceptButtonText: 'Yes',
      declineButtonText: 'No',
      icon: Icons.info_outline,
      iconBackgroundColor: AppColors.warning,
      iconColor: AppColors.warning,
      onAccept: () {
        // Navigate to send package view
        _handleSendItemTap();
      },
      onDecline: () {
        // Dialog will close automatically
      },
    );
  }

  void _navigateToShipmentView(BuildContext context) {
    // Navigate to shipment view - this would typically be handled by the parent dashboard
    // For now, we'll just show a debug message
    debugPrint('Navigate to shipment view');
    // TODO: Implement navigation to shipment view or trigger parent dashboard tab switch
  }

  /// Toggle expansion for search result shipments
  void _toggleSearchResultExpansion(int index) {
    setState(() {
      _searchResultsState[index] = _searchResultsState[index].copyWith(
        isExpanded: !_searchResultsState[index].isExpanded,
      );
    });
  }

  void _navigateToHome(BuildContext context) {
    // Use callback to switch to home tab if available
    if (widget.onNavigateToHome != null) {
      widget.onNavigateToHome!();
    } else {
      // Fallback: Try to find the parent dashboard and switch to home tab
      try {
        // Try to pop until we reach the dashboard
        Navigator.of(context).popUntil((route) => route.isFirst);
      } catch (e) {
        // If that fails, try to navigate to root
        Navigator.of(context).pushReplacementNamed('/');
      }
    }
  }

  // Responsive helper methods
  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getResponsiveFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8;
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2;
    } else {
      fontSize = baseFontSize;
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getResponsiveIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8;
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2;
    } else {
      iconSize = baseIconSize;
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getResponsiveSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.7;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.3;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.85;
    }

    return spacing;
  }
}
