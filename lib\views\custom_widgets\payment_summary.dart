import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';

/// Payment Summary Data Model
class PaymentSummaryData {
  final double shippingCost;
  final double vat;
  final double insurance;
  final double pickupCharge;
  final bool isInsuranceFree;
  final bool isPickupFree;

  const PaymentSummaryData({
    required this.shippingCost,
    this.vat = 0.0,
    this.insurance = 0.0,
    this.pickupCharge = 0.0,
    this.isInsuranceFree = true,
    this.isPickupFree = true,
  });

  double get total => shippingCost + vat + (isInsuranceFree ? 0 : insurance) + (isPickupFree ? 0 : pickupCharge);
}

/// A reusable payment summary widget
///
/// This widget displays a payment breakdown with shipping cost, VAT, insurance,
/// pickup charges, and total amount in a clean, responsive card layout.
///
/// Features:
/// - Responsive design for mobile, tablet, and smartwatch
/// - Customizable payment data
/// - Consistent styling with app theme
/// - Uses Naira (₦) currency symbol
/// - Clean card design with proper spacing
class PaymentSummary extends StatelessWidget {
  /// The payment data to display
  final PaymentSummaryData paymentData;

  /// Custom title for the payment summary
  final String title;

  /// Custom background color for the card
  final Color? backgroundColor;

  /// Custom horizontal padding
  final double? horizontalPadding;

  /// Whether to show the card shadow
  final bool showShadow;

  const PaymentSummary({
    super.key,
    required this.paymentData,
    this.title = 'Payment Summary',
    this.backgroundColor,
    this.horizontalPadding,
    this.showShadow = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: horizontalPadding ?? _getHorizontalPadding(context),
      ),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(_getContentPadding(context)),
        decoration: BoxDecoration(
          color: backgroundColor ?? AppColors.white,
          borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          boxShadow: showShadow ? [
            BoxShadow(
              color: AppColors.black.withValues(alpha: 0.1),
              blurRadius: _getSpacing(context, 8),
              offset: Offset(0, _getSpacing(context, 2)),
              spreadRadius: 0,
            ),
          ] : null,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            _buildTitle(context),

            SizedBox(height: _getSpacing(context, 24)),

            // Payment items
            _buildPaymentItem(context, 'Shipping Cost', _formatCurrency(paymentData.shippingCost)),

            SizedBox(height: _getSpacing(context, 16)),

            _buildPaymentItem(context, 'VAT', _formatCurrency(paymentData.vat)),

            SizedBox(height: _getSpacing(context, 16)),

            _buildPaymentItem(context, 'Insurance', paymentData.isInsuranceFree ? 'Free' : _formatCurrency(paymentData.insurance)),

            SizedBox(height: _getSpacing(context, 16)),

            _buildPaymentItem(context, 'Pickup Charge', paymentData.isPickupFree ? 'Free' : _formatCurrency(paymentData.pickupCharge)),

            SizedBox(height: _getSpacing(context, 20)),

            // Divider
            Container(
              height: 1,
              width: double.infinity,
              color: AppColors.black.withValues(alpha: 0.1),
            ),

            SizedBox(height: _getSpacing(context, 16)),

            // Total
            _buildTotalItem(context),
          ],
        ),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Text(
      title,
      style: TextStyle(
        color: AppColors.black,
        fontSize: _getTitleFontSize(context),
        fontFamily: 'Poppins',
        fontWeight: FontWeight.w600,
        letterSpacing: -0.5,
      ),
    );
  }

  Widget _buildPaymentItem(BuildContext context, String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          label,
          style: TextStyle(
            color: AppColors.black.withValues(alpha: 0.7),
            fontSize: _getBodyFontSize(context),
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w400,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            color: AppColors.black.withValues(alpha: 0.7),
            fontSize: _getBodyFontSize(context),
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildTotalItem(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          'Total',
          style: TextStyle(
            color: AppColors.black,
            fontSize: _getBodyFontSize(context),
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          _formatCurrency(paymentData.total),
          style: TextStyle(
            color: AppColors.primary,
            fontSize: _getBodyFontSize(context),
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w700,
          ),
        ),
      ],
    );
  }

  // Helper methods
  String _formatCurrency(double amount) {
    return '₦${amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )}';
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 40; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getContentPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 16; // Smartwatch - smaller padding
    } else if (screenWidth > 600) {
      return 28; // Tablet
    } else {
      return 24; // Mobile - standard padding
    }
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch - reduced spacing
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.0; // Tablet
    } else {
      spacing = baseSpacing; // Mobile - standard spacing
    }

    if (isShortScreen) {
      spacing = spacing * 0.8; // Reduce for short screens
    }

    return spacing;
  }

  double _getBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 16; // Smartwatch
    } else if (screenWidth > 600) {
      return 20; // Tablet
    } else {
      return 18; // Mobile
    }
  }

  double _getTitleFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 14; // Smartwatch - smaller text
    } else if (screenWidth > 600) {
      baseSize = 20; // Tablet - larger text
    } else {
      baseSize = 18; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9; // Reduce for short screens
    }

    return baseSize;
  }

  double _getBodyFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10; // Smartwatch - very small text
    } else if (screenWidth > 600) {
      baseSize = 16; // Tablet - larger text
    } else {
      baseSize = 14; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9; // Reduce for short screens
    }

    return baseSize;
  }
}