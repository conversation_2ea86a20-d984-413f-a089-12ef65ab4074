import 'dart:async';
import 'package:rideoon/services/api_service.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/models/api_response.dart';
import 'package:rideoon/models/order/collection.dart';
import 'package:rideoon/models/order/package.dart';

/// Service class for handling order/collection-related API calls
/// 
/// This service provides methods for:
/// - Creating and managing orders (collections)
/// - Managing packages within orders
/// - Managing addresses for orders and packages
/// - Getting order pricing
/// - Retrieving order lists and details
class OrderApiService {
  
  /// Create a new order (collection)
  /// POST /v1/auth/client/order/new
  static Future<ApiResponse<Collection>> createOrder(
    CreateCollectionRequest request, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();
    
    return await ApiService.makeRequest<Collection>(
      endpoint: '/v1/auth/client/order/new',
      method: 'POST',
      body: request.toJson(),
      requiresAuth: true,
      authToken: token,
      fromJson: (json) => Collection.fromJson(json),
    );
  }

  /// Get order details by UUID
  /// GET /v1/auth/client/order/{orderUuid}
  static Future<ApiResponse<Collection>> getOrder(
    String orderUuid, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();
    
    return await ApiService.makeRequest<Collection>(
      endpoint: '/v1/auth/client/order/$orderUuid',
      method: 'GET',
      requiresAuth: true,
      authToken: token,
      fromJson: (json) => Collection.fromJson(json),
    );
  }

  /// Update order details
  /// PUT /v1/auth/client/order/{orderUuid}
  static Future<ApiResponse<Collection>> updateOrder(
    String orderUuid,
    UpdateCollectionRequest request, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();
    
    return await ApiService.makeRequest<Collection>(
      endpoint: '/v1/auth/client/order/$orderUuid',
      method: 'PUT',
      body: request.toJson(),
      requiresAuth: true,
      authToken: token,
      fromJson: (json) => Collection.fromJson(json),
    );
  }

  /// Get all orders for the authenticated user
  /// GET /v1/auth/client/orders
  static Future<ApiResponse<List<Collection>>> getOrders({
    Map<String, String>? queryParams,
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();
    
    return await ApiService.makeRequest<List<Collection>>(
      endpoint: '/v1/auth/client/orders',
      method: 'GET',
      queryParams: queryParams,
      requiresAuth: true,
      authToken: token,
      fromJson: (json) {
        final List<dynamic> ordersJson = json['orders'] ?? json['data'] ?? [];
        return ordersJson.map((orderJson) => Collection.fromJson(orderJson)).toList();
      },
    );
  }

  /// Get packages for a specific order
  /// GET /v1/auth/client/order/{orderUuid}/packages
  static Future<ApiResponse<List<Package>>> getOrderPackages(
    String orderUuid, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();
    
    return await ApiService.makeRequest<List<Package>>(
      endpoint: '/v1/auth/client/order/$orderUuid/packages',
      method: 'GET',
      requiresAuth: true,
      authToken: token,
      fromJson: (json) {
        final List<dynamic> packagesJson = json['packages'] ?? json['data'] ?? [];
        return packagesJson.map((packageJson) => Package.fromJson(packageJson)).toList();
      },
    );
  }

  /// Get specific package details
  /// GET /v1/auth/client/order/{orderUuid}/packages/{packageUuid}
  static Future<ApiResponse<Package>> getPackage(
    String orderUuid,
    String packageUuid, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();
    
    return await ApiService.makeRequest<Package>(
      endpoint: '/v1/auth/client/order/$orderUuid/packages/$packageUuid',
      method: 'GET',
      requiresAuth: true,
      authToken: token,
      fromJson: (json) => Package.fromJson(json),
    );
  }

  /// Update package details
  /// PUT /v1/auth/client/order/{orderUuid}/packages/{packageUuid}
  static Future<ApiResponse<Package>> updatePackage(
    String orderUuid,
    String packageUuid,
    UpdatePackageRequest request, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();
    
    return await ApiService.makeRequest<Package>(
      endpoint: '/v1/auth/client/order/$orderUuid/packages/$packageUuid',
      method: 'PUT',
      body: request.toJson(),
      requiresAuth: true,
      authToken: token,
      fromJson: (json) => Package.fromJson(json),
    );
  }

  /// Get order pricing
  /// GET /v1/auth/client/order/{orderUuid}/price
  static Future<ApiResponse<Map<String, dynamic>>> getOrderPrice(
    String orderUuid, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();
    
    return await ApiService.makeRequest<Map<String, dynamic>>(
      endpoint: '/v1/auth/client/order/$orderUuid/price',
      method: 'GET',
      requiresAuth: true,
      authToken: token,
      fromJson: (json) => json,
    );
  }

  /// Get addresses for an order by type
  /// GET /v1/auth/client/order/{orderUuid}/addresses/{addressType}
  static Future<ApiResponse<List<Map<String, dynamic>>>> getOrderAddresses(
    String orderUuid,
    String addressType, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();
    
    return await ApiService.makeRequest<List<Map<String, dynamic>>>(
      endpoint: '/v1/auth/client/order/$orderUuid/addresses/$addressType',
      method: 'GET',
      requiresAuth: true,
      authToken: token,
      fromJson: (json) {
        final List<dynamic> addressesJson = json['addresses'] ?? json['data'] ?? [];
        return addressesJson.cast<Map<String, dynamic>>();
      },
    );
  }

  /// Get specific address for an order
  /// GET /v1/auth/client/order/{orderUuid}/address/{addressUuid}
  static Future<ApiResponse<Map<String, dynamic>>> getOrderAddress(
    String orderUuid,
    String addressUuid, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();
    
    return await ApiService.makeRequest<Map<String, dynamic>>(
      endpoint: '/v1/auth/client/order/$orderUuid/address/$addressUuid',
      method: 'GET',
      requiresAuth: true,
      authToken: token,
      fromJson: (json) => json,
    );
  }

  /// Create or add address to order
  /// POST /v1/auth/client/order/{orderUuid}/address/{addressType}/{createOrAdd}
  static Future<ApiResponse<Map<String, dynamic>>> createOrAddOrderAddress(
    String orderUuid,
    String addressType,
    String createOrAdd,
    Map<String, dynamic> addressData, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();
    
    return await ApiService.makeRequest<Map<String, dynamic>>(
      endpoint: '/v1/auth/client/order/$orderUuid/address/$addressType/$createOrAdd',
      method: 'POST',
      body: addressData,
      requiresAuth: true,
      authToken: token,
      fromJson: (json) => json,
    );
  }

  /// Set address for package
  /// PUT /v1/auth/client/order/{orderUuid}/{packageUuid}/address/{addressUuid}
  static Future<ApiResponse<Map<String, dynamic>>> setPackageAddress(
    String orderUuid,
    String packageUuid,
    String addressUuid, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();
    
    return await ApiService.makeRequest<Map<String, dynamic>>(
      endpoint: '/v1/auth/client/order/$orderUuid/$packageUuid/address/$addressUuid',
      method: 'PUT',
      requiresAuth: true,
      authToken: token,
      fromJson: (json) => json,
    );
  }

  /// Get addresses for a package by type
  /// GET /v1/auth/client/order/{orderUuid}/{packageUuid}/addresses/{addressType}
  static Future<ApiResponse<List<Map<String, dynamic>>>> getPackageAddresses(
    String orderUuid,
    String packageUuid,
    String addressType, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();
    
    return await ApiService.makeRequest<List<Map<String, dynamic>>>(
      endpoint: '/v1/auth/client/order/$orderUuid/$packageUuid/addresses/$addressType',
      method: 'GET',
      requiresAuth: true,
      authToken: token,
      fromJson: (json) {
        final List<dynamic> addressesJson = json['addresses'] ?? json['data'] ?? [];
        return addressesJson.cast<Map<String, dynamic>>();
      },
    );
  }

  // ============================================================================
  // NEW ORDER CREATION AND MANAGEMENT ENDPOINTS
  // ============================================================================

  /// Create a new order using the new API format
  /// POST /v1/auth/client/order/new
  static Future<ApiResponse<Map<String, dynamic>>> createNewOrder(
    Map<String, dynamic> orderData, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();

    return await ApiService.makeRequest<Map<String, dynamic>>(
      endpoint: '/v1/auth/client/order/new',
      method: 'POST',
      body: orderData,
      requiresAuth: true,
      authToken: token,
      fromJson: (json) => json,
    );
  }

  /// Create or update an order with specific UUID
  /// POST /v1/auth/client/order/{orderUuid}
  static Future<ApiResponse<Map<String, dynamic>>> createOrUpdateOrder(
    String orderUuid,
    Map<String, dynamic> orderData, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();

    return await ApiService.makeRequest<Map<String, dynamic>>(
      endpoint: '/v1/auth/client/order/$orderUuid',
      method: 'POST',
      body: orderData,
      requiresAuth: true,
      authToken: token,
      fromJson: (json) => json,
    );
  }

  /// Get order details using the new API format
  /// GET /v1/auth/client/order/{orderUuid}
  static Future<ApiResponse<Map<String, dynamic>>> getOrderDetails(
    String orderUuid, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();

    return await ApiService.makeRequest<Map<String, dynamic>>(
      endpoint: '/v1/auth/client/order/$orderUuid',
      method: 'GET',
      requiresAuth: true,
      authToken: token,
      fromJson: (json) => json,
    );
  }

  /// Update multiple packages on an order
  /// POST /v1/auth/client/order/{orderUuid}/packages
  static Future<ApiResponse<List<Map<String, dynamic>>>> updateOrderPackages(
    String orderUuid,
    List<Map<String, dynamic>> packages, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();

    // For now, we'll send the packages as a wrapper object
    // TODO: Modify ApiService to handle list bodies directly
    return await ApiService.makeRequest<List<Map<String, dynamic>>>(
      endpoint: '/v1/auth/client/order/$orderUuid/packages',
      method: 'POST',
      body: {'packages': packages},
      requiresAuth: true,
      authToken: token,
      fromJson: (json) => (json as List).cast<Map<String, dynamic>>(),
    );
  }

  /// Update a single package on an order
  /// POST /v1/auth/client/order/{orderUuid}/packages/{packageUuid}
  static Future<ApiResponse<Map<String, dynamic>>> updateOrderPackage(
    String orderUuid,
    String packageUuid,
    Map<String, dynamic> packageData, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();

    return await ApiService.makeRequest<Map<String, dynamic>>(
      endpoint: '/v1/auth/client/order/$orderUuid/packages/$packageUuid',
      method: 'POST',
      body: packageData,
      requiresAuth: true,
      authToken: token,
      fromJson: (json) => json,
    );
  }

  /// Delete packages from an order
  /// DELETE /v1/auth/client/order/{orderUuid}/packages/{packageUuids}
  static Future<ApiResponse<bool>> deleteOrderPackages(
    String orderUuid,
    String packageUuids, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();

    return await ApiService.makeRequest<bool>(
      endpoint: '/v1/auth/client/order/$orderUuid/packages/$packageUuids',
      method: 'DELETE',
      requiresAuth: true,
      authToken: token,
      fromJson: (json) => true,
    );
  }
}
