# Environment Variables Setup Guide

This guide explains how to use environment variables in your RideOn Flutter app.

## Overview

The app now supports environment-based configuration using the `flutter_dotenv` package. This allows you to:
- Store sensitive information like API keys securely
- Use different configurations for development, staging, and production
- Easily switch between different environments
- Keep configuration separate from code

## Files Structure

```
├── .env                    # Development environment (not committed)
├── .env.example           # Template file (committed)
├── .env.staging           # Staging environment (not committed)
├── .env.production        # Production environment (not committed)
├── lib/services/config_service.dart  # Configuration service
└── lib/services/api_service.dart     # Example usage
```

## Setup Instructions

### 1. Initial Setup

The environment system is already configured. The `.env` file contains development defaults.

### 2. Configure Your Environment Variables

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` with your actual values:
   ```bash
   # Replace placeholder values with real ones
   API_KEY=your_actual_api_key_here
   GOOGLE_MAPS_API_KEY=your_google_maps_key
   # ... etc
   ```

### 3. Environment-Specific Configuration

For different environments, create or modify:
- `.env` - Development (local development)
- `.env.staging` - Staging environment
- `.env.production` - Production environment

### 4. Switching Environments

To use a different environment file, modify the `ConfigService.initialize()` call in `main.dart`:

```dart
// For staging
await ConfigService.initialize(fileName: ".env.staging");

// For production
await ConfigService.initialize(fileName: ".env.production");
```

## Available Configuration Variables

### App Configuration
- `APP_NAME` - Application name
- `APP_VERSION` - Application version
- `APP_ENVIRONMENT` - Current environment (development/staging/production)

### API Configuration
- `API_BASE_URL` - Base URL for your API
- `API_TIMEOUT` - Request timeout in milliseconds
- `API_KEY` - API authentication key

### Authentication
- `JWT_SECRET` - JWT secret for token validation
- `OAUTH_CLIENT_ID` - OAuth client ID
- `OAUTH_CLIENT_SECRET` - OAuth client secret

### Third-party Services
- `GOOGLE_MAPS_API_KEY` - Google Maps API key
- `FIREBASE_PROJECT_ID` - Firebase project ID
- `STRIPE_PUBLISHABLE_KEY` - Stripe publishable key
- `STRIPE_SECRET_KEY` - Stripe secret key

### Feature Flags
- `ENABLE_DEBUG_MODE` - Enable/disable debug mode (true/false)
- `ENABLE_ANALYTICS` - Enable/disable analytics (true/false)
- `ENABLE_CRASH_REPORTING` - Enable/disable crash reporting (true/false)

### Logging
- `LOG_LEVEL` - Logging level (debug/info/warning/error)
- `ENABLE_REMOTE_LOGGING` - Enable remote logging (true/false)

## Usage in Code

### Basic Usage

```dart
import 'package:rideoon/services/config_service.dart';

// Get configuration values
String apiUrl = ConfigService.apiBaseUrl;
String apiKey = ConfigService.apiKey;
bool isDebug = ConfigService.enableDebugMode;

// Check environment
if (ConfigService.isDevelopment) {
  print('Running in development mode');
}
```

### API Service Example

```dart
import 'package:rideoon/services/api_service.dart';

// The ApiService automatically uses environment configuration
final userProfile = await ApiService.getUserProfile('user123');
```

### Custom Variables

```dart
// Get a custom environment variable
String? customValue = ConfigService.getCustomValue('MY_CUSTOM_VAR');

// Check if a variable exists
bool hasCustomVar = ConfigService.hasValue('MY_CUSTOM_VAR');
```

## Security Best Practices

1. **Never commit sensitive `.env` files** - They're already in `.gitignore`
2. **Use different keys for different environments**
3. **Rotate API keys regularly**
4. **Use the least privileged access principle**
5. **Validate required variables** - The app will throw an error if required production variables are missing

## Troubleshooting

### Common Issues

1. **App crashes on startup**
   - Check that `.env` file exists
   - Verify all required variables are set for production builds

2. **Environment variables not loading**
   - Ensure `.env` is listed in `pubspec.yaml` assets
   - Check file encoding (should be UTF-8)
   - Verify file path is correct

3. **API calls failing**
   - Check `API_BASE_URL` and `API_KEY` values
   - Verify network connectivity
   - Check API service configuration

### Debug Mode

In development, you can view all environment variables:

```dart
if (ConfigService.isDevelopment) {
  final allVars = ConfigService.getAllValues();
  print('All environment variables: $allVars');
}
```

## Adding New Configuration Variables

1. Add the variable to all `.env*` files
2. Add a getter method in `ConfigService`
3. Update this documentation
4. Add validation if the variable is required

Example:
```dart
// In ConfigService
static String get newConfigValue => dotenv.env['NEW_CONFIG_VALUE'] ?? 'default';
```

## Deployment

### Development
- Use `.env` with development values
- Debug mode enabled
- Local API endpoints

### Staging
- Use `.env.staging`
- Production-like environment
- Staging API endpoints
- Analytics enabled for testing

### Production
- Use `.env.production`
- Debug mode disabled
- Production API endpoints
- All monitoring enabled
- Required variables validation enforced
