import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/shipment_card.dart';
import 'package:rideoon/services/package_data_service.dart';

/// Shipment view page for user dashboard
///
/// This page displays current shipments and shipment history with a tabbed interface.
/// Users can view detailed shipment information and track their packages.
class ShipmentView extends StatefulWidget {
  /// Callback function to navigate back to home tab
  final VoidCallback? onNavigateToHome;

  /// Whether the parent dashboard is showing a header
  final bool hasHeader;

  /// Callback to set the filter callback in parent
  final Function(VoidCallback)? onFilterCallback;

  const ShipmentView({
    super.key,
    this.onNavigateToHome,
    this.hasHeader = false,
    this.onFilterCallback,
  });

  @override
  State<ShipmentView> createState() => _ShipmentViewState();
}

class _ShipmentViewState extends State<ShipmentView> {
  int _selectedTabIndex = 0;
  bool _isFilterActive = false;
  List<ShipmentData> _currentShipmentsState = [];
  List<ShipmentData> _shipmentHistoryState = [];

  // Public method to show filter options (called from parent dashboard)
  void showFilterOptions() {
    print('showFilterOptions called in ShipmentView'); // Debug
    setState(() {
      _isFilterActive = !_isFilterActive;
    });
    _showFilterOptions(context);
  }

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadShipmentData();

    // Set the filter callback in parent if provided
    if (widget.onFilterCallback != null) {
      print('Setting filter callback in ShipmentView'); // Debug
      widget.onFilterCallback!(showFilterOptions);
    } else {
      print('No filter callback provided to ShipmentView'); // Debug
    }
  }

  /// Load shipment data from local storage
  Future<void> _loadShipmentData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Load current shipments
      final currentShipmentsData = await PackageDataService.getCurrentShipments();
      final currentShipments = currentShipmentsData.map((data) => _convertToShipmentData(data)).toList();

      // Load completed orders for history
      final completedOrdersData = await PackageDataService.getCompletedOrders();
      final completedShipments = completedOrdersData
          .where((data) => data['status'] == 'completed')
          .map((data) => _convertToShipmentData(data))
          .toList();

      setState(() {
        _currentShipmentsState = currentShipments.map((shipment) => shipment.copyWith(isExpanded: false)).toList();
        _shipmentHistoryState = completedShipments.map((shipment) => shipment.copyWith(isExpanded: false)).toList();
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading shipment data: $e');
      setState(() {
        _currentShipmentsState = [];
        _shipmentHistoryState = [];
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top spacing (adjust based on whether header is shown)
          SizedBox(height: widget.hasHeader
            ? MediaQuery.of(context).padding.top + 95 // Space for header + underline
            : MediaQuery.of(context).size.height * 0.07), // Original spacing

          // Divider (only show if no header)
          if (!widget.hasHeader) _buildDivider(context),

          SizedBox(height: _getSpacing(context, 24)),

          // Tab buttons
          _buildTabButtons(context),

          SizedBox(height: _getSpacing(context, 24)),

          // Tab content
          Expanded(
            child: _buildTabContent(context),
          ),
        ],
      ),
    );
  }

  Widget _buildTabButtons(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Row(
        children: [
          // Current Shipment tab
          Expanded(
            child: _buildTabButton(
              context,
              title: 'Current Shipment',
              isSelected: _selectedTabIndex == 0,
              onTap: () => setState(() => _selectedTabIndex = 0),
            ),
          ),

          SizedBox(width: _getSpacing(context, 8)),

          // Shipment History tab
          Expanded(
            child: _buildTabButton(
              context,
              title: 'Shipment History',
              isSelected: _selectedTabIndex == 1,
              onTap: () => setState(() => _selectedTabIndex = 1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabButton(
    BuildContext context, {
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: _getSpacing(context, 15)),
        decoration: ShapeDecoration(
          color: isSelected ? AppColors.primary : AppColors.white,
          shape: RoundedRectangleBorder(
            side: BorderSide(
              width: 1,
              color: isSelected
                  ? AppColors.primary
                  : const Color(0x0A1E1E1E),
            ),
            borderRadius: BorderRadius.circular(18),
          ),
        ),
        child: Center(
          child: Text(
            title,
            style: TextStyle(
              color: isSelected ? AppColors.white : const Color(0xFF111111),
              fontSize: 12,
              fontFamily: 'Inter',
              fontWeight: FontWeight.w400,
              height: 2.09,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabContent(BuildContext context) {
    switch (_selectedTabIndex) {
      case 0:
        return _buildCurrentShipmentsTab(context);
      case 1:
        return _buildShipmentHistoryTab(context);
      default:
        return _buildCurrentShipmentsTab(context);
    }
  }

  Widget _buildCurrentShipmentsTab(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingWidget(context);
    }

    if (_currentShipmentsState.isEmpty) {
      return _buildEmptyState(
        context,
        title: 'Currently Empty',
        subtitle: 'No current shipments available',
      );
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(height: _getSpacing(context, 20)),
            ..._currentShipmentsState.asMap().entries.map((entry) {
              final index = entry.key;
              final shipment = entry.value;

              return Padding(
                padding: EdgeInsets.only(bottom: _getSpacing(context, 16)),
                child: ShipmentCard(
                  shipment: shipment,
                  showTrackingTimeline: true, // Current shipments show timeline
                  onExpandToggle: () => _toggleCurrentShipmentExpansion(index),
                  onCardTap: () {
                    // Handle current shipment tap - navigate to shipment details
                    // TODO: Implement navigation to shipment details
                  },
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildShipmentHistoryTab(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingWidget(context);
    }

    if (_shipmentHistoryState.isEmpty) {
      return _buildEmptyState(
        context,
        title: 'No History',
        subtitle: 'No shipment history available',
      );
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(height: _getSpacing(context, 20)),
            ..._shipmentHistoryState.asMap().entries.map((entry) {
              final index = entry.key;
              final shipment = entry.value;

              return Padding(
                padding: EdgeInsets.only(bottom: _getSpacing(context, 16)),
                child: ShipmentCard(
                  shipment: shipment,
                  showTrackingTimeline: true, // History also shows timeline with view details
                  onExpandToggle: () => _toggleHistoryShipmentExpansion(index),
                  onCardTap: () {
                    // Handle history shipment tap - navigate to shipment details
                    // TODO: Implement navigation to shipment details
                  },
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(
    BuildContext context, {
    required String title,
    required String subtitle,
  }) {
    return Padding(
      padding: EdgeInsets.all(_getHorizontalPadding(context)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Empty state illustration - centered
          Center(
            child: Container(
              width: 149,
              height: 155,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Image.asset(
                'assets/icons/empty.png',
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  // Fallback if image doesn't exist - centered
                  return Container(
                    decoration: BoxDecoration(
                      color: AppColors.black.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Center(
                      child: Icon(
                        Icons.inventory_2_outlined,
                        size: 48,
                        color: AppColors.black.withValues(alpha: 0.3),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          SizedBox(height: _getSpacing(context, 24)),

          Text(
            title,
            style: TextStyle(
              color: AppColors.black.withValues(alpha: 0.74),
              fontSize: 16,
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
              height: 0.95,
              letterSpacing: -0.80,
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: _getSpacing(context, 8)),

          Text(
            subtitle,
            style: TextStyle(
              color: AppColors.black.withValues(alpha: 0.5),
              fontSize: 14,
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }





  Widget _buildSortIcon(BuildContext context) {
    final iconSize = _getResponsiveSpacing(context, 20);
    final color = _isFilterActive ? AppColors.primary : AppColors.black;

    return SizedBox(
      width: iconSize,
      height: iconSize,
      child: CustomPaint(
        painter: SortIconPainter(color: color),
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getResponsiveSpacing(context, 33)),
      child: Container(
        width: _getResponsiveSpacing(context, 324),
        decoration: ShapeDecoration(
          shape: RoundedRectangleBorder(
            side: BorderSide(
              width: 1,
              strokeAlign: BorderSide.strokeAlignCenter,
              color: AppColors.black.withValues(alpha: 0.1),
            ),
          ),
        ),
      ),
    );
  }

  void _showFilterOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(_getResponsiveSpacing(context, 24)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Filter & Sort',
              style: TextStyle(
                fontSize: _getResponsiveFontSize(context, 18),
                fontWeight: FontWeight.w600,
                fontFamily: 'Poppins',
                color: AppColors.black,
              ),
            ),
            SizedBox(height: _getResponsiveSpacing(context, 20)),

            // Sort options
            _buildFilterOption(context, 'Date (Newest First)', true),
            _buildFilterOption(context, 'Date (Oldest First)', false),
            _buildFilterOption(context, 'Status (Active First)', false),
            _buildFilterOption(context, 'Status (Completed First)', false),

            SizedBox(height: _getResponsiveSpacing(context, 20)),

            // Apply button
            SizedBox(
              width: double.infinity,
              height: _getResponsiveSpacing(context, 48),
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  setState(() {
                    _isFilterActive = false;
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: AppColors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'Apply Filter',
                  style: TextStyle(
                    fontSize: _getResponsiveFontSize(context, 16),
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Poppins',
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterOption(BuildContext context, String title, bool isSelected) {
    return GestureDetector(
      onTap: () {
        // Handle filter selection
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: _getResponsiveSpacing(context, 12)),
        child: Row(
          children: [
            Icon(
              isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: isSelected ? AppColors.primary : AppColors.black.withValues(alpha: 0.5),
              size: _getResponsiveSpacing(context, 20),
            ),
            SizedBox(width: _getResponsiveSpacing(context, 12)),
            Text(
              title,
              style: TextStyle(
                fontSize: _getResponsiveFontSize(context, 14),
                fontWeight: FontWeight.w400,
                fontFamily: 'Poppins',
                color: AppColors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleCurrentShipmentExpansion(int index) {
    setState(() {
      _currentShipmentsState[index] = _currentShipmentsState[index].copyWith(
        isExpanded: !_currentShipmentsState[index].isExpanded,
      );
    });
  }

  void _toggleHistoryShipmentExpansion(int index) {
    setState(() {
      _shipmentHistoryState[index] = _shipmentHistoryState[index].copyWith(
        isExpanded: !_shipmentHistoryState[index].isExpanded,
      );
    });
  }

  void _navigateToHome(BuildContext context) {
    // Use callback to switch to home tab if available
    if (widget.onNavigateToHome != null) {
      widget.onNavigateToHome!();
    } else {
      // Fallback: Try to find the parent dashboard and switch to home tab
      try {
        // Try to pop until we reach the dashboard
        Navigator.of(context).popUntil((route) => route.isFirst);
      } catch (e) {
        // If that fails, try to navigate to root
        Navigator.of(context).pushReplacementNamed('/');
      }
    }
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 16;
    if (screenWidth > 600) return 40;
    return 24;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getResponsiveSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.7;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.3;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.85;
    }

    return spacing;
  }

  double _getResponsiveFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8;
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2;
    } else {
      fontSize = baseFontSize;
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }
}

/// Custom painter for the sort icon with three horizontal lines that get progressively shorter
class SortIconPainter extends CustomPainter {
  final Color color;

  SortIconPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round;

    final centerY = size.height / 2;
    final spacing = size.height / 6;

    // First line (longest)
    final firstLineY = centerY - spacing;
    canvas.drawLine(
      Offset(0, firstLineY),
      Offset(size.width, firstLineY),
      paint,
    );

    // Second line (medium)
    final secondLineY = centerY;
    final secondLineWidth = size.width * 0.75;
    final secondLineStart = (size.width - secondLineWidth) / 2;
    canvas.drawLine(
      Offset(secondLineStart, secondLineY),
      Offset(secondLineStart + secondLineWidth, secondLineY),
      paint,
    );

    // Third line (shortest)
    final thirdLineY = centerY + spacing;
    final thirdLineWidth = size.width * 0.5;
    final thirdLineStart = (size.width - thirdLineWidth) / 2;
    canvas.drawLine(
      Offset(thirdLineStart, thirdLineY),
      Offset(thirdLineStart + thirdLineWidth, thirdLineY),
      paint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return oldDelegate is SortIconPainter && oldDelegate.color != color;
  }
}

/// Helper methods for ShipmentView
extension ShipmentViewHelpers on _ShipmentViewState {
  /// Convert stored data to ShipmentData
  ShipmentData _convertToShipmentData(Map<String, dynamic> data) {
    final status = _getShipmentStatus(data['status'] ?? 'pending');
    final trackingSteps = _generateTrackingSteps(status, data);

    // Get title from cargo items or package data
    String title = 'Package';
    if (data['cargoItems'] != null && (data['cargoItems'] as List).isNotEmpty) {
      final firstItem = (data['cargoItems'] as List).first;
      title = firstItem['itemName'] ?? 'Package';
    } else if (data['packageData'] != null) {
      final packageData = data['packageData'] as Map<String, dynamic>;
      if (packageData['packageDetails'] != null) {
        final packageDetails = packageData['packageDetails'] as Map<String, dynamic>;
        title = packageDetails['itemName'] ?? packageDetails['category'] ?? 'Package';
      }
    }

    return ShipmentData(
      id: data['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      trackingNumber: data['trackingNumber'] ?? '#RO00000',
      status: status,
      trackingSteps: trackingSteps,
    );
  }

  /// Get shipment status from string
  ShipmentStatus _getShipmentStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return ShipmentStatus.pending;
      case 'in_progress':
      case 'inprogress':
        return ShipmentStatus.inProgress;
      case 'completed':
        return ShipmentStatus.completed;
      case 'cancelled':
        return ShipmentStatus.cancelled;
      default:
        return ShipmentStatus.pending;
    }
  }

  /// Generate tracking steps based on status and data
  List<TrackingStep> _generateTrackingSteps(ShipmentStatus status, Map<String, dynamic> data) {
    final steps = <TrackingStep>[];

    // Get pickup and receiver addresses
    String pickupAddress = '';
    String receiverAddress = '';

    if (data['pickupData'] != null) {
      final pickup = data['pickupData'] as Map<String, dynamic>;
      pickupAddress = '${pickup['fullAddress'] ?? ''}, ${pickup['state'] ?? ''}';
    }

    if (data['receiverData'] != null) {
      final receiver = data['receiverData'] as Map<String, dynamic>;
      receiverAddress = '${receiver['address'] ?? ''}, ${receiver['state'] ?? ''}';
    }

    // Add steps based on status
    steps.add(TrackingStep(
      title: 'Order placed',
      time: _formatTimestamp(data['timestamp']),
      isCompleted: true,
    ));

    if (status == ShipmentStatus.inProgress || status == ShipmentStatus.completed) {
      steps.add(TrackingStep(
        title: 'Rider picked up package from sender location',
        address: pickupAddress.isNotEmpty ? pickupAddress : null,
        isCompleted: true,
      ));

      steps.add(TrackingStep(
        title: 'Package in transit',
        isCompleted: status == ShipmentStatus.completed,
      ));
    }

    if (status == ShipmentStatus.completed) {
      steps.add(TrackingStep(
        title: 'Package delivered to destination',
        address: receiverAddress.isNotEmpty ? receiverAddress : null,
        isCompleted: true,
      ));
    } else if (status == ShipmentStatus.pending) {
      steps.add(TrackingStep(
        title: 'Awaiting pickup',
        isCompleted: false,
      ));
    }

    return steps;
  }

  /// Format timestamp for display
  String? _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return null;

    try {
      final dateTime = timestamp is int
          ? DateTime.fromMillisecondsSinceEpoch(timestamp)
          : DateTime.parse(timestamp.toString());

      return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return null;
    }
  }

  /// Build loading widget
  Widget _buildLoadingWidget(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(_getResponsiveSpacing(context, 24)),
        child: Center(
          child: CircularProgressIndicator(
            color: AppColors.primary,
            strokeWidth: 2,
          ),
        ),
      ),
    );
  }
}
