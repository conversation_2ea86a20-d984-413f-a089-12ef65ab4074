import 'package:rideoon/models/core/address.dart';
import 'package:rideoon/models/core/contact.dart';
import 'package:rideoon/models/shipment/package.dart';
import 'package:rideoon/models/shipment/shipment_status.dart';
import 'package:rideoon/models/shipment/tracking.dart';
import 'package:rideoon/models/payment/payment.dart';

/// Shipment priority enum
enum ShipmentPriority {
  low('Low'),
  normal('Normal'),
  high('High'),
  urgent('Urgent');

  const ShipmentPriority(this.displayName);
  final String displayName;

  /// Get priority from string
  static ShipmentPriority fromString(String value) {
    return ShipmentPriority.values.firstWhere(
      (priority) => priority.displayName.toLowerCase() == value.toLowerCase(),
      orElse: () => ShipmentPriority.normal,
    );
  }
}

/// Complete shipment model
class Shipment {
  final String id;
  final String trackingNumber;
  final Contact sender;
  final Address pickupAddress;
  final Contact receiver;
  final Address deliveryAddress;
  final List<Package> packages;
  final ShipmentStatus status;
  final ShipmentPriority priority;
  final DeliveryType deliveryType;
  final Payment? payment;
  final TrackingTimeline? tracking;
  final DateTime createdAt;
  final DateTime? scheduledPickupAt;
  final DateTime? scheduledDeliveryAt;
  final DateTime? actualPickupAt;
  final DateTime? actualDeliveryAt;
  final String? specialInstructions;
  final String? driverId;
  final String? driverName;
  final String? driverPhone;
  final Map<String, dynamic>? metadata;

  const Shipment({
    required this.id,
    required this.trackingNumber,
    required this.sender,
    required this.pickupAddress,
    required this.receiver,
    required this.deliveryAddress,
    required this.packages,
    required this.status,
    this.priority = ShipmentPriority.normal,
    this.deliveryType = DeliveryType.standard,
    this.payment,
    this.tracking,
    required this.createdAt,
    this.scheduledPickupAt,
    this.scheduledDeliveryAt,
    this.actualPickupAt,
    this.actualDeliveryAt,
    this.specialInstructions,
    this.driverId,
    this.driverName,
    this.driverPhone,
    this.metadata,
  });

  /// Get total weight of all packages
  double get totalWeight => packages.fold(0.0, (sum, package) => sum + package.totalWeight);

  /// Get total number of items
  int get totalItems => packages.fold(0, (sum, package) => sum + package.quantity);

  /// Get estimated delivery date
  DateTime? get estimatedDeliveryDate {
    if (scheduledDeliveryAt != null) return scheduledDeliveryAt;
    
    // Calculate based on delivery type and creation date
    final baseDate = actualPickupAt ?? createdAt;
    switch (deliveryType) {
      case DeliveryType.sameDay:
        return baseDate.add(const Duration(hours: 8));
      case DeliveryType.overnight:
        return baseDate.add(const Duration(days: 1));
      case DeliveryType.express:
        return baseDate.add(const Duration(days: 2));
      case DeliveryType.standard:
        return baseDate.add(const Duration(days: 3));
    }
  }

  /// Check if shipment is overdue
  bool get isOverdue {
    final estimated = estimatedDeliveryDate;
    if (estimated == null || status.isCompleted) return false;
    return DateTime.now().isAfter(estimated);
  }

  /// Get shipment duration (from creation to delivery)
  Duration? get duration {
    if (actualDeliveryAt != null) {
      return actualDeliveryAt!.difference(createdAt);
    }
    return null;
  }

  /// Get primary package (first package or largest by weight)
  Package get primaryPackage {
    if (packages.isEmpty) {
      throw StateError('Shipment must have at least one package');
    }
    if (packages.length == 1) return packages.first;
    
    // Return package with highest total weight
    return packages.reduce((a, b) => a.totalWeight > b.totalWeight ? a : b);
  }

  /// Get shipment title for display
  String get title {
    if (packages.length == 1) {
      return packages.first.itemName;
    } else {
      return '${packages.length} items';
    }
  }

  /// Get shipment summary for display
  String get summary {
    final parts = <String>[
      title,
      '${totalWeight}kg',
      deliveryType.displayName,
    ];
    return parts.join(' • ');
  }

  /// Check if shipment has driver assigned
  bool get hasDriver => driverId?.isNotEmpty == true;

  /// Check if shipment can be cancelled
  bool get canBeCancelled => status.canBeCancelled;

  /// Check if shipment can be tracked
  bool get canBeTracked => status.canBeTracked;

  /// Create Shipment from JSON
  factory Shipment.fromJson(Map<String, dynamic> json) {
    return Shipment(
      id: json['id'] as String? ?? '',
      trackingNumber: json['trackingNumber'] as String? ?? '',
      sender: Contact.fromJson(json['sender'] as Map<String, dynamic>? ?? 
                              json['pickupData'] as Map<String, dynamic>? ?? {}),
      pickupAddress: Address.fromJson(json['pickupAddress'] as Map<String, dynamic>? ?? 
                                     json['pickupData'] as Map<String, dynamic>? ?? {}),
      receiver: Contact.fromJson(json['receiver'] as Map<String, dynamic>? ?? 
                                json['receiverData'] as Map<String, dynamic>? ?? {}),
      deliveryAddress: Address.fromJson(json['deliveryAddress'] as Map<String, dynamic>? ?? 
                                       json['receiverData'] as Map<String, dynamic>? ?? {}),
      packages: _parsePackages(json),
      status: ShipmentStatus.fromString(json['status'] as String? ?? 'pending'),
      priority: ShipmentPriority.fromString(json['priority'] as String? ?? 'normal'),
      deliveryType: DeliveryType.fromString(json['deliveryType'] as String? ?? 'standard'),
      payment: json['payment'] != null 
          ? Payment.fromJson(json['payment'] as Map<String, dynamic>)
          : json['paymentData'] != null
              ? Payment.fromJson({
                  'id': json['id'] ?? '',
                  'shipmentId': json['id'] ?? '',
                  'breakdown': json['paymentData'],
                  'method': 'cash',
                  'status': 'completed',
                  'createdAt': json['createdAt'] ?? DateTime.now().toIso8601String(),
                })
              : null,
      tracking: json['tracking'] != null 
          ? TrackingTimeline.fromJson(json['tracking'] as Map<String, dynamic>)
          : null,
      createdAt: DateTime.parse(json['createdAt'] as String? ?? 
                               json['orderDate'] as String? ?? 
                               DateTime.now().toIso8601String()),
      scheduledPickupAt: json['scheduledPickupAt'] != null 
          ? DateTime.parse(json['scheduledPickupAt'] as String)
          : null,
      scheduledDeliveryAt: json['scheduledDeliveryAt'] != null 
          ? DateTime.parse(json['scheduledDeliveryAt'] as String)
          : null,
      actualPickupAt: json['actualPickupAt'] != null 
          ? DateTime.parse(json['actualPickupAt'] as String)
          : null,
      actualDeliveryAt: json['actualDeliveryAt'] != null 
          ? DateTime.parse(json['actualDeliveryAt'] as String)
          : null,
      specialInstructions: json['specialInstructions'] as String?,
      driverId: json['driverId'] as String?,
      driverName: json['driverName'] as String?,
      driverPhone: json['driverPhone'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Parse packages from JSON (handles both new and legacy formats)
  static List<Package> _parsePackages(Map<String, dynamic> json) {
    // New format: packages array
    if (json['packages'] != null) {
      return (json['packages'] as List<dynamic>)
          .map((p) => Package.fromJson(p as Map<String, dynamic>))
          .toList();
    }
    
    // Legacy format: cargoItems array
    if (json['cargoItems'] != null) {
      return (json['cargoItems'] as List<dynamic>)
          .map((item) => Package.fromJson({
            'itemName': item['itemName'],
            'category': item['category'],
            'itemType': item['itemType'],
            'weight': item['weight'],
            'quantity': item['quantity'],
            'durability': item['durability'],
            'imagePaths': item['imagePaths'],
          }))
          .toList();
    }
    
    // Legacy format: packageData
    if (json['packageData'] != null) {
      final packageData = json['packageData'] as Map<String, dynamic>;
      final packageDetails = packageData['packageDetails'] as Map<String, dynamic>?;
      
      if (packageDetails != null) {
        return [Package.fromJson(packageDetails)];
      }
    }
    
    return [];
  }

  /// Convert Shipment to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trackingNumber': trackingNumber,
      'sender': sender.toJson(),
      'pickupAddress': pickupAddress.toJson(),
      'receiver': receiver.toJson(),
      'deliveryAddress': deliveryAddress.toJson(),
      'packages': packages.map((p) => p.toJson()).toList(),
      'status': status.name,
      'priority': priority.displayName,
      'deliveryType': deliveryType.displayName,
      if (payment != null) 'payment': payment!.toJson(),
      if (tracking != null) 'tracking': tracking!.toJson(),
      'createdAt': createdAt.toIso8601String(),
      if (scheduledPickupAt != null) 'scheduledPickupAt': scheduledPickupAt!.toIso8601String(),
      if (scheduledDeliveryAt != null) 'scheduledDeliveryAt': scheduledDeliveryAt!.toIso8601String(),
      if (actualPickupAt != null) 'actualPickupAt': actualPickupAt!.toIso8601String(),
      if (actualDeliveryAt != null) 'actualDeliveryAt': actualDeliveryAt!.toIso8601String(),
      if (specialInstructions != null) 'specialInstructions': specialInstructions,
      if (driverId != null) 'driverId': driverId,
      if (driverName != null) 'driverName': driverName,
      if (driverPhone != null) 'driverPhone': driverPhone,
      if (metadata != null) 'metadata': metadata,
      
      // Legacy format compatibility
      'pickupData': {
        ...sender.toJson(),
        ...pickupAddress.toJson(),
      },
      'receiverData': {
        ...receiver.toJson(),
        ...deliveryAddress.toJson(),
      },
      'cargoItems': packages.map((p) => {
        'itemName': p.itemName,
        'category': p.category.displayName,
        'itemType': p.itemType,
        'weight': p.weight,
        'quantity': p.quantity,
        'durability': p.durability.displayName,
        'imagePaths': p.imagePaths,
      }).toList(),
      if (payment != null) 'paymentData': payment!.breakdown.toJson(),
      'orderDate': createdAt.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  Shipment copyWith({
    String? id,
    String? trackingNumber,
    Contact? sender,
    Address? pickupAddress,
    Contact? receiver,
    Address? deliveryAddress,
    List<Package>? packages,
    ShipmentStatus? status,
    ShipmentPriority? priority,
    DeliveryType? deliveryType,
    Payment? payment,
    TrackingTimeline? tracking,
    DateTime? createdAt,
    DateTime? scheduledPickupAt,
    DateTime? scheduledDeliveryAt,
    DateTime? actualPickupAt,
    DateTime? actualDeliveryAt,
    String? specialInstructions,
    String? driverId,
    String? driverName,
    String? driverPhone,
    Map<String, dynamic>? metadata,
  }) {
    return Shipment(
      id: id ?? this.id,
      trackingNumber: trackingNumber ?? this.trackingNumber,
      sender: sender ?? this.sender,
      pickupAddress: pickupAddress ?? this.pickupAddress,
      receiver: receiver ?? this.receiver,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      packages: packages ?? this.packages,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      deliveryType: deliveryType ?? this.deliveryType,
      payment: payment ?? this.payment,
      tracking: tracking ?? this.tracking,
      createdAt: createdAt ?? this.createdAt,
      scheduledPickupAt: scheduledPickupAt ?? this.scheduledPickupAt,
      scheduledDeliveryAt: scheduledDeliveryAt ?? this.scheduledDeliveryAt,
      actualPickupAt: actualPickupAt ?? this.actualPickupAt,
      actualDeliveryAt: actualDeliveryAt ?? this.actualDeliveryAt,
      specialInstructions: specialInstructions ?? this.specialInstructions,
      driverId: driverId ?? this.driverId,
      driverName: driverName ?? this.driverName,
      driverPhone: driverPhone ?? this.driverPhone,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'Shipment(id: $id, trackingNumber: $trackingNumber, status: ${status.displayName}, packages: ${packages.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Shipment &&
        other.id == id &&
        other.trackingNumber == trackingNumber;
  }

  @override
  int get hashCode {
    return Object.hash(id, trackingNumber);
  }
}
