/// Model representing a Package Shipment for tracking purposes
/// 
/// Package shipment monitoring/tracking model with details that allows 
/// specifics about the current status of a package delivery
class PackageShipment {
  final String uuid;
  final DateTime created;
  final DateTime updated;
  final String trackingId;
  final int deliveryReattemptCount;
  final String status;
  final String? note;
  final String type;
  final String package; // UUID reference to Package

  const PackageShipment({
    required this.uuid,
    required this.created,
    required this.updated,
    required this.trackingId,
    required this.deliveryReattemptCount,
    required this.status,
    this.note,
    required this.type,
    required this.package,
  });

  /// Create PackageShipment from JSON
  factory PackageShipment.fromJson(Map<String, dynamic> json) {
    return PackageShipment(
      uuid: json['uuid'] as String,
      created: DateTime.parse(json['created'] as String),
      updated: DateTime.parse(json['updated'] as String),
      trackingId: json['trackingId'] as String,
      deliveryReattemptCount: json['deliveryReattemptCount'] as int? ?? 0,
      status: json['status'] as String,
      note: json['note'] as String?,
      type: json['type'] as String,
      package: json['package'] as String,
    );
  }

  /// Convert PackageShipment to JSON
  Map<String, dynamic> toJson() {
    return {
      'uuid': uuid,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
      'trackingId': trackingId,
      'deliveryReattemptCount': deliveryReattemptCount,
      'status': status,
      if (note != null) 'note': note,
      'type': type,
      'package': package,
    };
  }

  /// Create a copy with updated fields
  PackageShipment copyWith({
    String? uuid,
    DateTime? created,
    DateTime? updated,
    String? trackingId,
    int? deliveryReattemptCount,
    String? status,
    String? note,
    String? type,
    String? package,
  }) {
    return PackageShipment(
      uuid: uuid ?? this.uuid,
      created: created ?? this.created,
      updated: updated ?? this.updated,
      trackingId: trackingId ?? this.trackingId,
      deliveryReattemptCount: deliveryReattemptCount ?? this.deliveryReattemptCount,
      status: status ?? this.status,
      note: note ?? this.note,
      type: type ?? this.type,
      package: package ?? this.package,
    );
  }

  @override
  String toString() {
    return 'PackageShipment(uuid: $uuid, trackingId: $trackingId, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PackageShipment && other.uuid == uuid;
  }

  @override
  int get hashCode => uuid.hashCode;
}

/// Common shipment status constants
class ShipmentStatus {
  static const String pending = 'pending';
  static const String confirmed = 'confirmed';
  static const String pickedUp = 'picked_up';
  static const String inTransit = 'in_transit';
  static const String outForDelivery = 'out_for_delivery';
  static const String delivered = 'delivered';
  static const String failed = 'failed';
  static const String cancelled = 'cancelled';
  static const String returned = 'returned';

  /// Get all available statuses
  static List<String> get allStatuses => [
    pending,
    confirmed,
    pickedUp,
    inTransit,
    outForDelivery,
    delivered,
    failed,
    cancelled,
    returned,
  ];

  /// Get user-friendly status display name
  static String getDisplayName(String status) {
    switch (status) {
      case pending:
        return 'Pending';
      case confirmed:
        return 'Confirmed';
      case pickedUp:
        return 'Picked Up';
      case inTransit:
        return 'In Transit';
      case outForDelivery:
        return 'Out for Delivery';
      case delivered:
        return 'Delivered';
      case failed:
        return 'Delivery Failed';
      case cancelled:
        return 'Cancelled';
      case returned:
        return 'Returned';
      default:
        return status.replaceAll('_', ' ').split(' ')
            .map((word) => word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : '')
            .join(' ');
    }
  }

  /// Check if status indicates shipment is in progress
  static bool isInProgress(String status) {
    return [confirmed, pickedUp, inTransit, outForDelivery].contains(status);
  }

  /// Check if status indicates shipment is completed
  static bool isCompleted(String status) {
    return [delivered, cancelled, returned].contains(status);
  }

  /// Check if status indicates shipment has failed
  static bool isFailed(String status) {
    return [failed, cancelled, returned].contains(status);
  }
}

/// Request model for updating shipment note
class UpdateShipmentRequest {
  final String? note;

  const UpdateShipmentRequest({
    this.note,
  });

  Map<String, dynamic> toJson() {
    return {
      if (note != null) 'note': note,
    };
  }
}
