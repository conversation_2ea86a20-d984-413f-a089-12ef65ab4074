/// Payment method enum
enum PaymentMethod {
  cash('Cash'),
  card('Card'),
  bankTransfer('Bank Transfer'),
  wallet('Wallet'),
  paypal('PayPal'),
  applePay('Apple Pay'),
  googlePay('Google Pay');

  const PaymentMethod(this.displayName);
  final String displayName;

  /// Get payment method from string
  static PaymentMethod fromString(String value) {
    return PaymentMethod.values.firstWhere(
      (method) => method.displayName.toLowerCase() == value.toLowerCase(),
      orElse: () => PaymentMethod.cash,
    );
  }
}

/// Payment status enum
enum PaymentStatus {
  pending('Pending'),
  processing('Processing'),
  completed('Completed'),
  failed('Failed'),
  cancelled('Cancelled'),
  refunded('Refunded');

  const PaymentStatus(this.displayName);
  final String displayName;

  /// Get payment status from string
  static PaymentStatus fromString(String value) {
    return PaymentStatus.values.firstWhere(
      (status) => status.displayName.toLowerCase() == value.toLowerCase(),
      orElse: () => PaymentStatus.pending,
    );
  }
}

/// Payment breakdown model
class PaymentBreakdown {
  final double baseShippingCost;
  final double vat;
  final double insurance;
  final double pickupCharge;
  final double deliveryCharge;
  final double serviceFee;
  final double discount;
  final bool isInsuranceFree;
  final bool isPickupFree;
  final bool isDeliveryFree;

  const PaymentBreakdown({
    required this.baseShippingCost,
    this.vat = 0.0,
    this.insurance = 0.0,
    this.pickupCharge = 0.0,
    this.deliveryCharge = 0.0,
    this.serviceFee = 0.0,
    this.discount = 0.0,
    this.isInsuranceFree = true,
    this.isPickupFree = true,
    this.isDeliveryFree = false,
  });

  /// Calculate subtotal before taxes and fees
  double get subtotal => baseShippingCost + 
                        (isPickupFree ? 0 : pickupCharge) + 
                        (isDeliveryFree ? 0 : deliveryCharge);

  /// Calculate total insurance cost
  double get totalInsurance => isInsuranceFree ? 0 : insurance;

  /// Calculate total before discount
  double get totalBeforeDiscount => subtotal + vat + totalInsurance + serviceFee;

  /// Calculate final total amount
  double get total => totalBeforeDiscount - discount;

  /// Calculate total savings from free services and discounts
  double get totalSavings => 
      (isInsuranceFree ? insurance : 0) + 
      (isPickupFree ? pickupCharge : 0) + 
      (isDeliveryFree ? deliveryCharge : 0) + 
      discount;

  /// Create PaymentBreakdown from JSON
  factory PaymentBreakdown.fromJson(Map<String, dynamic> json) {
    return PaymentBreakdown(
      baseShippingCost: (json['baseShippingCost'] as num?)?.toDouble() ?? 
                       (json['shippingCost'] as num?)?.toDouble() ?? 0.0,
      vat: (json['vat'] as num?)?.toDouble() ?? 0.0,
      insurance: (json['insurance'] as num?)?.toDouble() ?? 0.0,
      pickupCharge: (json['pickupCharge'] as num?)?.toDouble() ?? 0.0,
      deliveryCharge: (json['deliveryCharge'] as num?)?.toDouble() ?? 0.0,
      serviceFee: (json['serviceFee'] as num?)?.toDouble() ?? 0.0,
      discount: (json['discount'] as num?)?.toDouble() ?? 0.0,
      isInsuranceFree: json['isInsuranceFree'] as bool? ?? true,
      isPickupFree: json['isPickupFree'] as bool? ?? true,
      isDeliveryFree: json['isDeliveryFree'] as bool? ?? false,
    );
  }

  /// Convert PaymentBreakdown to JSON
  Map<String, dynamic> toJson() {
    return {
      'baseShippingCost': baseShippingCost,
      'shippingCost': baseShippingCost, // For backward compatibility
      'vat': vat,
      'insurance': insurance,
      'pickupCharge': pickupCharge,
      'deliveryCharge': deliveryCharge,
      'serviceFee': serviceFee,
      'discount': discount,
      'isInsuranceFree': isInsuranceFree,
      'isPickupFree': isPickupFree,
      'isDeliveryFree': isDeliveryFree,
      'total': total,
    };
  }

  /// Create a copy with updated fields
  PaymentBreakdown copyWith({
    double? baseShippingCost,
    double? vat,
    double? insurance,
    double? pickupCharge,
    double? deliveryCharge,
    double? serviceFee,
    double? discount,
    bool? isInsuranceFree,
    bool? isPickupFree,
    bool? isDeliveryFree,
  }) {
    return PaymentBreakdown(
      baseShippingCost: baseShippingCost ?? this.baseShippingCost,
      vat: vat ?? this.vat,
      insurance: insurance ?? this.insurance,
      pickupCharge: pickupCharge ?? this.pickupCharge,
      deliveryCharge: deliveryCharge ?? this.deliveryCharge,
      serviceFee: serviceFee ?? this.serviceFee,
      discount: discount ?? this.discount,
      isInsuranceFree: isInsuranceFree ?? this.isInsuranceFree,
      isPickupFree: isPickupFree ?? this.isPickupFree,
      isDeliveryFree: isDeliveryFree ?? this.isDeliveryFree,
    );
  }

  @override
  String toString() {
    return 'PaymentBreakdown(total: ₦${total.toStringAsFixed(2)}, savings: ₦${totalSavings.toStringAsFixed(2)})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaymentBreakdown &&
        other.baseShippingCost == baseShippingCost &&
        other.vat == vat &&
        other.insurance == insurance &&
        other.total == total;
  }

  @override
  int get hashCode {
    return Object.hash(baseShippingCost, vat, insurance, total);
  }
}

/// Payment transaction model
class Payment {
  final String id;
  final String shipmentId;
  final PaymentBreakdown breakdown;
  final PaymentMethod method;
  final PaymentStatus status;
  final DateTime createdAt;
  final DateTime? completedAt;
  final String? transactionReference;
  final String? gatewayReference;
  final String? failureReason;
  final Map<String, dynamic>? metadata;

  const Payment({
    required this.id,
    required this.shipmentId,
    required this.breakdown,
    required this.method,
    required this.status,
    required this.createdAt,
    this.completedAt,
    this.transactionReference,
    this.gatewayReference,
    this.failureReason,
    this.metadata,
  });

  /// Check if payment is successful
  bool get isSuccessful => status == PaymentStatus.completed;

  /// Check if payment is pending
  bool get isPending => status == PaymentStatus.pending || status == PaymentStatus.processing;

  /// Check if payment failed
  bool get isFailed => status == PaymentStatus.failed || status == PaymentStatus.cancelled;

  /// Get payment duration
  Duration? get duration {
    if (completedAt != null) {
      return completedAt!.difference(createdAt);
    }
    return null;
  }

  /// Create Payment from JSON
  factory Payment.fromJson(Map<String, dynamic> json) {
    return Payment(
      id: json['id'] as String? ?? '',
      shipmentId: json['shipmentId'] as String? ?? '',
      breakdown: PaymentBreakdown.fromJson(json['breakdown'] as Map<String, dynamic>? ?? json),
      method: PaymentMethod.fromString(json['method'] as String? ?? 'cash'),
      status: PaymentStatus.fromString(json['status'] as String? ?? 'pending'),
      createdAt: DateTime.parse(json['createdAt'] as String? ?? DateTime.now().toIso8601String()),
      completedAt: json['completedAt'] != null 
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      transactionReference: json['transactionReference'] as String?,
      gatewayReference: json['gatewayReference'] as String?,
      failureReason: json['failureReason'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert Payment to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'shipmentId': shipmentId,
      'breakdown': breakdown.toJson(),
      'method': method.displayName,
      'status': status.displayName,
      'createdAt': createdAt.toIso8601String(),
      if (completedAt != null) 'completedAt': completedAt!.toIso8601String(),
      if (transactionReference != null) 'transactionReference': transactionReference,
      if (gatewayReference != null) 'gatewayReference': gatewayReference,
      if (failureReason != null) 'failureReason': failureReason,
      if (metadata != null) 'metadata': metadata,
    };
  }

  /// Create a copy with updated fields
  Payment copyWith({
    String? id,
    String? shipmentId,
    PaymentBreakdown? breakdown,
    PaymentMethod? method,
    PaymentStatus? status,
    DateTime? createdAt,
    DateTime? completedAt,
    String? transactionReference,
    String? gatewayReference,
    String? failureReason,
    Map<String, dynamic>? metadata,
  }) {
    return Payment(
      id: id ?? this.id,
      shipmentId: shipmentId ?? this.shipmentId,
      breakdown: breakdown ?? this.breakdown,
      method: method ?? this.method,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      transactionReference: transactionReference ?? this.transactionReference,
      gatewayReference: gatewayReference ?? this.gatewayReference,
      failureReason: failureReason ?? this.failureReason,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'Payment(id: $id, amount: ₦${breakdown.total.toStringAsFixed(2)}, status: ${status.displayName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Payment &&
        other.id == id &&
        other.shipmentId == shipmentId &&
        other.status == status;
  }

  @override
  int get hashCode {
    return Object.hash(id, shipmentId, status);
  }
}
