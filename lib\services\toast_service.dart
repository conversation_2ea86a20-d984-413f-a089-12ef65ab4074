import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/custom_toast.dart';

/// Toast service that provides a global way to show toast messages
/// 
/// This service acts as a wrapper around CustomToast and provides
/// a singleton pattern for easy access throughout the app.
class ToastService {
  static final ToastService _instance = ToastService._internal();
  factory ToastService() => _instance;
  ToastService._internal();

  /// Global navigator key for accessing context
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  /// Initialize the toast service
  static void initialize() {
    // The initialization will happen when the first toast is shown
    // This ensures we have a valid context
  }

  /// Get the current context from the navigator
  BuildContext? get _context => navigatorKey.currentContext;

  /// Initialize toast with current context if not already initialized
  void _ensureInitialized() {
    if (_context != null && !CustomToast.isInitialized) {
      try {
        CustomToast.init(_context!);
      } catch (e) {
        // If initialization fails, we'll fall back to simple toast
        debugPrint('Toast initialization failed: $e');
      }
    }
  }

  /// Show a success toast message
  void showSuccess(String message, {Duration? duration}) {
    _ensureInitialized();
    CustomToast.showSuccess(message, duration: duration);
  }

  /// Show an error toast message
  void showError(String message, {Duration? duration}) {
    _ensureInitialized();
    CustomToast.showError(message, duration: duration);
  }

  /// Show a warning toast message
  void showWarning(String message, {Duration? duration}) {
    _ensureInitialized();
    CustomToast.showWarning(message, duration: duration);
  }

  /// Show an info toast message
  void showInfo(String message, {Duration? duration}) {
    _ensureInitialized();
    CustomToast.showInfo(message, duration: duration);
  }

  /// Show a custom toast with specific styling
  void showCustom({
    required String message,
    required Color backgroundColor,
    required IconData icon,
    Duration? duration,
    Color? textColor,
    Color? iconColor,
  }) {
    _ensureInitialized();
    CustomToast.showCustom(
      message: message,
      backgroundColor: backgroundColor,
      icon: icon,
      duration: duration,
      textColor: textColor,
      iconColor: iconColor,
    );
  }

  /// Show a simple text toast (fallback)
  void showSimple(String message, {bool isLong = false}) {
    CustomToast.showSimple(message, isLong: isLong);
  }

  /// Remove all active toasts
  void removeAllToasts() {
    CustomToast.removeAllToasts();
  }

  /// Remove the current toast
  void removeToast() {
    CustomToast.removeToast();
  }

  /// Show toast based on type enum
  void showByType(ToastType type, String message, {Duration? duration}) {
    type.show(message, duration: duration);
  }

  // Convenience static methods for global access
  static void success(String message, {Duration? duration}) {
    ToastService().showSuccess(message, duration: duration);
  }

  static void error(String message, {Duration? duration}) {
    ToastService().showError(message, duration: duration);
  }

  static void warning(String message, {Duration? duration}) {
    ToastService().showWarning(message, duration: duration);
  }

  static void info(String message, {Duration? duration}) {
    ToastService().showInfo(message, duration: duration);
  }

  static void simple(String message, {bool isLong = false}) {
    ToastService().showSimple(message, isLong: isLong);
  }

  static void removeAll() {
    ToastService().removeAllToasts();
  }

  static void remove() {
    ToastService().removeToast();
  }
}

/// Global toast instance for easy access
final toast = ToastService();
