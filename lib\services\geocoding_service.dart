import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:rideoon/services/config_service.dart';

/// Service for geocoding addresses to coordinates and reverse geocoding
class GeocodingService {
  static const String _baseUrl = 'https://maps.googleapis.com/maps/api/geocode/json';

  /// Convert address to coordinates
  static Future<LatLng?> getCoordinatesFromAddress(String address) async {
    try {
      final apiKey = ConfigService.googleMapsApiKey;
      final encodedAddress = Uri.encodeComponent(address);
      final url = '$_baseUrl?address=$encodedAddress&key=$apiKey';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['status'] == 'OK' && data['results'].isNotEmpty) {
          final location = data['results'][0]['geometry']['location'];
          return LatLng(location['lat'], location['lng']);
        }
      }
    } catch (e) {
      print('Error geocoding address: $e');
    }
    
    return null;
  }

  /// Convert coordinates to address
  static Future<String?> getAddressFromCoordinates(LatLng coordinates) async {
    try {
      final apiKey = ConfigService.googleMapsApiKey;
      final url = '$_baseUrl?latlng=${coordinates.latitude},${coordinates.longitude}&key=$apiKey';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['status'] == 'OK' && data['results'].isNotEmpty) {
          return data['results'][0]['formatted_address'];
        }
      }
    } catch (e) {
      print('Error reverse geocoding coordinates: $e');
    }
    
    return null;
  }

  /// Get detailed location information from address
  static Future<Map<String, dynamic>?> getLocationDetails(String address) async {
    try {
      final apiKey = ConfigService.googleMapsApiKey;
      final encodedAddress = Uri.encodeComponent(address);
      final url = '$_baseUrl?address=$encodedAddress&key=$apiKey';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['status'] == 'OK' && data['results'].isNotEmpty) {
          final result = data['results'][0];
          final location = result['geometry']['location'];
          
          return {
            'address': result['formatted_address'],
            'latitude': location['lat'],
            'longitude': location['lng'],
            'placeId': result['place_id'],
            'types': result['types'],
            'addressComponents': result['address_components'],
          };
        }
      }
    } catch (e) {
      print('Error getting location details: $e');
    }
    
    return null;
  }

  /// Search for places based on query
  static Future<List<Map<String, dynamic>>> searchPlaces(String query) async {
    try {
      final apiKey = ConfigService.googleMapsApiKey;
      final encodedQuery = Uri.encodeComponent(query);
      final url = '$_baseUrl?address=$encodedQuery&key=$apiKey';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['status'] == 'OK') {
          return (data['results'] as List).map((result) {
            final location = result['geometry']['location'];
            return {
              'address': result['formatted_address'],
              'latitude': location['lat'],
              'longitude': location['lng'],
              'placeId': result['place_id'],
              'types': result['types'],
            };
          }).toList();
        }
      }
    } catch (e) {
      print('Error searching places: $e');
    }
    
    return [];
  }
}
