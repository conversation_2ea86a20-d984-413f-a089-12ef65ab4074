import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';

/// Custom toast widget that provides consistent styling across the app
///
/// This widget uses the app's color scheme and provides different toast types
/// for success, error, warning, and info messages.
class CustomToast {
  static FToast? _fToast;

  /// Initialize the toast system with the given context
  static void init(BuildContext context) {
    _fToast = FToast();
    _fToast!.init(context);
  }

  /// Check if toast is initialized
  static bool get isInitialized => _fToast != null;

  /// Show a success toast message
  static void showSuccess(String message, {Duration? duration}) {
    if (!isInitialized) {
      showSimple(message);
      return;
    }
    _showCustomToast(
      message: message,
      backgroundColor: AppColors.success,
      icon: Icons.check_circle,
      duration: duration ?? const Duration(seconds: 3),
    );
  }

  /// Show an error toast message
  static void showError(String message, {Duration? duration}) {
    if (!isInitialized) {
      showSimple(message);
      return;
    }
    _showCustomToast(
      message: message,
      backgroundColor: AppColors.error,
      icon: Icons.error,
      duration: duration ?? const Duration(seconds: 4),
    );
  }

  /// Show a warning toast message
  static void showWarning(String message, {Duration? duration}) {
    if (!isInitialized) {
      showSimple(message);
      return;
    }
    _showCustomToast(
      message: message,
      backgroundColor: AppColors.warning,
      icon: Icons.warning,
      duration: duration ?? const Duration(seconds: 3),
    );
  }

  /// Show an info toast message
  static void showInfo(String message, {Duration? duration}) {
    if (!isInitialized) {
      showSimple(message);
      return;
    }
    _showCustomToast(
      message: message,
      backgroundColor: AppColors.primary,
      icon: Icons.info,
      duration: duration ?? const Duration(seconds: 3),
    );
  }

  /// Show a custom toast with specific styling
  static void showCustom({
    required String message,
    required Color backgroundColor,
    required IconData icon,
    Duration? duration,
    Color? textColor,
    Color? iconColor,
  }) {
    if (!isInitialized) {
      showSimple(message);
      return;
    }
    _showCustomToast(
      message: message,
      backgroundColor: backgroundColor,
      icon: icon,
      duration: duration ?? const Duration(seconds: 3),
      textColor: textColor,
      iconColor: iconColor,
    );
  }

  /// Internal method to show the actual toast
  static void _showCustomToast({
    required String message,
    required Color backgroundColor,
    required IconData icon,
    required Duration duration,
    Color? textColor,
    Color? iconColor,
  }) {
    Widget toast = Container(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 12.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.0),
        color: backgroundColor,
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: 8.0,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: iconColor ?? AppColors.white,
            size: 20,
          ),
          const SizedBox(width: 12.0),
          Flexible(
            child: Text(
              message,
              style: TextStyle(
                color: textColor ?? AppColors.white,
                fontSize: 14,
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w500,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );

    _fToast!.showToast(
      child: toast,
      gravity: ToastGravity.TOP,
      toastDuration: duration,
    );
  }

  /// Remove all active toasts
  static void removeAllToasts() {
    if (isInitialized) {
      _fToast!.removeCustomToast();
    }
  }

  /// Remove the current toast
  static void removeToast() {
    if (isInitialized) {
      _fToast!.removeQueuedCustomToasts();
    }
  }

  /// Show a simple text toast (fallback for when custom toast is not available)
  static void showSimple(String message, {bool isLong = false}) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: isLong ? Toast.LENGTH_LONG : Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      timeInSecForIosWeb: isLong ? 5 : 3,
      backgroundColor: AppColors.black.withValues(alpha: 0.8),
      textColor: AppColors.white,
      fontSize: 14.0,
    );
  }
}

/// Toast types enum for easier usage
enum ToastType {
  success,
  error,
  warning,
  info,
}

/// Extension to easily show toasts with enum
extension ToastTypeExtension on ToastType {
  void show(String message, {Duration? duration}) {
    switch (this) {
      case ToastType.success:
        CustomToast.showSuccess(message, duration: duration);
        break;
      case ToastType.error:
        CustomToast.showError(message, duration: duration);
        break;
      case ToastType.warning:
        CustomToast.showWarning(message, duration: duration);
        break;
      case ToastType.info:
        CustomToast.showInfo(message, duration: duration);
        break;
    }
  }
}
