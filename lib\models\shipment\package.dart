/// Package categories enum
enum PackageCategory {
  electronics('Electronics'),
  computerAccessories('Computer Accessories'),
  clothingFashion('Clothing & Fashion'),
  booksDocuments('Books & Documents'),
  foodBeverages('Food & Beverages'),
  homeGarden('Home & Garden'),
  sportsOutdoors('Sports & Outdoors'),
  healthBeauty('Health & Beauty'),
  toysGames('Toys & Games'),
  automotive('Automotive'),
  jewelryAccessories('Jewelry & Accessories'),
  artCrafts('Art & Crafts'),
  musicalInstruments('Musical Instruments'),
  officeSupplies('Office Supplies'),
  general('General');

  const PackageCategory(this.displayName);
  final String displayName;

  /// Get category from string
  static PackageCategory fromString(String value) {
    return PackageCategory.values.firstWhere(
      (category) => category.displayName.toLowerCase() == value.toLowerCase(),
      orElse: () => PackageCategory.general,
    );
  }
}

/// Package durability enum
enum PackageDurability {
  fragile('Fragile'),
  average('Average'),
  durable('Durable');

  const PackageDurability(this.displayName);
  final String displayName;

  /// Get durability from string
  static PackageDurability fromString(String value) {
    return PackageDurability.values.firstWhere(
      (durability) => durability.displayName.toLowerCase() == value.toLowerCase(),
      orElse: () => PackageDurability.average,
    );
  }
}

/// Package delivery type enum
enum DeliveryType {
  standard('Standard'),
  express('Express'),
  overnight('Overnight'),
  sameDay('Same Day');

  const DeliveryType(this.displayName);
  final String displayName;

  /// Get delivery type from string
  static DeliveryType fromString(String value) {
    return DeliveryType.values.firstWhere(
      (type) => type.displayName.toLowerCase() == value.toLowerCase(),
      orElse: () => DeliveryType.standard,
    );
  }
}

/// Core package model
class Package {
  final String itemName;
  final String? description;
  final PackageCategory category;
  final String itemType;
  final double weight; // in kg
  final int quantity;
  final PackageDurability durability;
  final DeliveryType deliveryType;
  final List<String> imagePaths;
  final double? value; // monetary value for insurance
  final Map<String, dynamic>? customAttributes;

  const Package({
    required this.itemName,
    this.description,
    required this.category,
    required this.itemType,
    required this.weight,
    this.quantity = 1,
    this.durability = PackageDurability.average,
    this.deliveryType = DeliveryType.standard,
    this.imagePaths = const [],
    this.value,
    this.customAttributes,
  });

  /// Get total weight (weight * quantity)
  double get totalWeight => weight * quantity;

  /// Check if package has images
  bool get hasImages => imagePaths.isNotEmpty;

  /// Check if package has monetary value
  bool get hasValue => value != null && value! > 0;

  /// Get package summary for display
  String get summary {
    final parts = <String>[
      itemName,
      if (quantity > 1) '(${quantity}x)',
      '${totalWeight}kg',
      durability.displayName,
    ];
    return parts.join(' • ');
  }

  /// Create Package from JSON
  factory Package.fromJson(Map<String, dynamic> json) {
    return Package(
      itemName: json['itemName'] as String? ?? '',
      description: json['description'] as String?,
      category: PackageCategory.fromString(json['category'] as String? ?? 'General'),
      itemType: json['itemType'] as String? ?? '',
      weight: (json['weight'] as num?)?.toDouble() ?? 0.0,
      quantity: json['quantity'] as int? ?? 1,
      durability: PackageDurability.fromString(json['durability'] as String? ?? 'Average'),
      deliveryType: DeliveryType.fromString(json['deliveryType'] as String? ?? 'Standard'),
      imagePaths: json['imagePaths'] != null 
          ? List<String>.from(json['imagePaths'] as List)
          : [],
      value: (json['value'] as num?)?.toDouble(),
      customAttributes: json['customAttributes'] as Map<String, dynamic>?,
    );
  }

  /// Convert Package to JSON
  Map<String, dynamic> toJson() {
    return {
      'itemName': itemName,
      if (description != null) 'description': description,
      'category': category.displayName,
      'itemType': itemType,
      'weight': weight,
      'quantity': quantity,
      'durability': durability.displayName,
      'deliveryType': deliveryType.displayName,
      'imagePaths': imagePaths,
      if (value != null) 'value': value,
      if (customAttributes != null) 'customAttributes': customAttributes,
    };
  }

  /// Create a copy with updated fields
  Package copyWith({
    String? itemName,
    String? description,
    PackageCategory? category,
    String? itemType,
    double? weight,
    int? quantity,
    PackageDurability? durability,
    DeliveryType? deliveryType,
    List<String>? imagePaths,
    double? value,
    Map<String, dynamic>? customAttributes,
  }) {
    return Package(
      itemName: itemName ?? this.itemName,
      description: description ?? this.description,
      category: category ?? this.category,
      itemType: itemType ?? this.itemType,
      weight: weight ?? this.weight,
      quantity: quantity ?? this.quantity,
      durability: durability ?? this.durability,
      deliveryType: deliveryType ?? this.deliveryType,
      imagePaths: imagePaths ?? this.imagePaths,
      value: value ?? this.value,
      customAttributes: customAttributes ?? this.customAttributes,
    );
  }

  @override
  String toString() {
    return 'Package(itemName: $itemName, category: ${category.displayName}, weight: ${totalWeight}kg)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Package &&
        other.itemName == itemName &&
        other.category == category &&
        other.itemType == itemType &&
        other.weight == weight &&
        other.quantity == quantity;
  }

  @override
  int get hashCode {
    return Object.hash(itemName, category, itemType, weight, quantity);
  }
}
