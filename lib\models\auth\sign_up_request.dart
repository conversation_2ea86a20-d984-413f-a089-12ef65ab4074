/// Request model for user sign-up API call
class SignUpRequest {
  final String email;
  final String password;
  final String repeatedPassword;
  final String firstName;
  final String lastName;
  final String phoneNumber;

  const SignUpRequest({
    required this.email,
    required this.password,
    required this.repeatedPassword,
    required this.firstName,
    required this.lastName,
    required this.phoneNumber,
  });

  /// Convert the request to JSON format for API call
  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
      'repeatedPassword': repeatedPassword,
      'firstName': firstName,
      'lastName': lastName,
      'phoneNumber': phoneNumber,
    };
  }

  /// Create a SignUpRequest from JSON data
  factory SignUpRequest.fromJson(Map<String, dynamic> json) {
    return SignUpRequest(
      email: json['email'] as String,
      password: json['password'] as String,
      repeatedPassword: json['repeatedPassword'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      phoneNumber: json['phoneNumber'] as String,
    );
  }

  /// Create a copy of this request with some fields updated
  SignUpRequest copyWith({
    String? email,
    String? password,
    String? repeatedPassword,
    String? firstName,
    String? lastName,
    String? phoneNumber,
  }) {
    return SignUpRequest(
      email: email ?? this.email,
      password: password ?? this.password,
      repeatedPassword: repeatedPassword ?? this.repeatedPassword,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
    );
  }

  @override
  String toString() {
    return 'SignUpRequest(email: $email, firstName: $firstName, lastName: $lastName, phoneNumber: $phoneNumber)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SignUpRequest &&
        other.email == email &&
        other.password == password &&
        other.repeatedPassword == repeatedPassword &&
        other.firstName == firstName &&
        other.lastName == lastName &&
        other.phoneNumber == phoneNumber;
  }

  @override
  int get hashCode {
    return Object.hash(
      email,
      password,
      repeatedPassword,
      firstName,
      lastName,
      phoneNumber,
    );
  }
}
