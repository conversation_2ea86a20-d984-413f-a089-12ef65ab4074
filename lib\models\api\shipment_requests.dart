import 'package:rideoon/models/shipment/shipment.dart';
import 'package:rideoon/models/shipment/package.dart';
import 'package:rideoon/models/payment/payment.dart';

/// Request model for creating a new shipment
class CreateShipmentRequest {
  final String senderName;
  final String senderPhone;
  final String senderEmail;
  final String pickupAddress;
  final String pickupLandmark;
  final String pickupCity;
  final String pickupState;
  final double? pickupLatitude;
  final double? pickupLongitude;
  
  final String receiverName;
  final String receiverPhone;
  final String receiverEmail;
  final String deliveryAddress;
  final String deliveryLandmark;
  final String deliveryCity;
  final String deliveryState;
  final double? deliveryLatitude;
  final double? deliveryLongitude;
  
  final List<PackageRequest> packages;
  final String deliveryType;
  final String priority;
  final PaymentRequest payment;
  final String? specialInstructions;
  final DateTime? scheduledPickupAt;
  final DateTime? scheduledDeliveryAt;

  const CreateShipmentRequest({
    required this.senderName,
    required this.senderPhone,
    required this.senderEmail,
    required this.pickupAddress,
    required this.pickupLandmark,
    required this.pickupCity,
    required this.pickupState,
    this.pickupLatitude,
    this.pickupLongitude,
    required this.receiverName,
    required this.receiverPhone,
    required this.receiverEmail,
    required this.deliveryAddress,
    required this.deliveryLandmark,
    required this.deliveryCity,
    required this.deliveryState,
    this.deliveryLatitude,
    this.deliveryLongitude,
    required this.packages,
    this.deliveryType = 'standard',
    this.priority = 'normal',
    required this.payment,
    this.specialInstructions,
    this.scheduledPickupAt,
    this.scheduledDeliveryAt,
  });

  /// Create from Shipment model
  factory CreateShipmentRequest.fromShipment(Shipment shipment) {
    return CreateShipmentRequest(
      senderName: shipment.sender.name,
      senderPhone: shipment.sender.phoneNumber,
      senderEmail: shipment.sender.email ?? '',
      pickupAddress: shipment.pickupAddress.street,
      pickupLandmark: shipment.pickupAddress.landmark ?? '',
      pickupCity: shipment.pickupAddress.city,
      pickupState: shipment.pickupAddress.state,
      pickupLatitude: shipment.pickupAddress.latitude,
      pickupLongitude: shipment.pickupAddress.longitude,
      receiverName: shipment.receiver.name,
      receiverPhone: shipment.receiver.phoneNumber,
      receiverEmail: shipment.receiver.email ?? '',
      deliveryAddress: shipment.deliveryAddress.street,
      deliveryLandmark: shipment.deliveryAddress.landmark ?? '',
      deliveryCity: shipment.deliveryAddress.city,
      deliveryState: shipment.deliveryAddress.state,
      deliveryLatitude: shipment.deliveryAddress.latitude,
      deliveryLongitude: shipment.deliveryAddress.longitude,
      packages: shipment.packages.map((p) => PackageRequest.fromPackage(p)).toList(),
      deliveryType: shipment.deliveryType.name,
      priority: shipment.priority.name,
      payment: PaymentRequest.fromPayment(shipment.payment!),
      specialInstructions: shipment.specialInstructions,
      scheduledPickupAt: shipment.scheduledPickupAt,
      scheduledDeliveryAt: shipment.scheduledDeliveryAt,
    );
  }

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      'sender': {
        'name': senderName,
        'phone': senderPhone,
        'email': senderEmail,
      },
      'pickup': {
        'address': pickupAddress,
        'landmark': pickupLandmark,
        'city': pickupCity,
        'state': pickupState,
        if (pickupLatitude != null) 'latitude': pickupLatitude,
        if (pickupLongitude != null) 'longitude': pickupLongitude,
      },
      'receiver': {
        'name': receiverName,
        'phone': receiverPhone,
        'email': receiverEmail,
      },
      'delivery': {
        'address': deliveryAddress,
        'landmark': deliveryLandmark,
        'city': deliveryCity,
        'state': deliveryState,
        if (deliveryLatitude != null) 'latitude': deliveryLatitude,
        if (deliveryLongitude != null) 'longitude': deliveryLongitude,
      },
      'packages': packages.map((p) => p.toJson()).toList(),
      'deliveryType': deliveryType,
      'priority': priority,
      'payment': payment.toJson(),
      if (specialInstructions != null) 'specialInstructions': specialInstructions,
      if (scheduledPickupAt != null) 'scheduledPickupAt': scheduledPickupAt!.toIso8601String(),
      if (scheduledDeliveryAt != null) 'scheduledDeliveryAt': scheduledDeliveryAt!.toIso8601String(),
    };
  }
}

/// Package request model for API
class PackageRequest {
  final String itemName;
  final String description;
  final String category;
  final String itemType;
  final double weight;
  final int quantity;
  final String durability;
  final double? value;
  final List<String> imagePaths;

  const PackageRequest({
    required this.itemName,
    required this.description,
    required this.category,
    required this.itemType,
    required this.weight,
    this.quantity = 1,
    this.durability = 'average',
    this.value,
    this.imagePaths = const [],
  });

  /// Create from Package model
  factory PackageRequest.fromPackage(Package package) {
    return PackageRequest(
      itemName: package.itemName,
      description: package.description ?? '',
      category: package.category.displayName,
      itemType: package.itemType,
      weight: package.weight,
      quantity: package.quantity,
      durability: package.durability.displayName.toLowerCase(),
      value: package.value,
      imagePaths: package.imagePaths,
    );
  }

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      'itemName': itemName,
      'description': description,
      'category': category,
      'itemType': itemType,
      'weight': weight,
      'quantity': quantity,
      'durability': durability,
      if (value != null) 'value': value,
      'imagePaths': imagePaths,
    };
  }
}

/// Payment request model for API
class PaymentRequest {
  final String method;
  final double amount;
  final PaymentBreakdownRequest breakdown;
  final String? gatewayReference;

  const PaymentRequest({
    required this.method,
    required this.amount,
    required this.breakdown,
    this.gatewayReference,
  });

  /// Create from Payment model
  factory PaymentRequest.fromPayment(Payment payment) {
    return PaymentRequest(
      method: payment.method.name,
      amount: payment.breakdown.total,
      breakdown: PaymentBreakdownRequest.fromPaymentBreakdown(payment.breakdown),
      gatewayReference: payment.gatewayReference,
    );
  }

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      'method': method,
      'amount': amount,
      'breakdown': breakdown.toJson(),
      if (gatewayReference != null) 'gatewayReference': gatewayReference,
    };
  }
}

/// Payment breakdown request model for API
class PaymentBreakdownRequest {
  final double shippingCost;
  final double vat;
  final double insurance;
  final double pickupCharge;
  final double deliveryCharge;
  final double serviceFee;
  final double discount;

  const PaymentBreakdownRequest({
    required this.shippingCost,
    this.vat = 0.0,
    this.insurance = 0.0,
    this.pickupCharge = 0.0,
    this.deliveryCharge = 0.0,
    this.serviceFee = 0.0,
    this.discount = 0.0,
  });

  /// Create from PaymentBreakdown model
  factory PaymentBreakdownRequest.fromPaymentBreakdown(PaymentBreakdown breakdown) {
    return PaymentBreakdownRequest(
      shippingCost: breakdown.baseShippingCost,
      vat: breakdown.vat,
      insurance: breakdown.insurance,
      pickupCharge: breakdown.pickupCharge,
      deliveryCharge: breakdown.deliveryCharge,
      serviceFee: breakdown.serviceFee,
      discount: breakdown.discount,
    );
  }

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      'shippingCost': shippingCost,
      'vat': vat,
      'insurance': insurance,
      'pickupCharge': pickupCharge,
      'deliveryCharge': deliveryCharge,
      'serviceFee': serviceFee,
      'discount': discount,
    };
  }
}

/// Request model for updating shipment status
class UpdateShipmentStatusRequest {
  final String shipmentId;
  final String status;
  final String? location;
  final String? notes;
  final DateTime? timestamp;

  const UpdateShipmentStatusRequest({
    required this.shipmentId,
    required this.status,
    this.location,
    this.notes,
    this.timestamp,
  });

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      'shipmentId': shipmentId,
      'status': status,
      if (location != null) 'location': location,
      if (notes != null) 'notes': notes,
      'timestamp': (timestamp ?? DateTime.now()).toIso8601String(),
    };
  }
}

/// Request model for tracking shipment
class TrackShipmentRequest {
  final String trackingNumber;

  const TrackShipmentRequest({
    required this.trackingNumber,
  });

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      'trackingNumber': trackingNumber,
    };
  }
}
