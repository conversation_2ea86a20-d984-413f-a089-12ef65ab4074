import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/dashboards/user_dashboard/home_view/home_analytics.dart';
import 'package:rideoon/views/custom_widgets/current_shipments_widget.dart';
import 'package:rideoon/views/custom_widgets/shipment_card.dart';
import 'package:rideoon/views/dashboards/user_dashboard/shipment_view/shipment_view.dart';
import 'package:rideoon/views/dashboards/user_dashboard/send_a_package/send_package_view.dart';
import 'package:rideoon/services/package_data_service.dart';

/// Home view page for user dashboard
///
/// This page contains the main dashboard content for users,
/// including user profile, package sending, analytics, and history.
class HomeView extends StatefulWidget {
  /// Callback function to switch to shipment tab
  final VoidCallback? onNavigateToShipment;

  /// Callback to provide refresh function to parent
  final Function(VoidCallback)? onRefreshCallback;

  const HomeView({
    super.key,
    this.onNavigateToShipment,
    this.onRefreshCallback,
  });

  @override
  State<HomeView> createState() => _HomeViewState();
}



class _HomeViewState extends State<HomeView> {
  List<ShipmentData> _currentShipments = [];
  List<ShipmentData> _shipmentHistory = [];
  bool _isLoading = true;
  VoidCallback? _refreshAnalytics;

  @override
  void initState() {
    super.initState();
    _loadShipmentData();

    // Provide refresh callback to parent if available
    if (widget.onRefreshCallback != null) {
      widget.onRefreshCallback!(refreshHomeData);
    }
  }

  /// Load shipment data from local storage
  Future<void> _loadShipmentData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Load current shipments
      final currentShipmentsData = await PackageDataService.getCurrentShipments();
      final currentShipments = currentShipmentsData.map((data) => _convertToShipmentData(data)).toList();

      // Load completed orders for history
      final completedOrdersData = await PackageDataService.getCompletedOrders();
      final completedShipments = completedOrdersData
          .where((data) => data['status'] == 'completed')
          .map((data) => _convertToShipmentData(data))
          .toList();

      setState(() {
        _currentShipments = currentShipments;
        _shipmentHistory = completedShipments;
        _isLoading = false;
      });

      // Refresh analytics after loading shipment data
      if (_refreshAnalytics != null) {
        _refreshAnalytics!();
      }
    } catch (e) {
      print('Error loading shipment data: $e');
      setState(() {
        _currentShipments = [];
        _shipmentHistory = [];
        _isLoading = false;
      });
    }
  }

  /// Convert stored data to ShipmentData
  ShipmentData _convertToShipmentData(Map<String, dynamic> data) {
    final status = _getShipmentStatus(data['status'] ?? 'pending');
    final trackingSteps = _generateTrackingSteps(status, data);

    // Get title from cargo items or package data
    String title = 'Package';
    if (data['cargoItems'] != null && (data['cargoItems'] as List).isNotEmpty) {
      final firstItem = (data['cargoItems'] as List).first;
      title = firstItem['itemName'] ?? 'Package';
    } else if (data['packageData'] != null) {
      final packageData = data['packageData'] as Map<String, dynamic>;
      if (packageData['packageDetails'] != null) {
        final packageDetails = packageData['packageDetails'] as Map<String, dynamic>;
        title = packageDetails['itemName'] ?? packageDetails['category'] ?? 'Package';
      }
    }

    return ShipmentData(
      id: data['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      trackingNumber: data['trackingNumber'] ?? '#RO00000',
      status: status,
      trackingSteps: trackingSteps,
    );
  }

  /// Get shipment status from string
  ShipmentStatus _getShipmentStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return ShipmentStatus.pending;
      case 'in_progress':
      case 'inprogress':
        return ShipmentStatus.inProgress;
      case 'completed':
        return ShipmentStatus.completed;
      case 'cancelled':
        return ShipmentStatus.cancelled;
      default:
        return ShipmentStatus.pending;
    }
  }

  /// Generate tracking steps based on status and data
  List<TrackingStep> _generateTrackingSteps(ShipmentStatus status, Map<String, dynamic> data) {
    final steps = <TrackingStep>[];

    // Get pickup and receiver addresses
    String pickupAddress = '';
    String receiverAddress = '';

    if (data['pickupData'] != null) {
      final pickup = data['pickupData'] as Map<String, dynamic>;
      pickupAddress = '${pickup['fullAddress'] ?? ''}, ${pickup['state'] ?? ''}';
    }

    if (data['receiverData'] != null) {
      final receiver = data['receiverData'] as Map<String, dynamic>;
      receiverAddress = '${receiver['address'] ?? ''}, ${receiver['state'] ?? ''}';
    }

    // Add steps based on status
    steps.add(TrackingStep(
      title: 'Order placed',
      time: _formatTimestamp(data['timestamp']),
      isCompleted: true,
    ));

    if (status == ShipmentStatus.inProgress || status == ShipmentStatus.completed) {
      steps.add(TrackingStep(
        title: 'Rider picked up package from sender location',
        address: pickupAddress.isNotEmpty ? pickupAddress : null,
        isCompleted: true,
      ));

      steps.add(TrackingStep(
        title: 'Package in transit',
        isCompleted: status == ShipmentStatus.completed,
      ));
    }

    if (status == ShipmentStatus.completed) {
      steps.add(TrackingStep(
        title: 'Package delivered to destination',
        address: receiverAddress.isNotEmpty ? receiverAddress : null,
        isCompleted: true,
      ));
    } else if (status == ShipmentStatus.pending) {
      steps.add(TrackingStep(
        title: 'Awaiting pickup',
        isCompleted: false,
      ));
    }

    return steps;
  }

  /// Format timestamp for display
  String? _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return null;

    try {
      final dateTime = timestamp is int
          ? DateTime.fromMillisecondsSinceEpoch(timestamp)
          : DateTime.parse(timestamp.toString());

      return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return null;
    }
  }

  /// Public method to refresh home view data (can be called from other parts of the app)
  void refreshHomeData() {
    _loadShipmentData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 7% gap from top of screen
              SizedBox(height: MediaQuery.of(context).size.height * 0.07),

              // Header with user profile and notification
              _buildHeader(context),

              SizedBox(height: _getSpacing(context, 32)),

              // Send package button
              _buildSendPackageButton(context),

              SizedBox(height: _getSpacing(context, 32)),

              // Analytics component
              HomeAnalytics(
                onRefreshCallback: (refreshCallback) {
                  _refreshAnalytics = refreshCallback;
                },
              ),

              SizedBox(height: _getSpacing(context, 32)),

              // Current shipments
              _isLoading
                  ? _buildLoadingWidget(context)
                  : CurrentShipmentsWidget(
                      shipments: _currentShipments,
                      maxShipments: 1, // Only show one shipment at a time as per user preference
                      onSeeAllTap: () {
                        // Navigate to shipment page (index 1 in bottom navigation)
                        // This will trigger the parent dashboard to switch to shipment tab
                        _navigateToShipmentView(context);
                      },
                      onShipmentTap: (shipment) {
                        // Handle shipment tap - navigate to shipment details
                        // TODO: Implement navigation to shipment details
                      },
                    ),

              SizedBox(height: _getSpacing(context, 32)),

              // History section
              _buildHistorySection(context),

              SizedBox(height: _getSpacing(context, 100)), // Extra space for bottom nav
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // User profile section
          Row(
            children: [
              Container(
                width: _getIconSize(context, 51),
                height: _getIconSize(context, 51),
                decoration: const ShapeDecoration(
                  image: DecorationImage(
                    image: AssetImage("assets/icons/profileplaceholder.png"),
                    fit: BoxFit.cover,
                  ),
                  shape: OvalBorder(),
                ),
              ),
              SizedBox(width: _getSpacing(context, 17)),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Location',
                    style: TextStyle(
                      color: const Color(0xFF393939),
                      fontSize: _getFontSize(context, 15),
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  SizedBox(height: _getSpacing(context, 2)),
                  Text(
                    'Ibadan, Mokola',
                    style: TextStyle(
                      color: AppColors.black,
                      fontSize: _getFontSize(context, 16),
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],
          ),

          // Notification icon
          _buildNotificationIcon(context),
        ],
      ),
    );
  }

  Widget _buildNotificationIcon(BuildContext context) {
    return SizedBox(
      width: _getIconSize(context, 49),
      height: _getIconSize(context, 49),
      child: Stack(
        children: [
          Positioned(
            left: 0,
            top: 0,
            child: Container(
              width: _getIconSize(context, 49),
              height: _getIconSize(context, 49),
              decoration: const ShapeDecoration(
                color: AppColors.white,
                shape: OvalBorder(),
              ),
              child: Icon(
                Icons.notifications_outlined,
                size: _getIconSize(context, 24),
                color: AppColors.black.withValues(alpha: 0.7),
              ),
            ),
          ),
          Positioned(
            right: _getSpacing(context, 2.5),
            top: _getSpacing(context, 2.5),
            child: Container(
              width: _getIconSize(context, 12.89),
              height: _getIconSize(context, 12.89),
              decoration: const ShapeDecoration(
                color: Color(0xF2FF001F),
                shape: OvalBorder(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSendPackageButton(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: GestureDetector(
        onTap: () => _navigateToSendPackage(context),
        child: Container(
          width: double.infinity,
          height: _getSpacing(context, 70),
          padding: EdgeInsets.symmetric(
            horizontal: _getSpacing(context, 20),
            vertical: _getSpacing(context, 10),
          ),
          decoration: ShapeDecoration(
            color: AppColors.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 44)),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: _getSpacing(context, 7),
                      vertical: _getSpacing(context, 6),
                    ),
                    decoration: ShapeDecoration(
                      color: const Color(0xFFF5F5FF),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(_getBorderRadius(context, 40)),
                      ),
                    ),
                    child: Icon(
                      Icons.local_shipping_outlined,
                      size: _getIconSize(context, 24),
                      color: AppColors.primary,
                    ),
                  ),
                  SizedBox(width: _getSpacing(context, 12)),
                  Text(
                    'Send a package',
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: _getFontSize(context, 17),
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w500,
                      height: 1.29,
                      letterSpacing: -1,
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Icon(
                    Icons.keyboard_double_arrow_right,
                    size: _getIconSize(context, 24),
                    color: AppColors.white,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHistorySection(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'History',
                style: TextStyle(
                  color: AppColors.black,
                  fontSize: _getFontSize(context, 20),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                  height: 0.95,
                  letterSpacing: -1,
                ),
              ),
              GestureDetector(
                onTap: () => _navigateToShipmentView(context),
                child: Text(
                  'See all',
                  style: TextStyle(
                    color: AppColors.primary,
                    fontSize: _getFontSize(context, 15),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: _getSpacing(context, 16)),

          // Show shipment history or empty state
          if (_isLoading) ...[
            _buildLoadingWidget(context),
          ] else if (_shipmentHistory.isNotEmpty) ...[
            // Show the most recent history item
            ShipmentCard(
              shipment: _shipmentHistory.first,
              showTrackingTimeline: false, // Don't show timeline in home view
              onCardTap: () => _navigateToShipmentView(context),
            ),
          ] else ...[
            // Empty state
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(_getSpacing(context, 24)),
              decoration: BoxDecoration(
                color: AppColors.black.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(_getBorderRadius(context, 16)),
              ),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.history,
                      size: _getIconSize(context, 48),
                      color: AppColors.black.withValues(alpha: 0.4),
                    ),
                    SizedBox(height: _getSpacing(context, 12)),
                    Text(
                      'No recent activity',
                      style: TextStyle(
                        color: AppColors.black.withValues(alpha: 0.6),
                        fontSize: _getFontSize(context, 16),
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: _getSpacing(context, 4)),
                    Text(
                      'Your package history will appear here',
                      style: TextStyle(
                        color: AppColors.black.withValues(alpha: 0.5),
                        fontSize: _getFontSize(context, 14),
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _navigateToShipmentView(BuildContext context) {
    // Use callback to switch to shipment tab if available
    if (widget.onNavigateToShipment != null) {
      widget.onNavigateToShipment!();
    } else {
      // Fallback: Navigate to shipment view as a new route
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const ShipmentView(),
        ),
      );
    }
  }

  void _navigateToSendPackage(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SendPackageView(),
      ),
    );
  }

  /// Build loading widget
  Widget _buildLoadingWidget(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 24)),
      decoration: BoxDecoration(
        color: AppColors.black.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 16)),
      ),
      child: Center(
        child: CircularProgressIndicator(
          color: AppColors.primary,
          strokeWidth: 2,
        ),
      ),
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 40; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet
    } else {
      spacing = baseSpacing; // Mobile
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2; // Tablet
    } else {
      fontSize = baseFontSize; // Mobile
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2; // Tablet
    } else {
      iconSize = baseIconSize; // Mobile
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2; // Tablet
    } else {
      borderRadius = baseBorderRadius; // Mobile
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }


}
