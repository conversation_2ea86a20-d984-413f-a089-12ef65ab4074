/// Response model for user sign-up API call
class SignUpResponse {
  final bool success;
  final String message;
  final String? userId;
  final String? token;
  final Map<String, dynamic>? userData;

  const SignUpResponse({
    required this.success,
    required this.message,
    this.userId,
    this.token,
    this.userData,
  });

  /// Create a SignUpResponse from JSON data
  factory SignUpResponse.fromJson(Map<String, dynamic> json) {
    return SignUpResponse(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      userId: json['userId'] as String?,
      token: json['token'] as String?,
      userData: json['userData'] as Map<String, dynamic>?,
    );
  }

  /// Convert the response to JSON format
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      if (userId != null) 'userId': userId,
      if (token != null) 'token': token,
      if (userData != null) 'userData': userData,
    };
  }

  /// Create a copy of this response with some fields updated
  SignUpResponse copyWith({
    bool? success,
    String? message,
    String? userId,
    String? token,
    Map<String, dynamic>? userData,
  }) {
    return SignUpResponse(
      success: success ?? this.success,
      message: message ?? this.message,
      userId: userId ?? this.userId,
      token: token ?? this.token,
      userData: userData ?? this.userData,
    );
  }

  @override
  String toString() {
    return 'SignUpResponse(success: $success, message: $message, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SignUpResponse &&
        other.success == success &&
        other.message == message &&
        other.userId == userId &&
        other.token == token;
  }

  @override
  int get hashCode {
    return Object.hash(success, message, userId, token);
  }
}
