import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/providers/toast_provider.dart';

/// Notifications settings view for managing notification preferences
class NotificationsView extends StatefulWidget {
  const NotificationsView({super.key});

  @override
  State<NotificationsView> createState() => _NotificationsViewState();
}

class _NotificationsViewState extends State<NotificationsView> {
  // Notification settings
  bool _pushNotifications = true;
  bool _emailNotifications = true;
  bool _smsNotifications = false;
  bool _newOrderNotifications = true;
  bool _orderUpdatesNotifications = true;
  bool _paymentNotifications = true;
  bool _promotionalNotifications = false;
  bool _systemNotifications = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _doNotDisturbEnabled = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5FF),
      appBar: _buildAppBar(context),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(_getHorizontalPadding(context)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: _getSpacing(context, 20)),

              // General notification settings
              _buildGeneralSettings(context),

              SizedBox(height: _getSpacing(context, 32)),

              // Delivery notifications
              _buildDeliveryNotifications(context),

              SizedBox(height: _getSpacing(context, 32)),

              // Sound & vibration settings
              _buildSoundVibrationSettings(context),

              SizedBox(height: _getSpacing(context, 32)),

              // Do not disturb
              _buildDoNotDisturbSettings(context),

              SizedBox(height: _getSpacing(context, 32)),

              // Save button
              _buildSaveButton(context),

              SizedBox(height: _getSpacing(context, 20)),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: const Color(0xFFF5F5FF),
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: AppColors.black,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        'Notifications',
        style: TextStyle(
          fontSize: _getFontSize(context, 18),
          fontWeight: FontWeight.w600,
          fontFamily: 'Poppins',
          color: AppColors.black,
        ),
      ),
      centerTitle: true,
    );
  }

  Widget _buildGeneralSettings(BuildContext context) {
    return _buildSection(
      context,
      title: 'General Settings',
      children: [
        _buildToggleItem(
          context,
          title: 'Push Notifications',
          subtitle: 'Receive notifications on your device',
          value: _pushNotifications,
          onChanged: (value) => setState(() => _pushNotifications = value),
        ),
        _buildToggleItem(
          context,
          title: 'Email Notifications',
          subtitle: 'Receive notifications via email',
          value: _emailNotifications,
          onChanged: (value) => setState(() => _emailNotifications = value),
        ),
        _buildToggleItem(
          context,
          title: 'SMS Notifications',
          subtitle: 'Receive notifications via SMS',
          value: _smsNotifications,
          onChanged: (value) => setState(() => _smsNotifications = value),
        ),
      ],
    );
  }

  Widget _buildDeliveryNotifications(BuildContext context) {
    return _buildSection(
      context,
      title: 'Delivery Notifications',
      children: [
        _buildToggleItem(
          context,
          title: 'New Orders',
          subtitle: 'Get notified when new delivery orders are available',
          value: _newOrderNotifications,
          onChanged: (value) => setState(() => _newOrderNotifications = value),
        ),
        _buildToggleItem(
          context,
          title: 'Order Updates',
          subtitle: 'Get notified about order status changes',
          value: _orderUpdatesNotifications,
          onChanged: (value) => setState(() => _orderUpdatesNotifications = value),
        ),
        _buildToggleItem(
          context,
          title: 'Payment Notifications',
          subtitle: 'Get notified about payment confirmations',
          value: _paymentNotifications,
          onChanged: (value) => setState(() => _paymentNotifications = value),
        ),
        _buildToggleItem(
          context,
          title: 'Promotional Offers',
          subtitle: 'Get notified about special offers and bonuses',
          value: _promotionalNotifications,
          onChanged: (value) => setState(() => _promotionalNotifications = value),
        ),
        _buildToggleItem(
          context,
          title: 'System Notifications',
          subtitle: 'Get notified about app updates and maintenance',
          value: _systemNotifications,
          onChanged: (value) => setState(() => _systemNotifications = value),
        ),
      ],
    );
  }

  Widget _buildSoundVibrationSettings(BuildContext context) {
    return _buildSection(
      context,
      title: 'Sound & Vibration',
      children: [
        _buildToggleItem(
          context,
          title: 'Sound',
          subtitle: 'Play sound for notifications',
          value: _soundEnabled,
          onChanged: (value) => setState(() => _soundEnabled = value),
        ),
        _buildToggleItem(
          context,
          title: 'Vibration',
          subtitle: 'Vibrate for notifications',
          value: _vibrationEnabled,
          onChanged: (value) => setState(() => _vibrationEnabled = value),
        ),
      ],
    );
  }

  Widget _buildDoNotDisturbSettings(BuildContext context) {
    return _buildSection(
      context,
      title: 'Do Not Disturb',
      children: [
        _buildToggleItem(
          context,
          title: 'Do Not Disturb Mode',
          subtitle: 'Silence all notifications during specified hours',
          value: _doNotDisturbEnabled,
          onChanged: (value) => setState(() => _doNotDisturbEnabled = value),
        ),
        if (_doNotDisturbEnabled) ...[
          SizedBox(height: _getSpacing(context, 16)),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(_getSpacing(context, 16)),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
              border: Border.all(
                color: AppColors.primary.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Quiet Hours',
                  style: TextStyle(
                    fontSize: _getFontSize(context, 16),
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Poppins',
                    color: AppColors.black,
                  ),
                ),
                SizedBox(height: _getSpacing(context, 8)),
                Text(
                  'Set the hours when you don\'t want to receive notifications',
                  style: TextStyle(
                    fontSize: _getFontSize(context, 14),
                    fontFamily: 'Poppins',
                    color: AppColors.black.withValues(alpha: 0.6),
                  ),
                ),
                SizedBox(height: _getSpacing(context, 16)),
                Row(
                  children: [
                    Expanded(
                      child: _buildTimeSelector(context, 'From', '22:00'),
                    ),
                    SizedBox(width: _getSpacing(context, 16)),
                    Expanded(
                      child: _buildTimeSelector(context, 'To', '07:00'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSection(BuildContext context, {required String title, required List<Widget> children}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: _getFontSize(context, 18),
            fontWeight: FontWeight.w600,
            fontFamily: 'Poppins',
            color: AppColors.black,
          ),
        ),
        SizedBox(height: _getSpacing(context, 16)),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
            border: Border.all(
              color: AppColors.black.withValues(alpha: 0.1),
            ),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildToggleItem(
    BuildContext context, {
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      padding: EdgeInsets.all(_getSpacing(context, 16)),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.black.withValues(alpha: 0.05),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: _getFontSize(context, 16),
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Poppins',
                    color: AppColors.black,
                  ),
                ),
                SizedBox(height: _getSpacing(context, 4)),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: _getFontSize(context, 14),
                    fontFamily: 'Poppins',
                    color: AppColors.black.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.primary,
            activeTrackColor: AppColors.primary.withValues(alpha: 0.3),
            inactiveThumbColor: AppColors.black.withValues(alpha: 0.3),
            inactiveTrackColor: AppColors.black.withValues(alpha: 0.1),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSelector(BuildContext context, String label, String time) {
    return GestureDetector(
      onTap: () {
        Toast.info('Time picker functionality to be implemented');
      },
      child: Container(
        padding: EdgeInsets.all(_getSpacing(context, 12)),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
          border: Border.all(
            color: AppColors.black.withValues(alpha: 0.2),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: _getFontSize(context, 12),
                fontFamily: 'Poppins',
                color: AppColors.black.withValues(alpha: 0.6),
              ),
            ),
            SizedBox(height: _getSpacing(context, 4)),
            Text(
              time,
              style: TextStyle(
                fontSize: _getFontSize(context, 16),
                fontWeight: FontWeight.w600,
                fontFamily: 'Poppins',
                color: AppColors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _saveNotificationSettings,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
          padding: EdgeInsets.symmetric(
            vertical: _getSpacing(context, 16),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
          ),
          elevation: 0,
        ),
        child: Text(
          'Save Settings',
          style: TextStyle(
            fontSize: _getFontSize(context, 16),
            fontWeight: FontWeight.w600,
            fontFamily: 'Poppins',
          ),
        ),
      ),
    );
  }

  void _saveNotificationSettings() {
    // TODO: Implement save functionality
    Toast.success('Notification settings saved successfully');
    Navigator.pop(context);
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16;
    } else if (screenWidth > 600) {
      basePadding = 40;
    } else {
      basePadding = 24;
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8;
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2;
    } else {
      fontSize = baseFontSize;
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6;
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2;
    } else {
      borderRadius = baseBorderRadius;
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }
}
