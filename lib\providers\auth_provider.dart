import 'package:flutter/foundation.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/models/auth/sign_in_request.dart';
import 'package:rideoon/models/auth/sign_up_request.dart';
import 'package:rideoon/models/auth/account.dart';
import 'package:rideoon/models/api_response.dart';

/// Provider for managing authentication state across the application
///
/// This provider handles:
/// - User authentication (sign in, sign up, sign out)
/// - User account data management
/// - Authentication state tracking
/// - Remember me functionality
/// - Token management
class AuthProvider extends ChangeNotifier {

  Account? _currentUser;
  String? _authToken;
  bool _isAuthenticated = false;
  bool _isLoading = false;
  String? _error;

  /// Get current authenticated user
  Account? get currentUser => _currentUser;

  /// Get current auth token
  String? get authToken => _authToken;

  /// Check if user is authenticated
  bool get isAuthenticated => _isAuthenticated;

  /// Check if currently loading
  bool get isLoading => _isLoading;

  /// Get current error message
  String? get error => _error;

  /// Get user's full name
  String get userFullName {
    if (_currentUser != null) {
      return '${_currentUser!.firstName} ${_currentUser!.lastName}'.trim();
    }
    return 'User';
  }

  /// Get user's email
  String get userEmail => _currentUser?.email ?? '<EMAIL>';

  /// Get user's phone number
  String get userPhoneNumber => _currentUser?.phoneNumber ?? '+**********';

  /// Get user's avatar URL
  String? get userAvatar => _currentUser?.avatar;

  /// Check if user is a client
  bool get isClient => _currentUser?.isClient ?? false;

  /// Check if user is a rider
  bool get isRider => _currentUser?.isRider ?? false;

  /// Initialize authentication state from stored data
  Future<void> initializeAuth() async {
    try {
      _setLoading(true);
      _clearError();

      // Load stored authentication data
      final authToken = await AuthService.getAuthToken();
      final userAccount = await AuthService.getUserAccount();

      if (authToken != null && userAccount != null) {
        _authToken = authToken;
        _currentUser = userAccount;
        _isAuthenticated = true;
      } else {
        _clearAuthData();
      }
    } catch (e) {
      _setError('Failed to initialize authentication: $e');
      _clearAuthData();
    } finally {
      _setLoading(false);
    }
  }

  /// Sign in user
  Future<bool> signIn(SignInRequest request, {bool rememberMe = false}) async {
    try {
      _setLoading(true);
      _clearError();

      final response = await AuthService.signIn(request);
      
      if (response.success && response.data != null) {
        _authToken = response.data!.token;
        _currentUser = response.data!.account;
        _isAuthenticated = true;

        // Save authentication data
        await AuthService.saveAuthData(
          token: _authToken!,
          account: _currentUser!,
        );

        // Handle remember me
        if (rememberMe) {
          await AuthService.saveRememberMeCredentials(
            request.email,
            request.password,
          );
        }

        notifyListeners();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('Sign in failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Sign up user
  Future<bool> signUp(SignUpRequest request) async {
    try {
      _setLoading(true);
      _clearError();

      final response = await AuthService.signUp(request);

      if (response.success) {
        // For sign-up, we typically don't get full auth data immediately
        // The user needs to verify their account first
        // So we don't set authentication state here

        notifyListeners();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('Sign up failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Sign out user
  Future<void> signOut() async {
    try {
      _setLoading(true);

      // Clear stored authentication data
      await AuthService.signOut();

      _clearAuthData();
      notifyListeners();
    } catch (e) {
      _setError('Sign out failed: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update user profile data (simplified version)
  Future<bool> updateProfile({
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    String? avatar,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      if (_currentUser != null) {
        // For now, just update the local data
        // In a real app, you would make an API call to update the server

        // Create updated user object with all required fields
        final updatedUser = Account(
          uuid: _currentUser!.uuid,
          created: _currentUser!.created,
          updated: DateTime.now(),
          firstName: firstName ?? _currentUser!.firstName,
          lastName: lastName ?? _currentUser!.lastName,
          email: email ?? _currentUser!.email,
          phoneNumber: phoneNumber ?? _currentUser!.phoneNumber,
          avatar: avatar ?? _currentUser!.avatar,
          address: _currentUser!.address,
          state: _currentUser!.state,
          secured: _currentUser!.secured,
          verified: _currentUser!.verified,
          type: _currentUser!.type,
        );

        // Update local state
        _currentUser = updatedUser;

        // Save updated data
        await AuthService.saveAuthData(
          token: _authToken!,
          account: _currentUser!,
        );

        notifyListeners();
        return true;
      }

      _setError('No user data to update');
      return false;
    } catch (e) {
      _setError('Profile update failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh user data from server
  Future<void> refreshUserData() async {
    try {
      _setLoading(true);
      _clearError();

      if (_authToken != null) {
        // In a real app, you would make an API call to get fresh user data
        // For now, we'll just reload from stored data
        final userAccount = await AuthService.getUserAccount();
        if (userAccount != null) {
          _currentUser = userAccount;
          notifyListeners();
        }
      }
    } catch (e) {
      _setError('Failed to refresh user data: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Clear authentication data
  void _clearAuthData() {
    _currentUser = null;
    _authToken = null;
    _isAuthenticated = false;
  }

  /// Helper method to set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Helper method to set error
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Helper method to clear error
  void _clearError() {
    _error = null;
  }
}
