import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/views/dashboards/user_dashboard/messages_view/chat_view.dart';

/// Messages list view page for user dashboard
///
/// This page displays all conversations with riders and support,
/// showing profile pictures, names, and message previews.
class MessagesView extends StatefulWidget {
  const MessagesView({super.key});

  @override
  State<MessagesView> createState() => _MessagesViewState();
}

class _MessagesViewState extends State<MessagesView> {
  // Sample conversations data
  final List<Map<String, dynamic>> _conversations = [
    {
      'id': '1',
      'name': 'Customer Support',
      'lastMessage': 'Welcome to RideOn! How can we help you today?',
      'timestamp': '2024-12-17 09:00:00',
      'unreadCount': 0,
      'isOnline': true,
      'type': 'support',
      'avatar': null,
    },
    {
      'id': '2',
      'name': '<PERSON>',
      'lastMessage': 'I\'m on my way to pick up your package',
      'timestamp': '2024-12-17 10:30:00',
      'unreadCount': 1,
      'isOnline': true,
      'type': 'rider',
      'avatar': null,
    },
    {
      'id': '3',
      'name': 'Sarah Delivery',
      'lastMessage': 'Package delivered successfully! Thank you for using RideOn.',
      'timestamp': '2024-12-17 08:45:00',
      'unreadCount': 0,
      'isOnline': false,
      'type': 'rider',
      'avatar': null,
    },
    {
      'id': '4',
      'name': 'Mike Express',
      'lastMessage': 'Could you please provide more details about the pickup location?',
      'timestamp': '2024-12-16 16:20:00',
      'unreadCount': 2,
      'isOnline': false,
      'type': 'rider',
      'avatar': null,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5FF),
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 7% gap from top of screen
            SizedBox(height: MediaQuery.of(context).size.height * 0.07),

            // Header
            _buildHeader(context),

            SizedBox(height: _getSpacing(context, 16)),

            // Subtitle
            _buildSubtitle(context),

            SizedBox(height: _getSpacing(context, 24)),

            // New chat button
            _buildNewChatButton(context),

            SizedBox(height: _getSpacing(context, 16)),

            // Conversations list
            Expanded(
              child: _buildConversationsList(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Text(
        'Messages',
        style: TextStyle(
          fontSize: _getFontSize(context, 28),
          fontWeight: FontWeight.bold,
          fontFamily: 'Poppins',
          color: AppColors.black,
        ),
      ),
    );
  }

  Widget _buildSubtitle(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Text(
        'Chat with riders and customer support',
        style: TextStyle(
          fontSize: _getFontSize(context, 16),
          fontFamily: 'Poppins',
          color: AppColors.black.withValues(alpha: 0.6),
        ),
      ),
    );
  }

  Widget _buildNewChatButton(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: () {
            _startNewChatWithSupport(context);
          },
          icon: Icon(
            Icons.add_comment,
            size: _getIconSize(context, 20),
            color: AppColors.white,
          ),
          label: Text(
            'New Customer Support Chat',
            style: TextStyle(
              fontSize: _getFontSize(context, 16),
              fontWeight: FontWeight.w600,
              fontFamily: 'Poppins',
              color: AppColors.white,
            ),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.white,
            padding: EdgeInsets.symmetric(
              horizontal: _getSpacing(context, 20),
              vertical: _getSpacing(context, 16),
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
            ),
            elevation: 0,
          ),
        ),
      ),
    );
  }

  Widget _buildConversationsList(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: ListView.builder(
        itemCount: _conversations.length,
        itemBuilder: (context, index) {
          final conversation = _conversations[index];
          return Padding(
            padding: EdgeInsets.only(bottom: _getSpacing(context, 12)),
            child: _buildConversationCard(context, conversation),
          );
        },
      ),
    );
  }

  Widget _buildConversationCard(BuildContext context, Map<String, dynamic> conversation) {
    final unreadCount = conversation['unreadCount'] as int;
    final isOnline = conversation['isOnline'] as bool;
    final conversationType = conversation['type'] as String;

    return GestureDetector(
      onTap: () {
        _openChat(context, conversation);
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(_getSpacing(context, 16)),
        decoration: ShapeDecoration(
          color: const Color(0xFFFEFEFE),
          shape: RoundedRectangleBorder(
            side: BorderSide(
              width: 1,
              color: AppColors.black.withValues(alpha: 0.07),
            ),
            borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
          ),
        ),
        child: Row(
          children: [
            // Avatar with online indicator
            Stack(
              children: [
                Container(
                  width: _getIconSize(context, 50),
                  height: _getIconSize(context, 50),
                  decoration: BoxDecoration(
                    color: _getAvatarColor(conversationType),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _getAvatarIcon(conversationType),
                    size: _getIconSize(context, 24),
                    color: AppColors.white,
                  ),
                ),
                if (isOnline)
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      width: _getIconSize(context, 14),
                      height: _getIconSize(context, 14),
                      decoration: BoxDecoration(
                        color: AppColors.success,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: AppColors.white,
                          width: 2,
                        ),
                      ),
                    ),
                  ),
              ],
            ),

            SizedBox(width: _getSpacing(context, 12)),

            // Message content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        conversation['name'] as String,
                        style: TextStyle(
                          fontSize: _getFontSize(context, 16),
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Poppins',
                          color: AppColors.black,
                        ),
                      ),
                      Text(
                        _formatTimestamp(conversation['timestamp'] as String),
                        style: TextStyle(
                          fontSize: _getFontSize(context, 12),
                          fontFamily: 'Poppins',
                          color: AppColors.black.withValues(alpha: 0.5),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: _getSpacing(context, 4)),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          conversation['lastMessage'] as String,
                          style: TextStyle(
                            fontSize: _getFontSize(context, 14),
                            fontFamily: 'Poppins',
                            color: AppColors.black.withValues(alpha: 0.7),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (unreadCount > 0) ...[
                        SizedBox(width: _getSpacing(context, 8)),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: _getSpacing(context, 8),
                            vertical: _getSpacing(context, 4),
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(_getBorderRadius(context, 10)),
                          ),
                          child: Text(
                            unreadCount.toString(),
                            style: TextStyle(
                              fontSize: _getFontSize(context, 10),
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Poppins',
                              color: AppColors.white,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getAvatarColor(String conversationType) {
    switch (conversationType) {
      case 'support':
        return AppColors.primary;
      case 'rider':
        return AppColors.success;
      default:
        return AppColors.black.withValues(alpha: 0.6);
    }
  }

  IconData _getAvatarIcon(String conversationType) {
    switch (conversationType) {
      case 'support':
        return Icons.support_agent;
      case 'rider':
        return Icons.delivery_dining;
      default:
        return Icons.person;
    }
  }

  String _formatTimestamp(String timestamp) {
    final dateTime = DateTime.parse(timestamp);
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  // Helper methods
  void _startNewChatWithSupport(BuildContext context) {
    final supportConversation = {
      'id': 'support_${DateTime.now().millisecondsSinceEpoch}',
      'name': 'Customer Support',
      'type': 'support',
      'isOnline': true,
    };

    _openChat(context, supportConversation);
  }

  void _openChat(BuildContext context, Map<String, dynamic> conversation) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatView(
          conversationId: conversation['id'] as String,
          conversationName: conversation['name'] as String,
          conversationType: conversation['type'] as String,
        ),
      ),
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16;
    } else if (screenWidth > 600) {
      basePadding = 40;
    } else {
      basePadding = 24;
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8;
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2;
    } else {
      fontSize = baseFontSize;
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8;
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2;
    } else {
      iconSize = baseIconSize;
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6;
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2;
    } else {
      borderRadius = baseBorderRadius;
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }
}
