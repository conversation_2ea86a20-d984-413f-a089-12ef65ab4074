import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/services/onboarding_navigation_service.dart';
import 'package:rideoon/views/onboarding/onboarding_page.dart';
import 'package:rideoon/views/authentication/userauth/sign_up.dart';
import 'package:rideoon/services/onboarding_wrapper.dart';

class OnboardingContainer extends StatefulWidget {
  const OnboardingContainer({super.key});

  @override
  State<OnboardingContainer> createState() => _OnboardingContainerState();
}

class _OnboardingContainerState extends State<OnboardingContainer>
    with SingleTickerProviderStateMixin {
  late PageController _pageController;
  late OnboardingNavigationService _navigationService;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<OnboardingPageData> _pages = [
    OnboardingPageData(
      illustration: 'assets/illustrations/onboarding_one.png',
      title: 'Do More Logistics',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.',
      fallbackIcon: Icons.delivery_dining,
    ),
    OnboardingPageData(
      illustration: 'assets/illustrations/onboarding_2.png',
      title: 'Fast & Reliable',
      description: 'Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore.',
      fallbackIcon: Icons.local_shipping,
    ),
    OnboardingPageData(
      illustration: 'assets/illustrations/onboarding_3.png',
      title: 'Track Everything',
      description: 'Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Sed ut perspiciatis unde omnis iste natus error sit voluptatem.',
      fallbackIcon: Icons.track_changes,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _pageController = PageController();
    _navigationService = OnboardingNavigationService();
    _navigationService.initialize(_pageController);

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _navigationService.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _skipOnboarding() async {
    // Set default user type as 'client' and mark onboarding as completed
    await OnboardingService.setUserType('client');
    await OnboardingService.setOnboardingCompleted();

    // Navigate directly to UserSignUp
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const UserSignUp(),
      ),
    );
  }

  void _handlePageChanged(int page) {
    _navigationService.updateCurrentPage(page);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            children: [
              // Page indicators
              _buildPageIndicators(),

              // Main content area
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: _handlePageChanged,
                  itemCount: _pages.length,
                  itemBuilder: (context, index) {
                    return OnboardingPage(
                      data: _pages[index],
                      navigationService: _navigationService,
                      onSkip: _skipOnboarding,
                      onNext: () {
                        if (_navigationService.isLastPage) {
                          _skipOnboarding();
                        } else {
                          _navigationService.nextPage();
                        }
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPageIndicators() {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: _getSpacing(context, 20),
      ),
      child: OnboardingPageIndicator(
        service: _navigationService,
        activeColor: AppColors.black,
        inactiveColor: AppColors.black.withValues(alpha: 0.3),
        size: _getIndicatorSize(context),
        spacing: _getSpacing(context, 8),
      ),
    );
  }

  // Responsive helper methods
  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet
    } else {
      spacing = baseSpacing; // Mobile
    }

    // Reduce spacing for short screens
    if (isShortScreen) {
      spacing = spacing * 0.7;
    }

    return spacing;
  }

  double _getIndicatorSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 6; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 10; // Tablet
    } else {
      baseSize = 8; // Mobile
    }

    // Reduce indicator size for short screens
    if (isShortScreen) {
      baseSize = baseSize * 0.8;
    }

    return baseSize;
  }
}

/// Data class for onboarding page content
class OnboardingPageData {
  final String illustration;
  final String title;
  final String description;
  final IconData fallbackIcon;

  const OnboardingPageData({
    required this.illustration,
    required this.title,
    required this.description,
    required this.fallbackIcon,
  });
}
