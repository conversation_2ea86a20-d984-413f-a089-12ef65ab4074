import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';

/// A dialog widget for selecting remember me duration
///
/// This widget displays a modal dialog allowing users to select how long
/// their login credentials should be remembered, with options for 3, 7, 14, and 30 days.
///
/// Features:
/// - Responsive design for mobile, tablet, and smartwatch
/// - Clear duration options with explanatory text
/// - Consistent styling with app theme
/// - Accessibility support
class RememberMeDialog extends StatefulWidget {
  /// The title text displayed at the top of the dialog
  final String title;

  /// The description text explaining the remember me functionality
  final String description;

  /// The text for the cancel button (default: "Cancel")
  final String cancelButtonText;

  /// The text for the confirm button (default: "Save")
  final String confirmButtonText;

  /// Callback function when the cancel button is pressed
  final VoidCallback? onCancel;

  /// Callback function when the confirm button is pressed with selected duration
  final Function(int durationDays)? onConfirm;

  /// Whether to show the dialog with a barrier that can be dismissed
  final bool barrierDismissible;

  /// The icon to display at the top of the dialog
  final IconData? icon;

  /// The color of the icon background
  final Color? iconBackgroundColor;

  /// The color of the icon
  final Color? iconColor;

  /// Default selected duration in days
  final int defaultDuration;

  const RememberMeDialog({
    super.key,
    this.title = 'Remember Me Duration',
    this.description = 'Choose how long you want your login credentials to be remembered. After this period, you\'ll need to enter your password again.',
    this.cancelButtonText = 'Cancel',
    this.confirmButtonText = 'Save',
    this.onCancel,
    this.onConfirm,
    this.barrierDismissible = true,
    this.icon,
    this.iconBackgroundColor,
    this.iconColor,
    this.defaultDuration = 7,
  });

  /// Static method to show the remember me duration dialog
  static Future<int?> show(
    BuildContext context, {
    String? title,
    String? description,
    String? cancelButtonText,
    String? confirmButtonText,
    VoidCallback? onCancel,
    Function(int durationDays)? onConfirm,
    bool barrierDismissible = true,
    IconData? icon,
    Color? iconBackgroundColor,
    Color? iconColor,
    int defaultDuration = 7,
  }) {
    return showDialog<int>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (BuildContext context) => RememberMeDialog(
        title: title ?? 'Remember Me Duration',
        description: description ?? 'Choose how long you want your login credentials to be remembered. After this period, you\'ll need to enter your password again.',
        cancelButtonText: cancelButtonText ?? 'Cancel',
        confirmButtonText: confirmButtonText ?? 'Save',
        onCancel: onCancel,
        onConfirm: onConfirm,
        barrierDismissible: barrierDismissible,
        icon: icon,
        iconBackgroundColor: iconBackgroundColor,
        iconColor: iconColor,
        defaultDuration: defaultDuration,
      ),
    );
  }

  @override
  State<RememberMeDialog> createState() => _RememberMeDialogState();
}

class _RememberMeDialogState extends State<RememberMeDialog> {
  late int _selectedDuration;

  final List<Map<String, dynamic>> _durationOptions = [
    {'days': 3, 'label': '3 days', 'description': 'Short term'},
    {'days': 7, 'label': '1 week', 'description': 'Recommended'},
    {'days': 14, 'label': '2 weeks', 'description': 'Extended'},
    {'days': 30, 'label': '1 month', 'description': 'Maximum'},
  ];

  @override
  void initState() {
    super.initState();
    _selectedDuration = widget.defaultDuration;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: _buildDialogContent(context),
    );
  }

  Widget _buildDialogContent(BuildContext context) {
    return Container(
      width: _getDialogWidth(context),
      constraints: BoxConstraints(maxWidth: _getMaxWidth(context)),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(_getBorderRadius(context)),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: _getSpacing(context, 20),
            offset: Offset(0, _getSpacing(context, 8)),
            spreadRadius: _getSpacing(context, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(_getContentPadding(context)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon
            Container(
              width: _getIconSize(context),
              height: _getIconSize(context),
              decoration: BoxDecoration(
                color: (widget.iconBackgroundColor ?? AppColors.primary).withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                widget.icon ?? Icons.schedule,
                size: _getIconInnerSize(context),
                color: widget.iconColor ?? AppColors.primary,
              ),
            ),

            SizedBox(height: _getSpacing(context, 20)),

            // Title
            _buildTitle(context),

            SizedBox(height: _getSpacing(context, 12)),

            // Description
            _buildDescription(context),

            SizedBox(height: _getSpacing(context, 24)),

            // Duration options
            _buildDurationOptions(context),

            SizedBox(height: _getSpacing(context, 24)),

            // Action buttons
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Text(
      widget.title,
      textAlign: TextAlign.center,
      style: TextStyle(
        color: AppColors.black,
        fontSize: _getTitleFontSize(context),
        fontFamily: 'Poppins',
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildDescription(BuildContext context) {
    return Text(
      widget.description,
      textAlign: TextAlign.center,
      style: TextStyle(
        color: AppColors.black.withValues(alpha: 0.7),
        fontSize: _getBodyFontSize(context),
        fontFamily: 'Poppins',
        fontWeight: FontWeight.w400,
        height: 1.4,
      ),
    );
  }

  Widget _buildDurationOptions(BuildContext context) {
    return Column(
      children: _durationOptions.map((option) {
        final isSelected = _selectedDuration == option['days'];
        return Padding(
          padding: EdgeInsets.only(bottom: _getSpacing(context, 8)),
          child: _buildDurationOption(context, option, isSelected),
        );
      }).toList(),
    );
  }

  Widget _buildDurationOption(BuildContext context, Map<String, dynamic> option, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedDuration = option['days'];
        });
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(
          horizontal: _getSpacing(context, 16),
          vertical: _getSpacing(context, 12),
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary.withValues(alpha: 0.1) : Colors.transparent,
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.black.withValues(alpha: 0.2),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
        ),
        child: Row(
          children: [
            // Radio button
            Container(
              width: _getSpacing(context, 20),
              height: _getSpacing(context, 20),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? AppColors.primary : AppColors.black.withValues(alpha: 0.4),
                  width: 2,
                ),
                color: isSelected ? AppColors.primary : Colors.transparent,
              ),
              child: isSelected
                  ? Icon(
                      Icons.check,
                      size: _getSpacing(context, 12),
                      color: AppColors.white,
                    )
                  : null,
            ),

            SizedBox(width: _getSpacing(context, 12)),

            // Duration text
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    option['label'],
                    style: TextStyle(
                      color: isSelected ? AppColors.primary : AppColors.black,
                      fontSize: _getBodyFontSize(context),
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    option['description'],
                    style: TextStyle(
                      color: AppColors.black.withValues(alpha: 0.6),
                      fontSize: _getSmallFontSize(context),
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        // Cancel button
        _buildCancelButton(context),

        SizedBox(width: _getSpacing(context, 12)),

        // Confirm button
        _buildConfirmButton(context),
      ],
    );
  }

  Widget _buildCancelButton(BuildContext context) {
    return Expanded(
      child: SizedBox(
        height: _getButtonHeight(context),
        child: OutlinedButton(
          onPressed: () {
            Navigator.of(context).pop(null);
            widget.onCancel?.call();
          },
          style: OutlinedButton.styleFrom(
            backgroundColor: AppColors.white,
            foregroundColor: AppColors.black,
            side: BorderSide(
              width: 1,
              color: AppColors.black.withValues(alpha: 0.3),
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
            ),
            padding: EdgeInsets.symmetric(vertical: _getSpacing(context, 12)),
            elevation: 0,
          ),
          child: Text(
            widget.cancelButtonText,
            style: TextStyle(
              fontSize: _getButtonFontSize(context),
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildConfirmButton(BuildContext context) {
    return Expanded(
      child: SizedBox(
        height: _getButtonHeight(context),
        child: ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop(_selectedDuration);
            widget.onConfirm?.call(_selectedDuration);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
            ),
            padding: EdgeInsets.symmetric(vertical: _getSpacing(context, 12)),
            elevation: 0,
          ),
          child: Text(
            widget.confirmButtonText,
            style: TextStyle(
              fontSize: _getButtonFontSize(context),
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  // Responsive helper methods
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return screenWidth * 0.90; // Smartwatch - 90% width
    } else if (screenWidth > 600) {
      return 360; // Tablet - wider for duration options
    } else {
      return screenWidth * 0.85; // Mobile - 85% width
    }
  }

  double _getMaxWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 280; // Smartwatch max width
    } else if (screenWidth > 600) {
      return 360; // Tablet max width
    } else {
      return 360; // Mobile max width
    }
  }

  double _getContentPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 16; // Smartwatch - smaller padding
    } else if (screenWidth > 600) {
      return 28; // Tablet
    } else {
      return 24; // Mobile - standard padding
    }
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch - reduced spacing
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.0; // Tablet - standard spacing
    } else {
      spacing = baseSpacing; // Mobile - standard spacing
    }

    if (isShortScreen) {
      spacing = spacing * 0.8; // Reduce for short screens
    }

    return spacing;
  }

  double _getIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 40; // Smartwatch - smaller icon
    } else if (screenWidth > 600) {
      return 60; // Tablet - slightly larger
    } else {
      return 56; // Mobile - standard size
    }
  }

  double _getIconInnerSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 20; // Smartwatch - smaller inner icon
    } else if (screenWidth > 600) {
      return 30; // Tablet - slightly larger
    } else {
      return 28; // Mobile - standard size
    }
  }

  double _getTitleFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 14; // Smartwatch - smaller text
    } else if (screenWidth > 600) {
      baseSize = 20; // Tablet - larger text
    } else {
      baseSize = 18; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9; // Reduce for short screens
    }

    return baseSize;
  }

  double _getBodyFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10; // Smartwatch - very small text
    } else if (screenWidth > 600) {
      baseSize = 16; // Tablet - larger text
    } else {
      baseSize = 14; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9; // Reduce for short screens
    }

    return baseSize;
  }

  double _getSmallFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 8; // Smartwatch - very small text
    } else if (screenWidth > 600) {
      baseSize = 14; // Tablet - larger text
    } else {
      baseSize = 12; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9; // Reduce for short screens
    }

    return baseSize;
  }

  double _getButtonFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10; // Smartwatch - small button text
    } else if (screenWidth > 600) {
      baseSize = 16; // Tablet - larger button text
    } else {
      baseSize = 14; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9; // Reduce for short screens
    }

    return baseSize;
  }

  double _getBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 12; // Smartwatch
    } else if (screenWidth > 600) {
      return 16; // Tablet
    } else {
      return 16; // Mobile
    }
  }

  double _getButtonBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 6; // Smartwatch
    } else if (screenWidth > 600) {
      return 8; // Tablet
    } else {
      return 8; // Mobile
    }
  }

  double _getButtonHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseHeight;
    if (screenWidth < 300) {
      baseHeight = 36; // Smartwatch
    } else if (screenWidth > 600) {
      baseHeight = 48; // Tablet
    } else {
      baseHeight = 44; // Mobile
    }

    if (isShortScreen) {
      baseHeight = baseHeight * 0.9; // Reduce for short screens
    }

    return baseHeight;
  }
}
