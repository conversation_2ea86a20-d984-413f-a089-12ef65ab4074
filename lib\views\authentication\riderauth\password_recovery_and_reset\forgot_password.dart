import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/authentication/riderauth/password_recovery_and_reset/input_recovery.dart';

class RiderForgotPassword extends StatefulWidget {
  const RiderForgotPassword({super.key});

  @override
  State<RiderForgotPassword> createState() => _RiderForgotPasswordState();
}

class _RiderForgotPasswordState extends State<RiderForgotPassword> {
  String? _selectedMethod;
  bool _isLoading = false;

  // Sample rider data - in real app, this would come from rider profile/session
  final String _riderEmail = '<EMAIL>';
  final String _riderPhone = '+234 8134 400 500';

  void _selectMethod(String method) {
    setState(() {
      _selectedMethod = method;
    });
  }

  void _navigateBack() {
    Navigator.of(context).pop();
  }

  Future<void> _handleContinue() async {
    if (_selectedMethod == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a recovery method'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isLoading = false;
    });

    if (mounted) {
      // Navigate to input recovery screen
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => RiderInputRecovery(
            recoveryMethod: _selectedMethod!,
            contactInfo: _selectedMethod == 'email' ? _riderEmail : _riderPhone,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height - MediaQuery.of(context).padding.top,
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: _getHorizontalPadding(context),
                vertical: _getVerticalPadding(context),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: _getSpacing(context, 20)),

                  // Back button and title
                  _buildHeader(context),

                  SizedBox(height: _getSpacing(context, 32)),

                  // Description
                  _buildDescription(context),

                  SizedBox(height: _getSpacing(context, 40)),

                  // Recovery methods
                  _buildRecoveryMethods(context),

                  SizedBox(height: _getSpacing(context, 40)),

                  // Continue button
                  _buildContinueButton(context),

                  SizedBox(height: _getSpacing(context, 20)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        GestureDetector(
          onTap: _navigateBack,
          child: Container(
            width: _getIconSize(context) + 8,
            height: _getIconSize(context) + 8,
            decoration: BoxDecoration(
              color: AppColors.black.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(_getBorderRadius(context)),
            ),
            child: Icon(
              Icons.arrow_back_ios_new,
              size: _getIconSize(context),
              color: AppColors.black,
            ),
          ),
        ),
        SizedBox(width: _getSpacing(context, 16)),
        Text(
          'Forgot password',
          style: TextStyle(
            color: AppColors.black,
            fontSize: _getHeadingFontSize(context),
            fontFamily: 'Bricolage Grotesque',
            fontWeight: FontWeight.w600,
            letterSpacing: -0.8,
          ),
        ),
      ],
    );
  }

  Widget _buildDescription(BuildContext context) {
    return Text(
      'Don\'t worry we got you covered. Please select password recovery method.',
      style: TextStyle(
        color: AppColors.black.withValues(alpha: 0.7),
        fontSize: _getBodyFontSize(context),
        fontFamily: 'Inter',
        fontWeight: FontWeight.w400,
        height: 1.5,
      ),
    );
  }

  Widget _buildRecoveryMethods(BuildContext context) {
    return Column(
      children: [
        // SMS Method
        _buildRecoveryOption(
          context: context,
          method: 'sms',
          icon: Icons.sms_outlined,
          title: 'Via SMS',
          subtitle: _riderPhone,
          isSelected: _selectedMethod == 'sms',
        ),

        SizedBox(height: _getSpacing(context, 16)),

        // Email Method
        _buildRecoveryOption(
          context: context,
          method: 'email',
          icon: Icons.email_outlined,
          title: 'Via Email',
          subtitle: _riderEmail,
          isSelected: _selectedMethod == 'email',
        ),
      ],
    );
  }

  Widget _buildRecoveryOption({
    required BuildContext context,
    required String method,
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: () => _selectMethod(method),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(_getSpacing(context, 20)),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : AppColors.white,
          border: Border.all(
            color: isSelected
                ? AppColors.primary
                : AppColors.black.withValues(alpha: 0.1),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(_getBorderRadius(context) + 4),
        ),
        child: Row(
          children: [
            Container(
              width: _getIconSize(context) + 8,
              height: _getIconSize(context) + 8,
              decoration: BoxDecoration(
                color: isSelected
                    ? AppColors.white.withValues(alpha: 0.2)
                    : AppColors.black.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(_getBorderRadius(context)),
              ),
              child: Icon(
                icon,
                size: _getIconSize(context),
                color: isSelected ? AppColors.white : AppColors.black.withValues(alpha: 0.7),
              ),
            ),
            SizedBox(width: _getSpacing(context, 16)),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: isSelected ? AppColors.white : AppColors.black,
                      fontSize: _getBodyFontSize(context),
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: _getSpacing(context, 4)),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: isSelected
                          ? AppColors.white.withValues(alpha: 0.8)
                          : AppColors.black.withValues(alpha: 0.6),
                      fontSize: _getSmallFontSize(context),
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContinueButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: _getButtonHeight(context),
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleContinue,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          ),
          elevation: 0,
        ),
        child: _isLoading
            ? SizedBox(
                width: _getSpacing(context, 20),
                height: _getSpacing(context, 20),
                child: const CircularProgressIndicator(
                  color: AppColors.white,
                  strokeWidth: 2,
                ),
              )
            : Text(
                'Continue',
                style: TextStyle(
                  fontSize: _getBodyFontSize(context),
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 40; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getVerticalPadding(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    if (screenHeight < 600) return 12; // Short screens
    if (screenHeight > 800) return 24; // Tall screens
    return 16; // Normal screens
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet
    } else {
      spacing = baseSpacing; // Mobile
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getHeadingFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 20; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 32; // Tablet
    } else {
      baseSize = 26; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.85;
    }

    return baseSize;
  }

  double _getBodyFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 12; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 16; // Tablet
    } else {
      baseSize = 14; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getSmallFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 14; // Tablet
    } else {
      baseSize = 12; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 16; // Smartwatch
    if (screenWidth > 600) return 24; // Tablet
    return 20; // Mobile
  }

  double _getBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 6; // Smartwatch
    if (screenWidth > 600) return 12; // Tablet
    return 8; // Mobile
  }

  double _getButtonHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseHeight;
    if (screenWidth < 300) {
      baseHeight = 40; // Smartwatch
    } else if (screenWidth > 600) {
      baseHeight = 56; // Tablet
    } else {
      baseHeight = 48; // Mobile
    }

    if (isShortScreen) {
      baseHeight = baseHeight * 0.9;
    }

    return baseHeight;
  }
}