import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:rideoon/services/order_api_service.dart';
import 'package:rideoon/services/address_service.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/models/order/collection.dart';
import 'package:rideoon/models/order/package.dart';
import 'package:rideoon/models/address/address_request.dart';
import 'package:rideoon/models/api_response.dart';

/// Service for managing package delivery data using API calls
/// This service replaces local storage with API-based data management
class PackageDataService {
  // Current draft order UUID for temporary data storage
  static String? _currentDraftOrderUuid;

  /// Save sender details by creating/updating pickup address
  static Future<String?> saveSenderData(Map<String, dynamic> data) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return null;

      // Create address request from sender data
      final phoneStr = data['phone']?.toString() ?? '';
      final phoneNumber = int.tryParse(phoneStr.replaceAll(RegExp(r'[^\d]'), '')) ?? 0;

      final addressRequest = AddressRequest(
        name: data['senderName'] ?? '',
        phoneNumber: phoneNumber,
        street: '${data['fullAddress'] ?? ''}${data['landmark'] != null ? ', ${data['landmark']}' : ''}',
        city: data['city'] ?? '',
        state: data['state'] ?? '',
        country: 'Nigeria',
        longitude: data['longitude'] is num ? (data['longitude'] as num).toDouble() : null,
        latitude: data['latitude'] is num ? (data['latitude'] as num).toDouble() : null,
        type: 'pickup',
      );

      final response = await AddressService.createAddress(addressRequest, authToken: authToken);
      if (response.success && response.data != null) {
        return response.data!.uuid;
      }
      return null;
    } catch (e) {
      print('Error saving sender data: $e');
      return null;
    }
  }

  /// Get sender details from user's pickup addresses
  static Future<Map<String, dynamic>?> getSenderData() async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return null;

      final addresses = await AddressService.getSavedAddresses(authToken: authToken);
      // Use addresses as set by the server (type 'address')
      final pickupAddresses = addresses.where((addr) => addr['type'] == 'address').toList();

      if (pickupAddresses.isNotEmpty) {
        // Use the first available address for pickup data
        final latest = pickupAddresses.first;
        print('📦 [PackageDataService] Found pickup data from address: ${latest['name']}');
        return {
          'senderName': latest['name'],
          'phone': latest['phoneNumber'],
          'fullAddress': latest['street'],
          'landmark': latest['landmark'],
          'city': latest['city'],
          'lga': latest['localGovernmentArea'],
          'state': latest['state'],
          'latitude': double.tryParse(latest['latitude'] ?? ''),
          'longitude': double.tryParse(latest['longitude'] ?? ''),
          'addressUuid': latest['uuid'],
        };
      }
      return null;
    } catch (e) {
      print('Error getting sender data: $e');
      return null;
    }
  }

  /// Save pickup details from user input to local storage
  static Future<String?> savePickupData(Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Store the exact user input data for pickup
      final pickupData = {
        'senderName': data['senderName'] ?? '',
        'phone': data['phone'] ?? '',
        'fullAddress': data['fullAddress'] ?? '',
        'landmark': data['landmark'] ?? '',
        'city': data['city'] ?? '',
        'lga': data['lga'] ?? '',
        'state': data['state'] ?? '',
        'latitude': data['latitude'],
        'longitude': data['longitude'],
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      await prefs.setString('pickup_data', jsonEncode(pickupData));
      print('📦 [PackageDataService] Saved pickup data to local storage');

      // Also save to API if user wants to save address
      return await saveSenderData(data);
    } catch (e) {
      print('Error saving pickup data: $e');
      return null;
    }
  }

  /// Get pickup details from user's saved choice
  static Future<Map<String, dynamic>?> getPickupData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pickupDataJson = prefs.getString('pickup_data');

      if (pickupDataJson != null) {
        final data = jsonDecode(pickupDataJson) as Map<String, dynamic>;
        print('📦 [PackageDataService] Found pickup data from user choice: ${data['senderName']}');
        return data;
      }

      // Fallback to sender data if no user choice saved
      return await getSenderData();
    } catch (e) {
      print('Error getting pickup data: $e');
      return await getSenderData();
    }
  }

  /// Save receiver details - stores user's exact choice for preview display
  static Future<String?> saveReceiverData(Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Store the user's exact input/selection for accurate preview display
      await prefs.setString('current_receiver_data', jsonEncode(data));
      print('📦 [PackageDataService] Saved receiver data for preview: ${data['name']}');

      // Also save to database
      final authToken = await AuthService.getAuthToken();
      if (authToken != null) {
        final phoneStr = data['phone']?.toString() ?? '';
        final phoneNumber = int.tryParse(phoneStr.replaceAll(RegExp(r'[^\d]'), '')) ?? 0;

        final addressRequest = AddressRequest(
          name: data['name'] ?? '',
          phoneNumber: phoneNumber,
          street: '${data['address'] ?? ''}${data['landmark'] != null ? ', ${data['landmark']}' : ''}',
          city: data['city'] ?? '',
          state: data['state'] ?? '',
          country: 'Nigeria',
          longitude: data['longitude'] is num ? (data['longitude'] as num).toDouble() : null,
          latitude: data['latitude'] is num ? (data['latitude'] as num).toDouble() : null,
          type: 'address', // Use server-managed type
        );

        final response = await AddressService.createAddress(addressRequest, authToken: authToken);
        if (response.success && response.data != null) {
          print('📦 [PackageDataService] Saved receiver data to database with UUID: ${response.data!.uuid}');
          return response.data!.uuid;
        }
      }

      return 'local_only'; // Return indicator that data was saved locally
    } catch (e) {
      print('Error saving receiver data: $e');
      return null;
    }
  }

  /// Get receiver details from user's saved choice
  static Future<Map<String, dynamic>?> getReceiverData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final receiverDataJson = prefs.getString('current_receiver_data');

      if (receiverDataJson != null) {
        final data = jsonDecode(receiverDataJson) as Map<String, dynamic>;
        print('📦 [PackageDataService] Found receiver data from user choice: ${data['name']}');
        return data;
      }

      // Fallback to most recent address if no user choice saved
      final authToken = await AuthService.getAuthToken();
      if (authToken != null) {
        final addresses = await AddressService.getSavedAddresses(authToken: authToken);
        if (addresses.isNotEmpty) {
          addresses.sort((a, b) {
            final aTime = a['lastUsed'] ?? 0;
            final bTime = b['lastUsed'] ?? 0;
            return bTime.compareTo(aTime);
          });

          final receiverAddress = addresses.first;
          print('📦 [PackageDataService] Found receiver data from fallback address: ${receiverAddress['name']}');

          return {
            'name': receiverAddress['name'],
            'phone': receiverAddress['phoneNumber'],
            'address': receiverAddress['street'],
            'city': receiverAddress['city'],
            'state': receiverAddress['state'],
            'latitude': double.tryParse(receiverAddress['latitude']?.toString() ?? '0'),
            'longitude': double.tryParse(receiverAddress['longitude']?.toString() ?? '0'),
            'addressUuid': receiverAddress['id'] ?? receiverAddress['uuid'],
          };
        }
      }

      return null;
    } catch (e) {
      print('Error getting receiver data: $e');
      return null;
    }
  }

  /// Save package details to current draft order
  static Future<bool> savePackageData(Map<String, dynamic> data) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return false;

      // If no current draft order, create one
      if (_currentDraftOrderUuid == null) {
        final createRequest = CreateCollectionRequest(
          orderPickupType: data['deliveryType'] ?? 'instant',
          orderPickupDate: data['pickupDate'] != null ? DateTime.parse(data['pickupDate']) : null,
          note: 'Draft order for package data',
        );

        final response = await OrderApiService.createOrder(createRequest, authToken: authToken);
        if (response.success && response.data != null) {
          _currentDraftOrderUuid = response.data!.uuid;
        } else {
          return false;
        }
      }

      // Package data is now managed through the order system
      return true;
    } catch (e) {
      print('Error saving package data: $e');
      return false;
    }
  }

  /// Get package details from current draft order
  static Future<Map<String, dynamic>?> getPackageData() async {
    try {
      if (_currentDraftOrderUuid == null) return null;

      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return null;

      final response = await OrderApiService.getOrder(_currentDraftOrderUuid!, authToken: authToken);
      if (response.success && response.data != null) {
        final order = response.data!;
        return {
          'deliveryType': order.orderPickupType,
          'pickupDate': order.orderPickupDate?.toIso8601String(),
          'note': order.note,
          'orderUuid': order.uuid,
        };
      }
      return null;
    } catch (e) {
      print('Error getting package data: $e');
      return null;
    }
  }

  /// Clear all package data by clearing current draft order
  static Future<void> clearAllData() async {
    _currentDraftOrderUuid = null;
    // Note: We don't delete the draft order from API as it might be needed later
  }

  /// Save completed order/shipment - now handled by API
  static Future<bool> saveCompletedOrder(Map<String, dynamic> orderData) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return false;

      // Convert local order data to API format
      final createRequest = CreateCollectionRequest(
        orderPickupType: orderData['deliveryType'] ?? 'instant',
        orderPickupDate: orderData['orderDate'] != null ? DateTime.parse(orderData['orderDate']) : null,
        note: orderData['note'] ?? 'Completed order',
        pickupAddressUuid: orderData['pickupAddressUuid'],
        deliveryAddressUuid: orderData['deliveryAddressUuid'],
      );

      final response = await OrderApiService.createOrder(createRequest, authToken: authToken);
      return response.success;
    } catch (e) {
      print('Error saving completed order: $e');
      return false;
    }
  }

  /// Get completed orders/shipments from API
  static Future<List<Map<String, dynamic>>> getCompletedOrders() async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return [];

      final response = await OrderApiService.getOrders(
        queryParams: {'status': 'completed'},
        authToken: authToken,
      );

      if (response.success && response.data != null) {
        return response.data!.map((order) => {
          'id': order.uuid,
          'trackingNumber': order.uuid.substring(0, 8).toUpperCase(),
          'status': order.status,
          'orderDate': order.created.toIso8601String(),
          'deliveryType': order.orderPickupType,
          'note': order.note,
          'amount': order.amount,
          'timestamp': order.created.millisecondsSinceEpoch,
        }).toList();
      }
      return [];
    } catch (e) {
      print('Error getting completed orders: $e');
      return [];
    }
  }

  /// Save current shipment - now handled by API
  static Future<bool> saveCurrentShipment(Map<String, dynamic> shipmentData) async {
    // Current shipments are now managed through the orders API
    return await saveCompletedOrder(shipmentData);
  }

  /// Get current shipments from API
  static Future<List<Map<String, dynamic>>> getCurrentShipments() async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return [];

      final response = await OrderApiService.getOrders(
        queryParams: {'status': 'pending,confirmed,in_transit'},
        authToken: authToken,
      );

      if (response.success && response.data != null) {
        return response.data!.map((order) => {
          'id': order.uuid,
          'trackingNumber': order.uuid.substring(0, 8).toUpperCase(),
          'status': order.status,
          'orderDate': order.created.toIso8601String(),
          'deliveryType': order.orderPickupType,
          'note': order.note,
          'amount': order.amount,
          'timestamp': order.created.millisecondsSinceEpoch,
        }).toList();
      }
      return [];
    } catch (e) {
      print('Error getting current shipments: $e');
      return [];
    }
  }

  /// Update shipment status via API
  static Future<bool> updateShipmentStatus(String shipmentId, String newStatus) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return false;

      // Update order status through API
      final updateRequest = UpdateCollectionRequest(
        // Note: The API might not have a direct status field,
        // this would need to be implemented based on actual API schema
        note: 'Status updated to: $newStatus',
      );

      final response = await OrderApiService.updateOrder(shipmentId, updateRequest, authToken: authToken);
      return response.success;
    } catch (e) {
      print('Error updating shipment status: $e');
      return false;
    }
  }

  /// Clear completed orders - API managed, no local clearing needed
  static Future<void> clearCompletedOrders() async {
    // Orders are managed by API, no local clearing needed
    print('Clear completed orders: API managed, no action needed');
  }

  /// Clear current shipments - API managed, no local clearing needed
  static Future<void> clearCurrentShipments() async {
    // Shipments are managed by API, no local clearing needed
    print('Clear current shipments: API managed, no action needed');
  }

  /// Check if pickup details exist
  static Future<bool> hasPickupData() async {
    final data = await getPickupData();
    return data != null && data.isNotEmpty;
  }

  /// Check if receiver details exist
  static Future<bool> hasReceiverData() async {
    final data = await getReceiverData();
    return data != null && data.isNotEmpty;
  }

  /// Check if sender details exist
  static Future<bool> hasSenderData() async {
    final data = await getSenderData();
    return data != null && data.isNotEmpty;
  }

  /// Check if package details exist
  static Future<bool> hasPackageData() async {
    final data = await getPackageData();
    return data != null && data.isNotEmpty;
  }

  /// Save pickup location to history - now uses address API
  static Future<void> savePickupLocationToHistory(Map<String, dynamic> locationData) async {
    // Location history is now managed through saved addresses
    await saveSenderData(locationData);
  }

  /// Get pickup location history from saved addresses
  static Future<List<Map<String, dynamic>>> getPickupLocationHistory() async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return [];

      final addresses = await AddressService.getSavedAddresses(authToken: authToken);
      final pickupAddresses = addresses.where((addr) => addr['type'] == 'pickup').toList();

      return pickupAddresses.map((addr) => {
        'address': addr['street'],
        'name': addr['name'],
        'phone': addr['phoneNumber'],
        'city': addr['city'],
        'state': addr['state'],
        'latitude': double.tryParse(addr['latitude'] ?? ''),
        'longitude': double.tryParse(addr['longitude'] ?? ''),
        'timestamp': DateTime.parse(addr['created'] ?? DateTime.now().toIso8601String()).millisecondsSinceEpoch,
        'addressUuid': addr['uuid'],
      }).toList();
    } catch (e) {
      print('Error getting pickup location history: $e');
      return [];
    }
  }

  /// Save receiver location to history - now uses address API
  static Future<void> saveReceiverLocationToHistory(Map<String, dynamic> locationData) async {
    // Location history is now managed through saved addresses
    await saveReceiverData(locationData);
  }

  /// Get receiver location history from saved addresses
  static Future<List<Map<String, dynamic>>> getReceiverLocationHistory() async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return [];

      final addresses = await AddressService.getSavedAddresses(authToken: authToken);
      final deliveryAddresses = addresses.where((addr) => addr['type'] == 'delivery').toList();

      return deliveryAddresses.map((addr) => {
        'address': addr['street'],
        'name': addr['name'],
        'phone': addr['phoneNumber'],
        'city': addr['city'],
        'state': addr['state'],
        'latitude': double.tryParse(addr['latitude'] ?? ''),
        'longitude': double.tryParse(addr['longitude'] ?? ''),
        'timestamp': DateTime.parse(addr['created'] ?? DateTime.now().toIso8601String()).millisecondsSinceEpoch,
        'addressUuid': addr['uuid'],
      }).toList();
    } catch (e) {
      print('Error getting receiver location history: $e');
      return [];
    }
  }
}
