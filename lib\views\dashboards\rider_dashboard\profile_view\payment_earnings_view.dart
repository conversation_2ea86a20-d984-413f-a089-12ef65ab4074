import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/providers/toast_provider.dart';

/// Payment & Earnings view for managing payment methods and viewing earnings
class PaymentEarningsView extends StatefulWidget {
  const PaymentEarningsView({super.key});

  @override
  State<PaymentEarningsView> createState() => _PaymentEarningsViewState();
}

class _PaymentEarningsViewState extends State<PaymentEarningsView> {
  // Sample earnings data
  final Map<String, dynamic> _earningsData = {
    'totalEarnings': '45,250',
    'thisWeek': '3,200',
    'thisMonth': '12,800',
    'pendingPayouts': '1,500',
    'completedDeliveries': 156,
    'averageRating': 4.8,
  };

  // Sample payment methods
  final List<Map<String, dynamic>> _paymentMethods = [
    {
      'id': '1',
      'type': 'bank',
      'name': 'First Bank Nigeria',
      'details': '****1234',
      'isDefault': true,
    },
    {
      'id': '2',
      'type': 'mobile',
      'name': 'MTN Mobile Money',
      'details': '****5678',
      'isDefault': false,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5FF),
      appBar: _buildAppBar(context),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(_getHorizontalPadding(context)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: _getSpacing(context, 20)),

              // Earnings overview
              _buildEarningsOverview(context),

              SizedBox(height: _getSpacing(context, 32)),

              // Payment methods section
              _buildPaymentMethodsSection(context),

              SizedBox(height: _getSpacing(context, 32)),

              // Earnings history
              _buildEarningsHistory(context),

              SizedBox(height: _getSpacing(context, 20)),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: const Color(0xFFF5F5FF),
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: AppColors.black,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        'Payment & Earnings',
        style: TextStyle(
          fontSize: _getFontSize(context, 18),
          fontWeight: FontWeight.w600,
          fontFamily: 'Poppins',
          color: AppColors.black,
        ),
      ),
      centerTitle: true,
    );
  }

  Widget _buildEarningsOverview(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 24)),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 16)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Total Earnings',
            style: TextStyle(
              fontSize: _getFontSize(context, 14),
              fontFamily: 'Poppins',
              color: AppColors.white.withValues(alpha: 0.8),
            ),
          ),
          SizedBox(height: _getSpacing(context, 8)),
          Text(
            '₦${_earningsData['totalEarnings']}',
            style: TextStyle(
              fontSize: _getFontSize(context, 32),
              fontWeight: FontWeight.bold,
              fontFamily: 'Poppins',
              color: AppColors.white,
            ),
          ),
          SizedBox(height: _getSpacing(context, 20)),
          Row(
            children: [
              Expanded(
                child: _buildEarningsItem(
                  'This Week',
                  '₦${_earningsData['thisWeek']}',
                ),
              ),
              Expanded(
                child: _buildEarningsItem(
                  'This Month',
                  '₦${_earningsData['thisMonth']}',
                ),
              ),
            ],
          ),
          SizedBox(height: _getSpacing(context, 16)),
          Row(
            children: [
              Expanded(
                child: _buildEarningsItem(
                  'Pending',
                  '₦${_earningsData['pendingPayouts']}',
                ),
              ),
              Expanded(
                child: _buildEarningsItem(
                  'Deliveries',
                  '${_earningsData['completedDeliveries']}',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEarningsItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: _getFontSize(context, 12),
            fontFamily: 'Poppins',
            color: AppColors.white.withValues(alpha: 0.8),
          ),
        ),
        SizedBox(height: _getSpacing(context, 4)),
        Text(
          value,
          style: TextStyle(
            fontSize: _getFontSize(context, 18),
            fontWeight: FontWeight.w600,
            fontFamily: 'Poppins',
            color: AppColors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentMethodsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Payment Methods',
              style: TextStyle(
                fontSize: _getFontSize(context, 18),
                fontWeight: FontWeight.w600,
                fontFamily: 'Poppins',
                color: AppColors.black,
              ),
            ),
            TextButton.icon(
              onPressed: () {
                Toast.info(
                    'Add payment method functionality to be implemented');
              },
              icon: Icon(
                Icons.add,
                size: _getIconSize(context, 18),
                color: AppColors.primary,
              ),
              label: Text(
                'Add',
                style: TextStyle(
                  fontSize: _getFontSize(context, 14),
                  fontWeight: FontWeight.w500,
                  fontFamily: 'Poppins',
                  color: AppColors.primary,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: _getSpacing(context, 16)),
        ..._paymentMethods.map((method) =>
            Padding(
              padding: EdgeInsets.only(bottom: _getSpacing(context, 12)),
              child: _buildPaymentMethodCard(context, method),
            )),
      ],
    );
  }

  Widget _buildPaymentMethodCard(BuildContext context,
      Map<String, dynamic> method) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 16)),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
        border: Border.all(
          color: method['isDefault']
              ? AppColors.primary
              : AppColors.black.withValues(alpha: 0.1),
          width: method['isDefault'] ? 2 : 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: _getIconSize(context, 48),
            height: _getIconSize(context, 48),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
            ),
            child: Icon(
              method['type'] == 'bank' ? Icons.account_balance : Icons
                  .phone_android,
              size: _getIconSize(context, 24),
              color: AppColors.primary,
            ),
          ),
          SizedBox(width: _getSpacing(context, 16)),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      method['name'],
                      style: TextStyle(
                        fontSize: _getFontSize(context, 16),
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Poppins',
                        color: AppColors.black,
                      ),
                    ),
                    if (method['isDefault']) ...[
                      SizedBox(width: _getSpacing(context, 8)),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: _getSpacing(context, 8),
                          vertical: _getSpacing(context, 2),
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.success,
                          borderRadius: BorderRadius.circular(_getBorderRadius(
                              context, 4)),
                        ),
                        child: Text(
                          'Default',
                          style: TextStyle(
                            fontSize: _getFontSize(context, 10),
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Poppins',
                            color: AppColors.white,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                SizedBox(height: _getSpacing(context, 4)),
                Text(
                  method['details'],
                  style: TextStyle(
                    fontSize: _getFontSize(context, 14),
                    fontFamily: 'Poppins',
                    color: AppColors.black.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              Toast.info('Edit payment method functionality to be implemented');
            },
            icon: Icon(
              Icons.edit_outlined,
              size: _getIconSize(context, 20),
              color: AppColors.black.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEarningsHistory(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Earnings',
          style: TextStyle(
            fontSize: _getFontSize(context, 18),
            fontWeight: FontWeight.w600,
            fontFamily: 'Poppins',
            color: AppColors.black,
          ),
        ),
        SizedBox(height: _getSpacing(context, 16)),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(_getSpacing(context, 20)),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
            border: Border.all(
              color: AppColors.black.withValues(alpha: 0.1),
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.analytics_outlined,
                size: _getIconSize(context, 48),
                color: AppColors.black.withValues(alpha: 0.3),
              ),
              SizedBox(height: _getSpacing(context, 16)),
              Text(
                'Detailed earnings history coming soon',
                style: TextStyle(
                  fontSize: _getFontSize(context, 16),
                  fontWeight: FontWeight.w500,
                  fontFamily: 'Poppins',
                  color: AppColors.black.withValues(alpha: 0.6),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: _getSpacing(context, 8)),
              Text(
                'Track your daily, weekly, and monthly earnings with detailed analytics.',
                style: TextStyle(
                  fontSize: _getFontSize(context, 14),
                  fontFamily: 'Poppins',
                  color: AppColors.black.withValues(alpha: 0.5),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery
        .of(context)
        .size
        .width;
    final screenHeight = MediaQuery
        .of(context)
        .size
        .height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16;
    } else if (screenWidth > 600) {
      basePadding = 40;
    } else {
      basePadding = 24;
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery
        .of(context)
        .size
        .width;
    final screenHeight = MediaQuery
        .of(context)
        .size
        .height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery
        .of(context)
        .size
        .width;
    final screenHeight = MediaQuery
        .of(context)
        .size
        .height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8;
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2;
    } else {
      fontSize = baseFontSize;
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery
        .of(context)
        .size
        .width;
    final screenHeight = MediaQuery
        .of(context)
        .size
        .height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8;
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2;
    } else {
      iconSize = baseIconSize;
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery
        .of(context)
        .size
        .width;
    final screenHeight = MediaQuery
        .of(context)
        .size
        .height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6;
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2;
    } else {
      borderRadius = baseBorderRadius;
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }
}
