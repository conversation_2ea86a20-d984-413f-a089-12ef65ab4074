import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:rideoon/services/map_service.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/dashboards/rider_dashboard/full_screen_map_view.dart';
import 'package:rideoon/providers/toast_provider.dart';

/// Dashboard view page for rider dashboard
///
/// This page contains the main dashboard content for riders,
/// including rider profile, live mapping, income analytics, and delivery management.
class DashboardView extends StatefulWidget {
  const DashboardView({super.key});

  @override
  State<DashboardView> createState() => _DashboardViewState();
}

class _DashboardViewState extends State<DashboardView> {
  // Sample data - replace with actual data from your state management
  final String _riderName = 'John Rider';
  final String _location = 'Lagos, Victoria Island';
  final bool _isOnline = true;
  final String _totalIncome = '150,000';
  final int _activeOrders = 4;
  final int _pendingOrders = 1;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Safe<PERSON>rea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 7% gap from top of screen
              SizedBox(height: MediaQuery.of(context).size.height * 0.07),

              // Header with rider profile and online status
              _buildHeader(context),

              SizedBox(height: _getSpacing(context, 32)),

              // Live mapping view button
              _buildLiveMappingButton(context),

              SizedBox(height: _getSpacing(context, 32)),

              // Income analytics card
              _buildIncomeCard(context),

              SizedBox(height: _getSpacing(context, 20)),

              // Orders overview cards
              _buildOrdersOverview(context),

              SizedBox(height: _getSpacing(context, 32)),

              // Recent delivery tracking
              _buildRecentDeliveryTracking(context),

              SizedBox(height: _getSpacing(context, 32)),

              // Action buttons
              _buildActionButtons(context),

              SizedBox(height: _getSpacing(context, 100)), // Extra space for bottom nav
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Rider profile section
          Row(
            children: [
              Container(
                width: _getIconSize(context, 55),
                height: _getIconSize(context, 55),
                decoration: const ShapeDecoration(
                  color: AppColors.white,
                  shape: OvalBorder(),
                ),
                child: Icon(
                  Icons.person,
                  size: _getIconSize(context, 30),
                  color: AppColors.primary,
                ),
              ),
              SizedBox(width: _getSpacing(context, 17)),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome back',
                    style: TextStyle(
                      color: const Color(0xFF393939),
                      fontSize: _getFontSize(context, 15),
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  SizedBox(height: _getSpacing(context, 2)),
                  Text(
                    _riderName,
                    style: TextStyle(
                      color: AppColors.black,
                      fontSize: _getFontSize(context, 16),
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],
          ),

          // Online status indicator
          _buildOnlineStatus(context),
        ],
      ),
    );
  }
  Widget _buildOnlineStatus(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: _getSpacing(context, 12),
        vertical: _getSpacing(context, 8),
      ),
      decoration: BoxDecoration(
        color: _isOnline
            ? AppColors.success.withValues(alpha: 0.1)
            : AppColors.black.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 20)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: _getSpacing(context, 8),
            height: _getSpacing(context, 8),
            decoration: BoxDecoration(
              color: _isOnline ? AppColors.success : AppColors.black.withValues(alpha: 0.5),
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: _getSpacing(context, 8)),
          Text(
            _isOnline ? 'Online' : 'Offline',
            style: TextStyle(
              fontSize: _getFontSize(context, 12),
              fontWeight: FontWeight.w500,
              fontFamily: 'Poppins',
              color: _isOnline ? AppColors.success : AppColors.black.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLiveMappingButton(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: GestureDetector(
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const FullScreenMapView(),
            ),
          );
        },
        child: Container(
          width: double.infinity,
          height: _getSpacing(context, 140),
          decoration: ShapeDecoration(
            color: AppColors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
            ),
            shadows: [
              BoxShadow(
                color: AppColors.black.withValues(alpha: 0.1),
                blurRadius: _getSpacing(context, 8),
                offset: Offset(0, _getSpacing(context, 2)),
                spreadRadius: 0,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
            child: Stack(
              children: [
                // Mini map preview
                Container(
                  width: double.infinity,
                  height: double.infinity,
                  child: FutureBuilder<CameraPosition>(
                    future: MapService.getCurrentLocationCameraPosition(zoom: 14.0),
                    builder: (context, snapshot) {
                      if (snapshot.hasData) {
                        return GoogleMap(
                          initialCameraPosition: snapshot.data!,
                          myLocationEnabled: false,
                          myLocationButtonEnabled: false,
                          zoomControlsEnabled: false,
                          mapToolbarEnabled: false,
                          compassEnabled: false,
                          trafficEnabled: false,
                          buildingsEnabled: true,
                          mapType: MapType.normal,
                          scrollGesturesEnabled: false,
                          zoomGesturesEnabled: false,
                          tiltGesturesEnabled: false,
                          rotateGesturesEnabled: false,
                          onMapCreated: (GoogleMapController controller) {
                            // Don't set the main controller for preview
                          },
                        );
                      } else {
                        return Container(
                          decoration: const BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Color(0xFFF5F5FF),
                                Color(0xFFE8E8FF),
                              ],
                            ),
                          ),
                          child: Center(
                            child: Icon(
                              Icons.map,
                              size: _getIconSize(context, 48),
                              color: AppColors.primary.withValues(alpha: 0.5),
                            ),
                          ),
                        );
                      }
                    },
                  ),
                ),
                // Overlay with button
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        AppColors.black.withValues(alpha: 0.3),
                        Colors.transparent,
                        AppColors.black.withValues(alpha: 0.5),
                      ],
                    ),
                  ),
                ),
                // Content
                Center(
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: _getSpacing(context, 20),
                      vertical: _getSpacing(context, 10),
                    ),
                    decoration: ShapeDecoration(
                      color: AppColors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.map_outlined,
                          size: _getIconSize(context, 20),
                          color: AppColors.primary,
                        ),
                        SizedBox(width: _getSpacing(context, 8)),
                        Text(
                          'View live mapping',
                          style: TextStyle(
                            color: AppColors.black,
                            fontSize: _getFontSize(context, 17),
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w600,
                            height: 1.29,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  Widget _buildIncomeCard(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(_getSpacing(context, 20)),
        decoration: ShapeDecoration(
          color: AppColors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
          ),
          shadows: [
            BoxShadow(
              color: AppColors.black.withValues(alpha: 0.1),
              blurRadius: _getSpacing(context, 8),
              offset: Offset(0, _getSpacing(context, 2)),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Row(
          children: [
            // Income section
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        width: _getIconSize(context, 21),
                        height: _getIconSize(context, 21),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.account_balance_wallet_outlined,
                          size: _getIconSize(context, 12),
                          color: AppColors.primary,
                        ),
                      ),
                      SizedBox(width: _getSpacing(context, 8)),
                      Text(
                        'Total Income',
                        style: TextStyle(
                          color: const Color(0xFF5F5F5F),
                          fontSize: _getFontSize(context, 12),
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: _getSpacing(context, 12)),
                  Row(
                    children: [
                      Text(
                        '₦$_totalIncome',
                        style: TextStyle(
                          color: AppColors.black,
                          fontSize: _getFontSize(context, 28),
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Payment history section
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'Payment History',
                    style: TextStyle(
                      color: AppColors.primary,
                      fontSize: _getFontSize(context, 10),
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  SizedBox(height: _getSpacing(context, 12)),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: _getSpacing(context, 12),
                      vertical: _getSpacing(context, 6),
                    ),
                    decoration: ShapeDecoration(
                      color: AppColors.success,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
                      ),
                    ),
                    child: Text(
                      'Withdraw',
                      style: TextStyle(
                        color: AppColors.white,
                        fontSize: _getFontSize(context, 10),
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  Widget _buildOrdersOverview(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Row(
        children: [
          // Active orders card
          Expanded(
            child: _buildOrderCard(
              context,
              title: 'Active Orders',
              count: _activeOrders.toString().padLeft(2, '0'),
              icon: Icons.delivery_dining,
              color: AppColors.warning,
              onViewAllTap: () {
                // TODO: Navigate to active orders
              },
            ),
          ),
          SizedBox(width: _getSpacing(context, 16)),
          // Pending orders card
          Expanded(
            child: _buildOrderCard(
              context,
              title: 'Pending Orders',
              count: _pendingOrders.toString().padLeft(2, '0'),
              icon: Icons.pending_actions,
              color: AppColors.pending,
              onViewAllTap: () {
                // TODO: Navigate to pending orders
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderCard(
    BuildContext context, {
    required String title,
    required String count,
    required IconData icon,
    required Color color,
    required VoidCallback onViewAllTap,
  }) {
    return Container(
      padding: EdgeInsets.all(_getSpacing(context, 16)),
      decoration: ShapeDecoration(
        color: AppColors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
        ),
        shadows: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: _getSpacing(context, 8),
            offset: Offset(0, _getSpacing(context, 2)),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: _getIconSize(context, 21),
                height: _getIconSize(context, 21),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: _getIconSize(context, 12),
                  color: color,
                ),
              ),
              SizedBox(width: _getSpacing(context, 8)),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: const Color(0xFF5F5F5F),
                    fontSize: _getFontSize(context, 10),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: _getSpacing(context, 16)),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                count,
                style: TextStyle(
                  color: AppColors.black,
                  fontSize: _getFontSize(context, 20),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w600,
                ),
              ),
              GestureDetector(
                onTap: onViewAllTap,
                child: Text(
                  'View All',
                  style: TextStyle(
                    color: AppColors.primary,
                    fontSize: _getFontSize(context, 8),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  Widget _buildRecentDeliveryTracking(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Delivery',
            style: TextStyle(
              color: AppColors.black,
              fontSize: _getFontSize(context, 18),
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w600,
              letterSpacing: -0.5,
            ),
          ),
          SizedBox(height: _getSpacing(context, 16)),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(_getSpacing(context, 20)),
            decoration: ShapeDecoration(
              color: const Color(0xFFFEFEFE),
              shape: RoundedRectangleBorder(
                side: BorderSide(
                  width: 1,
                  color: AppColors.black.withValues(alpha: 0.1),
                ),
                borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // First tracking step
                _buildTrackingStep(
                  context,
                  title: 'Rider picked Up package',
                  time: '18th of december, 2024 10:24:02PM',
                  address: '14 LAGOS ISLAND. 18, SIMPSON STREET, BESIDE TOTAL FILLING STATION SURA, LAGOS',
                  isCompleted: true,
                  isLast: false,
                ),
                SizedBox(height: _getSpacing(context, 20)),
                // Second tracking step
                _buildTrackingStep(
                  context,
                  title: 'Package Delivered to Destination',
                  time: null,
                  address: null,
                  isCompleted: false,
                  isLast: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrackingStep(
    BuildContext context, {
    required String title,
    String? time,
    String? address,
    required bool isCompleted,
    required bool isLast,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: _getIconSize(context, 19),
              height: _getIconSize(context, 19),
              decoration: BoxDecoration(
                color: AppColors.black.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Container(
                  width: _getIconSize(context, 9),
                  height: _getIconSize(context, 9),
                  decoration: BoxDecoration(
                    color: isCompleted ? AppColors.primary : AppColors.black.withValues(alpha: 0.3),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: _getSpacing(context, 40),
                color: AppColors.black.withValues(alpha: 0.1),
              ),
          ],
        ),
        SizedBox(width: _getSpacing(context, 16)),
        // Content
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: isCompleted
                      ? AppColors.black.withValues(alpha: 0.8)
                      : AppColors.black.withValues(alpha: 0.5),
                  fontSize: _getFontSize(context, 12),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (time != null) ...[
                SizedBox(height: _getSpacing(context, 4)),
                Text(
                  time,
                  style: TextStyle(
                    color: AppColors.black.withValues(alpha: 0.6),
                    fontSize: _getFontSize(context, 10),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
              if (address != null) ...[
                SizedBox(height: _getSpacing(context, 8)),
                Text(
                  address,
                  style: TextStyle(
                    color: AppColors.black.withValues(alpha: 0.7),
                    fontSize: _getFontSize(context, 10),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w400,
                    height: 1.4,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
  Widget _buildActionButtons(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Column(
        children: [
          // Update delivery button
          Container(
            width: double.infinity,
            height: _getSpacing(context, 50),
            child: ElevatedButton(
              onPressed: () {
                // TODO: Implement update delivery functionality
                Toast.info('Update delivery functionality to be implemented');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
                ),
                elevation: 2,
              ),
              child: Text(
                'Update Delivery',
                style: TextStyle(
                  fontSize: _getFontSize(context, 14),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          SizedBox(height: _getSpacing(context, 16)),
          // View delivery details button
          GestureDetector(
            onTap: () {
              // TODO: Navigate to delivery details
              Toast.info('View delivery details functionality to be implemented');
            },
            child: Text(
              'View Delivery Details',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: AppColors.primary,
                fontSize: _getFontSize(context, 14),
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w500,
                decoration: TextDecoration.underline,
                decorationColor: AppColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 40; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet
    } else {
      spacing = baseSpacing; // Mobile
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2; // Tablet
    } else {
      fontSize = baseFontSize; // Mobile
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2; // Tablet
    } else {
      iconSize = baseIconSize; // Mobile
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2; // Tablet
    } else {
      borderRadius = baseBorderRadius; // Mobile
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }
}