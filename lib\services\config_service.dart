import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Service class to manage environment-based configuration
/// This class provides a centralized way to access all environment variables
class ConfigService {
  // Private constructor to prevent instantiation
  ConfigService._();

  /// Initialize the configuration service
  /// This should be called before runApp() in main.dart
  static Future<void> initialize() async {
    await dotenv.load(fileName: ".env");
  }

  // App Configuration
  static String get appName => dotenv.env['APP_NAME'] ?? 'RideOn';
  static String get appVersion => dotenv.env['APP_VERSION'] ?? '1.0.0';
  static String get appEnvironment => dotenv.env['APP_ENVIRONMENT'] ?? 'development';

  // API Configuration
  static String get apiBaseUrl => dotenv.env['API_BASE_URL'] ?? 'https://api.rideon.com';
  static int get apiTimeout => int.tryParse(dotenv.env['API_TIMEOUT'] ?? '30000') ?? 30000;
  static String get apiKey => dotenv.env['API_KEY'] ?? '';

  // Database Configuration
  static String get databaseUrl => dotenv.env['DATABASE_URL'] ?? '';

  // Authentication
  static String get jwtSecret => dotenv.env['JWT_SECRET'] ?? '';
  static String get oauthClientId => dotenv.env['OAUTH_CLIENT_ID'] ?? '';
  static String get oauthClientSecret => dotenv.env['OAUTH_CLIENT_SECRET'] ?? '';

  // Third-party Services
  static String get googleMapsApiKey => dotenv.env['GOOGLE_MAPS_API_KEY'] ?? '';
  static String get firebaseProjectId => dotenv.env['FIREBASE_PROJECT_ID'] ?? '';
  static String get stripePublishableKey => dotenv.env['STRIPE_PUBLISHABLE_KEY'] ?? '';
  static String get stripeSecretKey => dotenv.env['STRIPE_SECRET_KEY'] ?? '';

  // Push Notifications
  static String get fcmServerKey => dotenv.env['FCM_SERVER_KEY'] ?? '';

  // Feature Flags
  static bool get enableDebugMode => _getBoolValue('ENABLE_DEBUG_MODE', true);
  static bool get enableAnalytics => _getBoolValue('ENABLE_ANALYTICS', false);
  static bool get enableCrashReporting => _getBoolValue('ENABLE_CRASH_REPORTING', true);

  // Logging
  static String get logLevel => dotenv.env['LOG_LEVEL'] ?? 'info';
  static bool get enableRemoteLogging => _getBoolValue('ENABLE_REMOTE_LOGGING', false);

  // App Store Configuration
  static String get androidAppId => dotenv.env['ANDROID_APP_ID'] ?? 'com.example.rideoon';
  static String get iosAppId => dotenv.env['IOS_APP_ID'] ?? 'com.example.rideoon';

  // Environment Checks
  static bool get isDevelopment => appEnvironment.toLowerCase() == 'development';
  static bool get isStaging => appEnvironment.toLowerCase() == 'staging';
  static bool get isProduction => appEnvironment.toLowerCase() == 'production';

  /// Helper method to parse boolean values from environment variables
  static bool _getBoolValue(String key, bool defaultValue) {
    final value = dotenv.env[key]?.toLowerCase();
    if (value == null) return defaultValue;
    return value == 'true' || value == '1' || value == 'yes';
  }

  /// Get a custom environment variable
  /// Useful for accessing variables not predefined in this service
  static String? getCustomValue(String key) {
    return dotenv.env[key];
  }

  /// Check if a specific environment variable exists
  static bool hasValue(String key) {
    return dotenv.env.containsKey(key) && dotenv.env[key]!.isNotEmpty;
  }

  /// Get all environment variables (for debugging purposes)
  /// Only use this in development mode
  static Map<String, String> getAllValues() {
    if (!isDevelopment) {
      throw Exception('getAllValues() can only be called in development mode');
    }
    return Map<String, String>.from(dotenv.env);
  }

  /// Validate that all required environment variables are set
  static void validateRequiredVariables() {
    final requiredVars = <String>[];

    // Add required variables based on your app's needs
    if (isProduction) {
      if (!hasValue('API_KEY')) requiredVars.add('API_KEY');
      if (!hasValue('JWT_SECRET')) requiredVars.add('JWT_SECRET');
    }

    if (requiredVars.isNotEmpty) {
      throw Exception(
        'Missing required environment variables: ${requiredVars.join(', ')}'
      );
    }
  }
}
