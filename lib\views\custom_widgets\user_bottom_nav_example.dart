import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/user_bottom_nav.dart';

/// Example usage of the UserBottomNavigation widget
/// 
/// This file demonstrates different ways to use the reusable
/// bottom navigation bar throughout the app.
class UserBottomNavExample extends StatefulWidget {
  const UserBottomNavExample({super.key});

  @override
  State<UserBottomNavExample> createState() => _UserBottomNavExampleState();
}

class _UserBottomNavExampleState extends State<UserBottomNavExample> {
  int _currentIndex = 0;

  // Define navigation items matching the original design
  final List<BottomNavItem> _navItems = [
    BottomNavItem(
      icon: Icons.home_outlined,
      activeIcon: Icons.home,
      label: 'Home',
      activeColor: AppColors.primary,
      inactiveColor: AppColors.black.withValues(alpha: 0.5),
      tooltip: 'Home Screen',
    ),
    BottomNavItem(
      icon: Icons.location_on_outlined,
      activeIcon: Icons.location_on,
      label: 'Track',
      activeColor: AppColors.primary,
      inactiveColor: AppColors.black.withValues(alpha: 0.5),
      tooltip: 'Track Orders',
    ),
    BottomNavItem(
      icon: Icons.local_shipping_outlined,
      activeIcon: Icons.local_shipping,
      label: 'Shipment',
      activeColor: AppColors.primary,
      inactiveColor: AppColors.black.withValues(alpha: 0.5),
      tooltip: 'Shipment Details',
    ),
    BottomNavItem(
      icon: Icons.person_outline,
      activeIcon: Icons.person,
      label: 'Account',
      activeColor: AppColors.primary,
      inactiveColor: AppColors.black.withValues(alpha: 0.5),
      tooltip: 'User Account',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bottom Navigation Examples'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
      ),
      body: _buildCurrentScreen(),
      bottomNavigationBar: UserBottomNavigation(
        items: _navItems,
        currentIndex: _currentIndex,
        onTap: _onNavItemTapped,
      ),
    );
  }

  Widget _buildCurrentScreen() {
    return IndexedStack(
      index: _currentIndex,
      children: [
        _buildHomeScreen(),
        _buildTrackScreen(),
        _buildShipmentScreen(),
        _buildAccountScreen(),
      ],
    );
  }

  Widget _buildHomeScreen() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.home,
            size: 80,
            color: AppColors.primary,
          ),
          SizedBox(height: 16),
          Text(
            'Home Screen',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.black,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Welcome to your dashboard',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.black,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrackScreen() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_on,
            size: 80,
            color: AppColors.primary,
          ),
          SizedBox(height: 16),
          Text(
            'Track Screen',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.black,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Track your orders in real-time',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.black,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShipmentScreen() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_shipping,
            size: 80,
            color: AppColors.primary,
          ),
          SizedBox(height: 16),
          Text(
            'Shipment Screen',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.black,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Manage your shipments',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.black,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountScreen() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person,
            size: 80,
            color: AppColors.primary,
          ),
          SizedBox(height: 16),
          Text(
            'Account Screen',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.black,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Manage your profile and settings',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.black,
            ),
          ),
        ],
      ),
    );
  }

  void _onNavItemTapped(int index) {
    setState(() {
      _currentIndex = index;
    });

    // Show a snackbar to demonstrate the callback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Navigated to ${_navItems[index].label}'),
        duration: const Duration(milliseconds: 1000),
        backgroundColor: AppColors.primary,
      ),
    );
  }
}

/// Alternative example with custom styling
class CustomStyledBottomNavExample extends StatefulWidget {
  const CustomStyledBottomNavExample({super.key});

  @override
  State<CustomStyledBottomNavExample> createState() => _CustomStyledBottomNavExampleState();
}

class _CustomStyledBottomNavExampleState extends State<CustomStyledBottomNavExample> {
  int _currentIndex = 0;

  // Custom styled navigation items
  final List<BottomNavItem> _customNavItems = [
    BottomNavItem(
      icon: Icons.dashboard_outlined,
      activeIcon: Icons.dashboard,
      label: 'Dashboard',
      activeColor: AppColors.success,
      inactiveColor: AppColors.black.withValues(alpha: 0.4),
      activeBackgroundColor: AppColors.success.withValues(alpha: 0.1),
    ),
    BottomNavItem(
      icon: Icons.search_outlined,
      activeIcon: Icons.search,
      label: 'Search',
      activeColor: AppColors.warning,
      inactiveColor: AppColors.black.withValues(alpha: 0.4),
      activeBackgroundColor: AppColors.warning.withValues(alpha: 0.1),
    ),
    BottomNavItem(
      icon: Icons.notifications_outlined,
      activeIcon: Icons.notifications,
      label: 'Alerts',
      activeColor: AppColors.error,
      inactiveColor: AppColors.black.withValues(alpha: 0.4),
      activeBackgroundColor: AppColors.error.withValues(alpha: 0.1),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Custom Styled Navigation'),
        backgroundColor: AppColors.success,
        foregroundColor: AppColors.white,
      ),
      body: Center(
        child: Text(
          'Custom Navigation Example\nCurrent: ${_customNavItems[_currentIndex].label}',
          textAlign: TextAlign.center,
          style: const TextStyle(fontSize: 18),
        ),
      ),
      bottomNavigationBar: UserBottomNavigation(
        items: _customNavItems,
        currentIndex: _currentIndex,
        onTap: (index) => setState(() => _currentIndex = index),
        backgroundColor: AppColors.white,
        height: 75,
      ),
    );
  }
}
