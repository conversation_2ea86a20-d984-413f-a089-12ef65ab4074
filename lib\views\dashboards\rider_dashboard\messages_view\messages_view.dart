import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/views/dashboards/rider_dashboard/messages_view/chat_view.dart';

/// Messages list view page for rider dashboard
///
/// This page displays all conversations with customers and support,
/// showing profile pictures, names, and message previews.
class MessagesView extends StatefulWidget {
  const MessagesView({super.key});

  @override
  State<MessagesView> createState() => _MessagesViewState();
}

class _MessagesViewState extends State<MessagesView> {
  // Sample conversations data
  final List<Map<String, dynamic>> _conversations = [
    {
      'id': '1',
      'name': 'Customer Support',
      'lastMessage': 'Welcome to RideOn! How can we help you today?',
      'timestamp': '2024-12-17 09:00:00',
      'unreadCount': 0,
      'isOnline': true,
      'type': 'support',
      'avatar': null,
    },
    {
      'id': '2',
      'name': '<PERSON>',
      'lastMessage': 'Hi, I need help with my delivery order #12345',
      'timestamp': '2024-12-17 10:30:00',
      'unreadCount': 2,
      'isOnline': false,
      'type': 'customer',
      'avatar': null,
    },
    {
      'id': '3',
      'name': 'Sarah Williams',
      'lastMessage': 'Thank you for the quick delivery!',
      'timestamp': '2024-12-17 08:45:00',
      'unreadCount': 0,
      'isOnline': true,
      'type': 'customer',
      'avatar': null,
    },
    {
      'id': '4',
      'name': 'Mike Johnson',
      'lastMessage': 'Are you on your way?',
      'timestamp': '2024-12-16 16:20:00',
      'unreadCount': 1,
      'isOnline': false,
      'type': 'customer',
      'avatar': null,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5FF),
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 7% gap from top of screen
            SizedBox(height: MediaQuery.of(context).size.height * 0.07),

            // Header
            _buildHeader(context),

            SizedBox(height: _getSpacing(context, 16)),

            // Subtitle
            _buildSubtitle(context),

            SizedBox(height: _getSpacing(context, 24)),

            // New chat button
            _buildNewChatButton(context),

            SizedBox(height: _getSpacing(context, 16)),

            // Conversations list
            Expanded(
              child: _buildConversationsList(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Center(
      child: Text(
        'Messages',
        style: TextStyle(
          color: const Color(0xFF414141),
          fontSize: _getFontSize(context, 20),
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildSubtitle(BuildContext context) {
    return Center(
      child: Text(
        'Chat with customers and support team',
        style: TextStyle(
          color: const Color(0xFF9A9A9A),
          fontSize: _getFontSize(context, 10),
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w500,
          letterSpacing: 0.20,
        ),
      ),
    );
  }

  Widget _buildNewChatButton(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: () {
            _startNewChatWithSupport(context);
          },
          icon: Icon(
            Icons.add_comment,
            size: _getIconSize(context, 18),
            color: AppColors.white,
          ),
          label: Text(
            'New Chat with Customer Support',
            style: TextStyle(
              fontSize: _getFontSize(context, 14),
              fontWeight: FontWeight.w500,
              fontFamily: 'Poppins',
              color: AppColors.white,
            ),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.white,
            padding: EdgeInsets.symmetric(
              horizontal: _getSpacing(context, 20),
              vertical: _getSpacing(context, 16),
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
            ),
            elevation: 0,
          ),
        ),
      ),
    );
  }

  Widget _buildConversationsList(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: ListView.builder(
        itemCount: _conversations.length,
        itemBuilder: (context, index) {
          final conversation = _conversations[index];
          return Padding(
            padding: EdgeInsets.only(bottom: _getSpacing(context, 12)),
            child: _buildConversationCard(context, conversation),
          );
        },
      ),
    );
  }

  Widget _buildConversationCard(BuildContext context, Map<String, dynamic> conversation) {
    final unreadCount = conversation['unreadCount'] as int;
    final isOnline = conversation['isOnline'] as bool;
    final conversationType = conversation['type'] as String;

    return GestureDetector(
      onTap: () {
        _openChat(context, conversation);
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(_getSpacing(context, 16)),
        decoration: ShapeDecoration(
          color: const Color(0xFFFEFEFE),
          shape: RoundedRectangleBorder(
            side: BorderSide(
              width: 1,
              color: AppColors.black.withValues(alpha: 0.07),
            ),
            borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
          ),
        ),
        child: Row(
          children: [
            // Profile picture with online indicator
            Stack(
              children: [
                Container(
                  width: _getAvatarSize(context),
                  height: _getAvatarSize(context),
                  decoration: BoxDecoration(
                    color: _getAvatarColor(conversationType),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _getAvatarIcon(conversationType),
                    size: _getIconSize(context, 24),
                    color: AppColors.white,
                  ),
                ),
                if (isOnline && conversationType != 'support')
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      width: _getSpacing(context, 12),
                      height: _getSpacing(context, 12),
                      decoration: BoxDecoration(
                        color: AppColors.success,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: AppColors.white,
                          width: 2,
                        ),
                      ),
                    ),
                  ),
              ],
            ),

            SizedBox(width: _getSpacing(context, 12)),

            // Conversation details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        conversation['name'] as String,
                        style: TextStyle(
                          fontSize: _getFontSize(context, 16),
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Poppins',
                          color: AppColors.black,
                        ),
                      ),
                      Text(
                        _formatTimestamp(conversation['timestamp'] as String),
                        style: TextStyle(
                          fontSize: _getFontSize(context, 10),
                          fontFamily: 'Poppins',
                          color: const Color(0xFF9A9A9A),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: _getSpacing(context, 4)),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          conversation['lastMessage'] as String,
                          style: TextStyle(
                            fontSize: _getFontSize(context, 12),
                            fontFamily: 'Poppins',
                            color: AppColors.black.withValues(alpha: 0.7),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (unreadCount > 0) ...[
                        SizedBox(width: _getSpacing(context, 8)),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: _getSpacing(context, 8),
                            vertical: _getSpacing(context, 4),
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(_getBorderRadius(context, 10)),
                          ),
                          child: Text(
                            unreadCount.toString(),
                            style: TextStyle(
                              fontSize: _getFontSize(context, 10),
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Poppins',
                              color: AppColors.white,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  void _startNewChatWithSupport(BuildContext context) {
    final supportConversation = {
      'id': 'support_${DateTime.now().millisecondsSinceEpoch}',
      'name': 'Customer Support',
      'type': 'support',
      'isOnline': true,
    };

    _openChat(context, supportConversation);
  }

  void _openChat(BuildContext context, Map<String, dynamic> conversation) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatView(
          conversationId: conversation['id'] as String,
          conversationName: conversation['name'] as String,
          conversationType: conversation['type'] as String,
        ),
      ),
    );
  }

  Color _getAvatarColor(String type) {
    switch (type) {
      case 'support':
        return AppColors.primary;
      case 'customer':
        return AppColors.success;
      default:
        return AppColors.black.withValues(alpha: 0.6);
    }
  }

  IconData _getAvatarIcon(String type) {
    switch (type) {
      case 'support':
        return Icons.support_agent;
      case 'customer':
        return Icons.person;
      default:
        return Icons.person;
    }
  }

  double _getAvatarSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 40;
    if (screenWidth > 600) return 60;
    return 50;
  }

  String _formatTimestamp(String timestamp) {
    try {
      final dateTime = DateTime.parse(timestamp);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inDays > 0) {
        return '${difference.inDays}d ago';
      } else if (difference.inHours > 0) {
        return '${difference.inHours}h ago';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes}m ago';
      } else {
        return 'Just now';
      }
    } catch (e) {
      return 'Unknown';
    }
  }

  // Responsive helper methods (matching deliveries view pattern)
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 40; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet
    } else {
      spacing = baseSpacing; // Mobile
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2; // Tablet
    } else {
      fontSize = baseFontSize; // Mobile
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2; // Tablet
    } else {
      iconSize = baseIconSize; // Mobile
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2; // Tablet
    } else {
      borderRadius = baseBorderRadius; // Mobile
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.9;
    }

    return borderRadius;
  }
}
