import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';

/// A floating action button for viewing all orders
///
/// This widget provides a consistent floating button across different views
/// to allow users to quickly access their orders summary.
///
/// Features:
/// - Responsive design for mobile, tablet, and smartwatch
/// - Consistent styling with app theme
/// - Badge indicator for order count
/// - Smooth animations and interactions
class FloatingOrdersButton extends StatelessWidget {
  /// Callback when the button is tapped
  final VoidCallback onTap;

  /// Number of orders to display in badge (optional)
  final int? orderCount;

  /// Custom position from bottom (default: 16)
  final double? bottom;

  /// Custom position from right (default: 16)
  final double? right;

  /// Custom button size (default: responsive)
  final double? size;

  /// Whether to show the badge even when count is 0
  final bool showBadgeWhenZero;

  const FloatingOrdersButton({
    super.key,
    required this.onTap,
    this.orderCount,
    this.bottom,
    this.right,
    this.size,
    this.showBadgeWhenZero = false,
  });

  @override
  Widget build(BuildContext context) {
    final buttonSize = size ?? _getButtonSize(context);
    final shouldShowBadge = orderCount != null && 
        (orderCount! > 0 || showBadgeWhenZero);

    return Positioned(
      bottom: bottom ?? _getSpacing(context, 16),
      right: right ?? _getSpacing(context, 16),
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: buttonSize,
          height: buttonSize,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(buttonSize / 2),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withValues(alpha: 0.3),
                blurRadius: 12,
                offset: Offset(0, 6),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: AppColors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: Offset(0, 2),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Stack(
            children: [
              // Main icon
              Center(
                child: Icon(
                  Icons.receipt_long,
                  color: AppColors.white,
                  size: _getIconSize(context),
                ),
              ),

              // Badge indicator
              if (shouldShowBadge)
                Positioned(
                  top: _getSpacing(context, 8),
                  right: _getSpacing(context, 8),
                  child: Container(
                    width: _getBadgeSize(context),
                    height: _getBadgeSize(context),
                    padding: EdgeInsets.symmetric(
                      horizontal: _getSpacing(context, 6),
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(_getBadgeSize(context) / 2),
                      border: Border.all(
                        color: AppColors.primary,
                        width: 1.5,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        orderCount! > 99 ? '99+' : orderCount.toString(),
                        style: TextStyle(
                          color: AppColors.primary,
                          fontSize: _getBadgeFontSize(context),
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w700,
                          height: 1.0,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // Responsive helper methods
  double _getButtonSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 48; // Smartwatch - smaller button
    } else if (screenWidth > 600) {
      baseSize = 64; // Tablet - larger button
    } else {
      baseSize = 56; // Mobile - standard FAB size
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9; // Reduce for short screens
    }

    return baseSize;
  }

  double _getIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 20; // Smartwatch - smaller icon
    } else if (screenWidth > 600) {
      baseSize = 28; // Tablet - larger icon
    } else {
      baseSize = 24; // Mobile - standard icon size
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9; // Reduce for short screens
    }

    return baseSize;
  }

  double _getBadgeSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 16; // Smartwatch - smaller badge
    } else if (screenWidth > 600) {
      baseSize = 22; // Tablet - larger badge
    } else {
      baseSize = 18; // Mobile - standard badge size
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9; // Reduce for short screens
    }

    return baseSize;
  }

  double _getBadgeFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 8; // Smartwatch - smaller text
    } else if (screenWidth > 600) {
      baseSize = 12; // Tablet - larger text
    } else {
      baseSize = 10; // Mobile - standard text size
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9; // Reduce for short screens
    }

    return baseSize;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.7; // Smartwatch - smaller spacing
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet - larger spacing
    } else {
      spacing = baseSpacing; // Mobile - standard spacing
    }

    if (isShortScreen) {
      spacing = spacing * 0.8; // Reduce for short screens
    }

    return spacing;
  }
}
