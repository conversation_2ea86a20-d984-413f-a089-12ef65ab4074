import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/services/onboarding_wrapper.dart';
import 'package:rideoon/views/authentication/userauth/sign_up.dart';
import 'package:rideoon/views/authentication/riderauth/sign_in.dart';

class UserTypeSelection extends StatefulWidget {
  const UserTypeSelection({super.key});

  @override
  State<UserTypeSelection> createState() => _UserTypeSelectionState();
}

class _UserTypeSelectionState extends State<UserTypeSelection>
    with TickerProviderStateMixin {
  String? _selectedType;
  bool _isAnimating = false;

  late AnimationController _fadeController;
  late AnimationController _moveController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _moveAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _moveController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _moveAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset.zero, // Will be calculated dynamically
    ).animate(CurvedAnimation(
      parent: _moveController,
      curve: Curves.easeInOutCubic,
    ));
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _moveController.dispose();
    super.dispose();
  }

  void _selectUserType(String userType) async {
    if (_isAnimating) return;

    setState(() {
      _selectedType = userType;
      _isAnimating = true;
    });

    // Save user type
    await OnboardingService.setUserType(userType);
    await OnboardingService.setOnboardingCompleted();

    // Calculate move offset to center the selected card
    final isClient = userType == 'client';
    // Move client up by half card height, rider down by half card height
    final moveOffset = isClient ? -0.3 : 0.3;

    _moveAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset(0, moveOffset),
    ).animate(CurvedAnimation(
      parent: _moveController,
      curve: Curves.easeInOutCubic,
    ));

    // Start animations in sequence
    _fadeController.forward(); // Fade out unselected

    await Future.delayed(const Duration(milliseconds: 300));
    _moveController.forward(); // Move to center

    // Wait a bit in the center before transitioning
    await Future.delayed(const Duration(milliseconds: 800));

    if (mounted) {
      // Navigate to appropriate screen
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => userType == 'client'
              ? const UserSignUp()
              : const RiderSignIn(),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: _isAnimating
          ? _buildAnimatingLayout(context)
          : _buildStaticLayout(context),
      ),
    );
  }

  Widget _buildStaticLayout(BuildContext context) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Container(
        constraints: BoxConstraints(
          minHeight: MediaQuery.of(context).size.height -
                     MediaQuery.of(context).padding.top -
                     MediaQuery.of(context).padding.bottom,
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: _getHorizontalPadding(context),
            vertical: _getVerticalPadding(context),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Top spacer
              SizedBox(height: _getSpacing(context, 40)),

              // Title
              _buildTitle(context),

              SizedBox(height: _getSpacing(context, 60)),

              // User type options
              _buildUserTypeOptions(context),

              // Bottom spacer
              SizedBox(height: _getSpacing(context, 40)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatingLayout(BuildContext context) {
    return Stack(
      children: [
        // Background overlay during animation
        Container(
          width: double.infinity,
          height: double.infinity,
          color: AppColors.white,
        ),

        // Positioned cards for animation
        Positioned.fill(
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Container(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height -
                           MediaQuery.of(context).padding.top -
                           MediaQuery.of(context).padding.bottom,
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: _getHorizontalPadding(context),
                  vertical: _getVerticalPadding(context),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Top spacer
                    SizedBox(height: _getSpacing(context, 40)),

                    // Title (fade out during animation)
                    AnimatedOpacity(
                      opacity: _isAnimating ? 0.0 : 1.0,
                      duration: const Duration(milliseconds: 300),
                      child: _buildTitle(context),
                    ),

                    SizedBox(height: _getSpacing(context, 60)),

                    // User type options with animations
                    _buildUserTypeOptions(context),

                    // Bottom spacer
                    SizedBox(height: _getSpacing(context, 40)),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Text(
      'Choose Your Role',
      style: TextStyle(
        fontSize: _getTitleFontSize(context),
        fontWeight: FontWeight.bold,
        color: AppColors.black,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildUserTypeOptions(BuildContext context) {
    return Column(
      children: [
        _buildUserTypeCard(
          context,
          'client',
          'Client',
          Icons.person,
          'Order deliveries and logistics services',
        ),

        SizedBox(height: _getSpacing(context, 24)),

        _buildUserTypeCard(
          context,
          'rider',
          'Rider',
          Icons.delivery_dining,
          'Provide delivery and logistics services',
        ),
      ],
    );
  }

  Widget _buildUserTypeCard(
    BuildContext context,
    String type,
    String title,
    IconData icon,
    String description,
  ) {
    final isSelected = _selectedType == type;
    final shouldFade = _isAnimating && !isSelected;
    final shouldMove = _isAnimating && isSelected;

    return AnimatedBuilder(
      animation: Listenable.merge([_fadeController, _moveController]),
      builder: (context, child) {
        Widget cardWidget = _buildCardContent(context, type, title, icon, description);

        // Apply movement to center
        if (shouldMove) {
          cardWidget = SlideTransition(
            position: _moveAnimation,
            child: cardWidget,
          );
        }

        // Apply fade for unselected
        if (shouldFade) {
          cardWidget = Opacity(
            opacity: _fadeAnimation.value,
            child: cardWidget,
          );
        }

        return cardWidget;
      },
    );
  }

  Widget _buildCardContent(
    BuildContext context,
    String type,
    String title,
    IconData icon,
    String description,
  ) {
    return GestureDetector(
      onTap: () => _selectUserType(type),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(_getCardPadding(context)),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          border: Border.all(
            color: AppColors.primary.withValues(alpha: 0.2),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            // Icon
            Container(
              width: _getIconSize(context),
              height: _getIconSize(context),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: _getIconSize(context) * 0.5,
                color: AppColors.primary,
              ),
            ),

            SizedBox(height: _getSpacing(context, 16)),

            // Title
            Text(
              title,
              style: TextStyle(
                fontSize: _getCardTitleFontSize(context),
                fontWeight: FontWeight.bold,
                color: AppColors.black,
              ),
            ),

            SizedBox(height: _getSpacing(context, 8)),

            // Description
            Text(
              description,
              style: TextStyle(
                fontSize: _getCardDescriptionFontSize(context),
                color: AppColors.black.withValues(alpha: 0.6),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 16; // Smartwatch
    if (screenWidth > 600) return 80; // Tablet
    return 32; // Mobile
  }

  double _getVerticalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 40; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    // Reduce padding for short screens
    if (isShortScreen) {
      basePadding = basePadding * 0.5;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet
    } else {
      spacing = baseSpacing; // Mobile
    }

    // Reduce spacing for short screens
    if (isShortScreen) {
      spacing = spacing * 0.7;
    }

    return spacing;
  }

  double _getTitleFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 20; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 36; // Tablet
    } else {
      baseSize = 28; // Mobile
    }

    // Reduce font size for short screens
    if (isShortScreen) {
      baseSize = baseSize * 0.8;
    }

    return baseSize;
  }

  double _getCardPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 32; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    // Reduce padding for short screens
    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 12; // Smartwatch
    if (screenWidth > 600) return 20; // Tablet
    return 16; // Mobile
  }

  double _getIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 40; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 80; // Tablet
    } else {
      baseSize = 60; // Mobile
    }

    // Reduce icon size for short screens
    if (isShortScreen) {
      baseSize = baseSize * 0.8;
    }

    return baseSize;
  }

  double _getCardTitleFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 16; // Smartwatch
    if (screenWidth > 600) return 24; // Tablet
    return 20; // Mobile
  }

  double _getCardDescriptionFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 12; // Smartwatch
    if (screenWidth > 600) return 16; // Tablet
    return 14; // Mobile
  }
}