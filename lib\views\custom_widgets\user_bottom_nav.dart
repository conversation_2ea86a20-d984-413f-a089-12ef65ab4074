import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';

/// Data class for bottom navigation items
class BottomNavItem {
  /// The icon to display when the item is not active
  final IconData icon;

  /// The icon to display when the item is active (optional)
  final IconData? activeIcon;

  /// The label text for the navigation item
  final String label;

  /// Color when the item is active
  final Color? activeColor;

  /// Color when the item is inactive
  final Color? inactiveColor;

  /// Background color when the item is active (optional)
  final Color? activeBackgroundColor;

  /// Tooltip text for accessibility
  final String? tooltip;

  const BottomNavItem({
    required this.icon,
    required this.label,
    this.activeIcon,
    this.activeColor,
    this.inactiveColor,
    this.activeBackgroundColor,
    this.tooltip,
  });
}

/// A reusable bottom navigation bar widget for user screens
///
/// This widget provides a customizable bottom navigation bar with
/// responsive design and consistent styling throughout the app.
///
/// Features:
/// - Responsive design for mobile, tablet, and smartwatch
/// - Customizable navigation items with icons and labels
/// - Active/inactive state management
/// - Callback functions for navigation
/// - Consistent styling with app theme
/// - Accessibility support
class UserBottomNavigation extends StatelessWidget {
  /// List of navigation items to display
  final List<BottomNavItem> items;

  /// Currently selected index
  final int currentIndex;

  /// Callback function when a navigation item is tapped
  final Function(int index) onTap;

  /// Background color of the navigation bar
  final Color? backgroundColor;

  /// Whether to show labels on navigation items
  final bool showLabels;

  /// Height of the navigation bar (will be adjusted responsively)
  final double? height;

  const UserBottomNavigation({
    super.key,
    required this.items,
    required this.currentIndex,
    required this.onTap,
    this.backgroundColor,
    this.showLabels = true,
    this.height,
  }) : assert(items.length >= 2 && items.length <= 5, 'Navigation must have 2-5 items');

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? _getNavigationHeight(context),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: _getSpacing(context, 8),
            offset: Offset(0, -_getSpacing(context, 2)),
            spreadRadius: 0,
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: _getHorizontalPadding(context),
            vertical: _getVerticalPadding(context),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: List.generate(
              items.length,
              (index) => _buildNavigationItem(context, index),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavigationItem(BuildContext context, int index) {
    final item = items[index];
    final isActive = index == currentIndex;

    return Expanded(
      child: GestureDetector(
        onTap: () => onTap(index),
        behavior: HitTestBehavior.opaque,
        child: Container(
          padding: EdgeInsets.symmetric(
            vertical: _getSpacing(context, 4),
            horizontal: _getSpacing(context, 8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Icon
              _buildIcon(context, item, isActive),

              if (showLabels) ...[
                SizedBox(height: _getSpacing(context, 4)),
                // Label
                _buildLabel(context, item, isActive),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIcon(BuildContext context, BottomNavItem item, bool isActive) {
    return Container(
      width: _getIconSize(context),
      height: _getIconSize(context),
      decoration: isActive && item.activeBackgroundColor != null
          ? BoxDecoration(
              color: item.activeBackgroundColor,
              shape: BoxShape.circle,
            )
          : null,
      child: Icon(
        isActive ? (item.activeIcon ?? item.icon) : item.icon,
        size: _getIconSize(context) * 0.6,
        color: isActive
            ? (item.activeColor ?? AppColors.primary)
            : (item.inactiveColor ?? AppColors.black.withValues(alpha: 0.5)),
      ),
    );
  }

  Widget _buildLabel(BuildContext context, BottomNavItem item, bool isActive) {
    return Text(
      item.label,
      textAlign: TextAlign.center,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        color: isActive
            ? (item.activeColor ?? AppColors.primary)
            : (item.inactiveColor ?? AppColors.black.withValues(alpha: 0.5)),
        fontSize: _getLabelFontSize(context),
        fontFamily: 'Poppins',
        fontWeight: isActive ? FontWeight.w500 : FontWeight.w400,
        height: 1.2,
      ),
    );
  }

  // Responsive helper methods
  double _getNavigationHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseHeight;
    if (screenWidth < 300) {
      baseHeight = 60; // Smartwatch
    } else if (screenWidth > 600) {
      baseHeight = 80; // Tablet
    } else {
      baseHeight = 69; // Mobile (matching original design)
    }

    if (isShortScreen) {
      baseHeight = baseHeight * 0.9;
    }

    return baseHeight;
  }

  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 8; // Smartwatch
    } else if (screenWidth > 600) {
      return 16; // Tablet
    } else {
      return 10; // Mobile (matching original design)
    }
  }

  double _getVerticalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 6; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 12; // Tablet
    } else {
      basePadding = 8; // Mobile
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet
    } else {
      spacing = baseSpacing; // Mobile
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 20; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 32; // Tablet
    } else {
      baseSize = 24; // Mobile (matching original design)
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getLabelFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 15; // Tablet
    } else {
      baseSize = 13; // Mobile (matching original design)
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }
}