import 'package:flutter_test/flutter_test.dart';
import 'package:rideoon/services/config_service.dart';

void main() {
  group('ConfigService Tests', () {
    setUpAll(() async {
      // Initialize the config service for testing
      await ConfigService.initialize();
    });

    test('should load environment variables', () {
      // Test that basic configuration is loaded
      expect(ConfigService.appName, isNotEmpty);
      expect(ConfigService.appVersion, isNotEmpty);
      expect(ConfigService.appEnvironment, isNotEmpty);
    });

    test('should have correct default values', () {
      // Test default values
      expect(ConfigService.appName, equals('RideOn'));
      expect(ConfigService.appVersion, equals('1.0.0'));
      expect(ConfigService.appEnvironment, equals('development'));
    });

    test('should correctly identify development environment', () {
      // Test environment detection
      expect(ConfigService.isDevelopment, isTrue);
      expect(ConfigService.isProduction, isFalse);
      expect(ConfigService.isStaging, isFalse);
    });

    test('should parse boolean values correctly', () {
      // Test boolean parsing
      expect(ConfigService.enableDebugMode, isA<bool>());
      expect(ConfigService.enableAnalytics, isA<bool>());
      expect(ConfigService.enableCrashReporting, isA<bool>());
    });

    test('should parse integer values correctly', () {
      // Test integer parsing
      expect(ConfigService.apiTimeout, isA<int>());
      expect(ConfigService.apiTimeout, greaterThan(0));
    });

    test('should provide API configuration', () {
      // Test API configuration
      expect(ConfigService.apiBaseUrl, isNotEmpty);
      expect(ConfigService.apiBaseUrl, startsWith('http'));
    });

    test('should handle custom values', () {
      // Test custom value retrieval
      final customValue = ConfigService.getCustomValue('APP_NAME');
      expect(customValue, equals('RideOn'));
      
      final nonExistentValue = ConfigService.getCustomValue('NON_EXISTENT_KEY');
      expect(nonExistentValue, isNull);
    });

    test('should check if values exist', () {
      // Test value existence checking
      expect(ConfigService.hasValue('APP_NAME'), isTrue);
      expect(ConfigService.hasValue('NON_EXISTENT_KEY'), isFalse);
    });

    test('should validate required variables in development', () {
      // In development mode, validation should pass
      expect(() => ConfigService.validateRequiredVariables(), returnsNormally);
    });

    test('should provide all values in development mode', () {
      // Test getting all values (only works in development)
      expect(() => ConfigService.getAllValues(), returnsNormally);
      final allValues = ConfigService.getAllValues();
      expect(allValues, isA<Map<String, String>>());
      expect(allValues.containsKey('APP_NAME'), isTrue);
    });
  });
}
