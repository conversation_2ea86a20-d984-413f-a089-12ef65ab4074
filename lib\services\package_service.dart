import 'dart:async';
import 'package:rideoon/services/api_service.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/models/api_response.dart';
import 'package:rideoon/models/order/package.dart';

/// Service class for handling package-related API calls
/// 
/// This service provides methods for:
/// - Getting package categories
/// - Creating packages within orders
/// - Managing package details
/// - Package-specific operations
class PackageService {
  
  /// Get all package categories
  /// GET /v1/s/package-categories/
  static Future<ApiResponse<List<PackageCategory>>> getPackageCategories({
    Map<String, String>? queryParams,
  }) async {
    return await ApiService.makeRequest<List<PackageCategory>>(
      endpoint: '/v1/s/package-categories/',
      method: 'GET',
      queryParams: queryParams,
      requiresAuth: false, // Public endpoint
      fromJson: (json) {
        final List<dynamic> categoriesJson = json['categories'] ?? json['data'] ?? [];
        return categoriesJson.map((categoryJson) => PackageCategory.fromJson(categoryJson)).toList();
      },
    );
  }

  /// Get package categories configuration
  /// GET /v1/s/config/package-categories/
  static Future<ApiResponse<Map<String, dynamic>>> getPackageCategoriesConfig({
    Map<String, String>? queryParams,
  }) async {
    return await ApiService.makeRequest<Map<String, dynamic>>(
      endpoint: '/v1/s/config/package-categories/',
      method: 'GET',
      queryParams: queryParams,
      requiresAuth: false, // Public endpoint
      fromJson: (json) => json,
    );
  }

  /// Create a new package within an order
  /// This method combines order and package creation logic
  static Future<ApiResponse<Package>> createPackageInOrder(
    String orderUuid,
    CreatePackageRequest request, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();
    
    return await ApiService.makeRequest<Package>(
      endpoint: '/v1/auth/client/order/$orderUuid/packages',
      method: 'POST',
      body: request.toJson(),
      requiresAuth: true,
      authToken: token,
      fromJson: (json) => Package.fromJson(json),
    );
  }

  /// Update package details within an order
  static Future<ApiResponse<Package>> updatePackageInOrder(
    String orderUuid,
    String packageUuid,
    UpdatePackageRequest request, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();
    
    return await ApiService.makeRequest<Package>(
      endpoint: '/v1/auth/client/order/$orderUuid/packages/$packageUuid',
      method: 'PUT',
      body: request.toJson(),
      requiresAuth: true,
      authToken: token,
      fromJson: (json) => Package.fromJson(json),
    );
  }

  /// Delete a package from an order
  static Future<ApiResponse<Map<String, dynamic>>> deletePackageFromOrder(
    String orderUuid,
    String packageUuid, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();
    
    return await ApiService.makeRequest<Map<String, dynamic>>(
      endpoint: '/v1/auth/client/order/$orderUuid/packages/$packageUuid',
      method: 'DELETE',
      requiresAuth: true,
      authToken: token,
      fromJson: (json) => json,
    );
  }

  /// Get package details by UUID within an order
  static Future<ApiResponse<Package>> getPackageDetails(
    String orderUuid,
    String packageUuid, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();
    
    return await ApiService.makeRequest<Package>(
      endpoint: '/v1/auth/client/order/$orderUuid/packages/$packageUuid',
      method: 'GET',
      requiresAuth: true,
      authToken: token,
      fromJson: (json) => Package.fromJson(json),
    );
  }

  /// Get all packages for a specific order
  static Future<ApiResponse<List<Package>>> getOrderPackages(
    String orderUuid, {
    String? authToken,
  }) async {
    final token = authToken ?? await AuthService.getAuthToken();
    
    return await ApiService.makeRequest<List<Package>>(
      endpoint: '/v1/auth/client/order/$orderUuid/packages',
      method: 'GET',
      requiresAuth: true,
      authToken: token,
      fromJson: (json) {
        final List<dynamic> packagesJson = json['packages'] ?? json['data'] ?? [];
        return packagesJson.map((packageJson) => Package.fromJson(packageJson)).toList();
      },
    );
  }

  /// Calculate package weight in standardized format
  static Map<String, dynamic> calculatePackageWeight({
    required double weightValue,
    required String weightUnit, // 'kg', 'g', 'lb', 'oz'
  }) {
    // Convert everything to grams for standardization
    double weightInGrams;
    
    switch (weightUnit.toLowerCase()) {
      case 'kg':
        weightInGrams = weightValue * 1000;
        break;
      case 'g':
        weightInGrams = weightValue;
        break;
      case 'lb':
        weightInGrams = weightValue * 453.592;
        break;
      case 'oz':
        weightInGrams = weightValue * 28.3495;
        break;
      default:
        weightInGrams = weightValue; // Assume grams if unknown
    }

    return {
      'value': weightValue,
      'unit': weightUnit,
      'grams': weightInGrams,
      'kilograms': weightInGrams / 1000,
    };
  }

  /// Validate package data before creation
  static Map<String, String> validatePackageData(CreatePackageRequest request) {
    final errors = <String, String>{};

    if (request.quantity <= 0) {
      errors['quantity'] = 'Quantity must be greater than 0';
    }

    if (request.category.isEmpty) {
      errors['category'] = 'At least one category must be selected';
    }

    if (request.pickupAddressUuid.isEmpty) {
      errors['pickupAddress'] = 'Pickup address is required';
    }

    if (request.deliveryAddressUuid.isEmpty) {
      errors['deliveryAddress'] = 'Delivery address is required';
    }

    if (request.packagePickupType.isEmpty) {
      errors['packagePickupType'] = 'Package pickup type is required';
    }

    // Validate weight if provided
    if (request.weight != null) {
      final weight = request.weight!;
      if (!weight.containsKey('value') || !weight.containsKey('unit')) {
        errors['weight'] = 'Weight must include both value and unit';
      } else {
        final weightValue = weight['value'];
        if (weightValue is! num || weightValue <= 0) {
          errors['weight'] = 'Weight value must be a positive number';
        }
      }
    }

    return errors;
  }

  /// Get package pickup types
  static List<String> getPackagePickupTypes() {
    return [
      'instant',
      'scheduled',
      'flexible',
    ];
  }

  /// Get package pickup type display names
  static String getPickupTypeDisplayName(String pickupType) {
    switch (pickupType.toLowerCase()) {
      case 'instant':
        return 'Instant Pickup';
      case 'scheduled':
        return 'Scheduled Pickup';
      case 'flexible':
        return 'Flexible Pickup';
      default:
        return pickupType.replaceAll('_', ' ').split(' ')
            .map((word) => word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : '')
            .join(' ');
    }
  }

  /// Create package request from form data
  static CreatePackageRequest createPackageRequestFromForm({
    required int quantity,
    required List<String> categoryIds,
    String? label,
    String? detail,
    bool fragile = false,
    required String packagePickupType,
    DateTime? packagePickupDate,
    double? weightValue,
    String? weightUnit,
    required String pickupAddressUuid,
    required String deliveryAddressUuid,
  }) {
    Map<String, dynamic>? weight;
    if (weightValue != null && weightUnit != null) {
      weight = calculatePackageWeight(
        weightValue: weightValue,
        weightUnit: weightUnit,
      );
    }

    return CreatePackageRequest(
      quantity: quantity,
      category: categoryIds,
      label: label,
      detail: detail,
      fragile: fragile,
      packagePickupType: packagePickupType,
      packagePickupDate: packagePickupDate,
      weight: weight,
      pickupAddressUuid: pickupAddressUuid,
      deliveryAddressUuid: deliveryAddressUuid,
    );
  }

  /// Get package summary for display
  static Map<String, dynamic> getPackageSummary(Package package) {
    return {
      'uuid': package.uuid,
      'label': package.label ?? 'Package',
      'quantity': package.quantity,
      'fragile': package.fragile,
      'categories': package.category,
      'pickupType': getPickupTypeDisplayName(package.packagePickupType),
      'weight': package.weight,
      'price': package.price,
    };
  }
}
