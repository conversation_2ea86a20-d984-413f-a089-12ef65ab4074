import 'package:flutter/foundation.dart';
import 'package:rideoon/services/package_service.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/models/order/package.dart';

/// Provider for managing package state across the application
///
/// This provider handles:
/// - Loading and managing package categories
/// - Creating packages within orders
/// - Managing package form data
/// - Package validation and state management
/// - Notifying listeners when package data changes
class PackageProvider extends ChangeNotifier {
  List<PackageCategory> _categories = [];
  Map<String, dynamic>? _categoriesConfig;
  Package? _currentPackage;
  Map<String, dynamic> _packageFormData = {};
  bool _isLoading = false;
  String? _error;

  /// Get all package categories
  List<PackageCategory> get categories => List.unmodifiable(_categories);

  /// Get categories configuration
  Map<String, dynamic>? get categoriesConfig => _categoriesConfig;

  /// Get current package being worked on
  Package? get currentPackage => _currentPackage;

  /// Get package form data
  Map<String, dynamic> get packageFormData => Map.unmodifiable(_packageFormData);

  /// Check if currently loading
  bool get isLoading => _isLoading;

  /// Get current error message
  String? get error => _error;

  /// Get active categories only
  List<PackageCategory> get activeCategories => 
      _categories.where((category) => category.active).toList();

  /// Load package categories from the API
  Future<void> loadCategories({Map<String, String>? queryParams}) async {
    try {
      _setLoading(true);
      _clearError();

      final response = await PackageService.getPackageCategories(queryParams: queryParams);
      
      if (response.success && response.data != null) {
        _categories = response.data!;
      } else {
        _setError('Failed to load package categories: ${response.message}');
        _categories = [];
      }
    } catch (e) {
      _setError('Failed to load package categories: $e');
      _categories = [];
    } finally {
      _setLoading(false);
    }
  }

  /// Load package categories configuration
  Future<void> loadCategoriesConfig({Map<String, String>? queryParams}) async {
    try {
      _setLoading(true);
      _clearError();

      final response = await PackageService.getPackageCategoriesConfig(queryParams: queryParams);
      
      if (response.success && response.data != null) {
        _categoriesConfig = response.data!;
      } else {
        _setError('Failed to load categories config: ${response.message}');
        _categoriesConfig = null;
      }
    } catch (e) {
      _setError('Failed to load categories config: $e');
      _categoriesConfig = null;
    } finally {
      _setLoading(false);
    }
  }

  /// Create a package within an order
  Future<bool> createPackage(String orderUuid, CreatePackageRequest request) async {
    try {
      _setLoading(true);
      _clearError();

      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        _setError('Authentication required to create package');
        return false;
      }

      final response = await PackageService.createPackageInOrder(
        orderUuid,
        request,
        authToken: authToken,
      );
      
      if (response.success && response.data != null) {
        _currentPackage = response.data!;
        _clearPackageFormData(); // Clear form after successful creation
        return true;
      } else {
        _setError('Failed to create package: ${response.message}');
        return false;
      }
    } catch (e) {
      _setError('Failed to create package: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update a package within an order
  Future<bool> updatePackage(
    String orderUuid,
    String packageUuid,
    UpdatePackageRequest request,
  ) async {
    try {
      _setLoading(true);
      _clearError();

      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        _setError('Authentication required to update package');
        return false;
      }

      final response = await PackageService.updatePackageInOrder(
        orderUuid,
        packageUuid,
        request,
        authToken: authToken,
      );
      
      if (response.success && response.data != null) {
        _currentPackage = response.data!;
        return true;
      } else {
        _setError('Failed to update package: ${response.message}');
        return false;
      }
    } catch (e) {
      _setError('Failed to update package: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Load package details
  Future<bool> loadPackage(String orderUuid, String packageUuid) async {
    try {
      _setLoading(true);
      _clearError();

      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        _setError('Authentication required to load package');
        return false;
      }

      final response = await PackageService.getPackageDetails(
        orderUuid,
        packageUuid,
        authToken: authToken,
      );
      
      if (response.success && response.data != null) {
        _currentPackage = response.data!;
        return true;
      } else {
        _setError('Failed to load package: ${response.message}');
        return false;
      }
    } catch (e) {
      _setError('Failed to load package: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Update package form data
  void updatePackageFormData(String key, dynamic value) {
    _packageFormData[key] = value;
    notifyListeners();
  }

  /// Update multiple package form fields
  void updatePackageFormFields(Map<String, dynamic> fields) {
    _packageFormData.addAll(fields);
    notifyListeners();
  }

  /// Get package form field value
  T? getPackageFormField<T>(String key) {
    return _packageFormData[key] as T?;
  }

  /// Validate package form data
  Map<String, String> validatePackageForm() {
    final errors = <String, String>{};

    final quantity = _packageFormData['quantity'] as int?;
    if (quantity == null || quantity <= 0) {
      errors['quantity'] = 'Quantity must be greater than 0';
    }

    final categories = _packageFormData['categories'] as List<String>?;
    if (categories == null || categories.isEmpty) {
      errors['categories'] = 'At least one category must be selected';
    }

    final pickupAddress = _packageFormData['pickupAddress'] as String?;
    if (pickupAddress == null || pickupAddress.isEmpty) {
      errors['pickupAddress'] = 'Pickup address is required';
    }

    final deliveryAddress = _packageFormData['deliveryAddress'] as String?;
    if (deliveryAddress == null || deliveryAddress.isEmpty) {
      errors['deliveryAddress'] = 'Delivery address is required';
    }

    final pickupType = _packageFormData['pickupType'] as String?;
    if (pickupType == null || pickupType.isEmpty) {
      errors['pickupType'] = 'Pickup type is required';
    }

    // Validate weight if provided
    final weightValue = _packageFormData['weightValue'] as double?;
    final weightUnit = _packageFormData['weightUnit'] as String?;
    if (weightValue != null && (weightUnit == null || weightUnit.isEmpty)) {
      errors['weight'] = 'Weight unit is required when weight is specified';
    }
    if (weightValue != null && weightValue <= 0) {
      errors['weight'] = 'Weight must be greater than 0';
    }

    return errors;
  }

  /// Create package request from form data
  CreatePackageRequest? createPackageRequestFromForm() {
    final errors = validatePackageForm();
    if (errors.isNotEmpty) {
      _setError('Form validation failed: ${errors.values.first}');
      return null;
    }

    return PackageService.createPackageRequestFromForm(
      quantity: _packageFormData['quantity'] as int,
      categoryIds: (_packageFormData['categories'] as List<dynamic>).cast<String>(),
      label: _packageFormData['label'] as String?,
      detail: _packageFormData['detail'] as String?,
      fragile: _packageFormData['fragile'] as bool? ?? false,
      packagePickupType: _packageFormData['pickupType'] as String,
      packagePickupDate: _packageFormData['pickupDate'] as DateTime?,
      weightValue: _packageFormData['weightValue'] as double?,
      weightUnit: _packageFormData['weightUnit'] as String?,
      pickupAddressUuid: _packageFormData['pickupAddress'] as String,
      deliveryAddressUuid: _packageFormData['deliveryAddress'] as String,
    );
  }

  /// Get category by ID
  PackageCategory? getCategoryById(String id) {
    try {
      return _categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get categories by IDs
  List<PackageCategory> getCategoriesByIds(List<String> ids) {
    return _categories.where((category) => ids.contains(category.id)).toList();
  }

  /// Get package pickup types
  List<String> getPickupTypes() {
    return PackageService.getPackagePickupTypes();
  }

  /// Get pickup type display name
  String getPickupTypeDisplayName(String pickupType) {
    return PackageService.getPickupTypeDisplayName(pickupType);
  }

  /// Clear package form data
  void _clearPackageFormData() {
    _packageFormData.clear();
    notifyListeners();
  }

  /// Clear current package
  void clearCurrentPackage() {
    _currentPackage = null;
    notifyListeners();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    _error = null;
  }

  /// Clear all data
  void clearAll() {
    _categories = [];
    _categoriesConfig = null;
    _currentPackage = null;
    _packageFormData = {};
    _error = null;
    _isLoading = false;
    notifyListeners();
  }
}
