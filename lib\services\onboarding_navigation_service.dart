import 'package:flutter/material.dart';

/// Service class to manage onboarding navigation and page state
class OnboardingNavigationService extends ChangeNotifier {
  static const int totalPages = 3;
  
  int _currentPage = 0;
  PageController? _pageController;
  bool _isAnimating = false;

  /// Current page index (0-based)
  int get currentPage => _currentPage;
  
  /// Total number of onboarding pages
  int get totalPagesCount => totalPages;
  
  /// Check if we're on the first page
  bool get isFirstPage => _currentPage == 0;
  
  /// Check if we're on the last page
  bool get isLastPage => _currentPage == totalPages - 1;
  
  /// Check if animation is in progress
  bool get isAnimating => _isAnimating;
  
  /// Get the page controller
  PageController? get pageController => _pageController;

  /// Initialize the service with a PageController
  void initialize(PageController controller) {
    _pageController = controller;
  }

  /// Navigate to next page
  Future<void> nextPage() async {
    if (_isAnimating || isLastPage || _pageController == null) return;
    
    _isAnimating = true;
    notifyListeners();
    
    await _pageController!.nextPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
    
    _isAnimating = false;
    notifyListeners();
  }

  /// Navigate to previous page
  Future<void> previousPage() async {
    if (_isAnimating || isFirstPage || _pageController == null) return;
    
    _isAnimating = true;
    notifyListeners();
    
    await _pageController!.previousPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
    
    _isAnimating = false;
    notifyListeners();
  }

  /// Navigate to specific page
  Future<void> goToPage(int page) async {
    if (_isAnimating || page < 0 || page >= totalPages || _pageController == null) return;
    
    _isAnimating = true;
    notifyListeners();
    
    await _pageController!.animateToPage(
      page,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
    
    _isAnimating = false;
    notifyListeners();
  }

  /// Update current page (called by PageView)
  void updateCurrentPage(int page) {
    if (_currentPage != page) {
      _currentPage = page;
      notifyListeners();
    }
  }

  /// Get progress as a value between 0.0 and 1.0
  double get progress => _currentPage / (totalPages - 1);

  /// Get page indicator data
  List<bool> get pageIndicators {
    return List.generate(totalPages, (index) => index == _currentPage);
  }

  /// Handle swipe gestures
  void handleSwipe(DragEndDetails details) {
    if (_isAnimating) return;
    
    // Determine swipe direction based on velocity
    final velocity = details.primaryVelocity ?? 0;
    const threshold = 500; // Minimum velocity to trigger swipe
    
    if (velocity > threshold) {
      // Swiped right (go to previous page)
      previousPage();
    } else if (velocity < -threshold) {
      // Swiped left (go to next page)
      nextPage();
    }
  }

  /// Reset to first page
  void reset() {
    _currentPage = 0;
    _isAnimating = false;
    notifyListeners();
  }

  @override
  void dispose() {
    _pageController = null;
    super.dispose();
  }
}

/// Extension to add swipe gesture detection to any widget
extension SwipeGestureDetector on Widget {
  Widget addSwipeGesture(OnboardingNavigationService service) {
    return GestureDetector(
      onPanEnd: service.handleSwipe,
      child: this,
    );
  }
}

/// Custom page indicator widget
class OnboardingPageIndicator extends StatelessWidget {
  final OnboardingNavigationService service;
  final Color activeColor;
  final Color inactiveColor;
  final double size;
  final double spacing;

  const OnboardingPageIndicator({
    super.key,
    required this.service,
    this.activeColor = Colors.black,
    this.inactiveColor = Colors.grey,
    this.size = 8.0,
    this.spacing = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: service,
      builder: (context, child) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            service.totalPagesCount,
            (index) => Container(
              margin: EdgeInsets.symmetric(horizontal: spacing / 2),
              width: service.currentPage == index ? size * 3 : size,
              height: size,
              decoration: BoxDecoration(
                color: service.currentPage == index ? activeColor : inactiveColor,
                borderRadius: BorderRadius.circular(size / 2),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Progress bar widget for onboarding
class OnboardingProgressBar extends StatelessWidget {
  final OnboardingNavigationService service;
  final Color backgroundColor;
  final Color progressColor;
  final double height;

  const OnboardingProgressBar({
    super.key,
    required this.service,
    this.backgroundColor = Colors.grey,
    this.progressColor = Colors.blue,
    this.height = 4.0,
  });

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: service,
      builder: (context, child) {
        return LinearProgressIndicator(
          value: service.progress,
          backgroundColor: backgroundColor,
          valueColor: AlwaysStoppedAnimation<Color>(progressColor),
          minHeight: height,
        );
      },
    );
  }
}
