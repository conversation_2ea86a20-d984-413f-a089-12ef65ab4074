import 'package:flutter/material.dart';
import 'package:rideoon/services/package_data_service.dart';
import 'package:rideoon/views/custom_widgets/add_cargo.dart';
import 'package:rideoon/views/custom_widgets/payment_summary.dart';

/// Test file to demonstrate saving and retrieving shipment data
/// This file can be used to test the local storage functionality
class TestShipmentData {
  /// Create sample order data for testing
  static Future<void> createSampleOrder() async {
    try {
      // Sample pickup data
      await PackageDataService.savePickupData({
        'senderName': '<PERSON>',
        'fullAddress': '123 Main Street, Victoria Island',
        'landmark': 'Near Total Filling Station',
        'phone': '+234 ************',
        'state': 'Lagos',
        'latitude': 6.4281,
        'longitude': 3.4219,
      });

      // Sample receiver data
      await PackageDataService.saveReceiverData({
        'name': '<PERSON>',
        'address': '456 Broad Street, Wuse 2',
        'phone': '+234 ************',
        'state': 'Abuja',
        'latitude': 9.0579,
        'longitude': 7.4951,
      });

      // Sample package data
      await PackageDataService.savePackageData({
        'packageDetails': {
          'category': 'Electronics',
          'itemName': 'Smartphone',
          'description': 'Latest iPhone model',
        }
      });

      // Create sample order data
      final orderData = {
        'pickupData': await PackageDataService.getPickupData(),
        'receiverData': await PackageDataService.getReceiverData(),
        'packageData': await PackageDataService.getPackageData(),
        'cargoItems': [
          {
            'itemName': 'iPhone 15 Pro',
            'category': 'Electronics',
            'itemType': 'Mobile Phone',
            'weight': 0.2,
            'quantity': 1,
            'durability': 'Average',
            'imagePaths': [],
          },
          {
            'itemName': 'Phone Case',
            'category': 'Electronics',
            'itemType': 'Accessory',
            'weight': 0.1,
            'quantity': 1,
            'durability': 'Durable',
            'imagePaths': [],
          }
        ],
        'paymentData': {
          'shippingCost': 2500.0,
          'vat': 0.0,
          'insurance': 0.0,
          'pickupCharge': 0.0,
          'isInsuranceFree': true,
          'isPickupFree': true,
          'total': 2500.0,
        },
        'orderDate': DateTime.now().toIso8601String(),
        'trackingNumber': '#RO12345',
      };

      // Save as completed order
      await PackageDataService.saveCompletedOrder(orderData);

      // Also save as current shipment
      await PackageDataService.saveCurrentShipment(orderData);

      print('Sample order created successfully!');
      print('Tracking Number: ${orderData['trackingNumber']}');
      
    } catch (e) {
      print('Error creating sample order: $e');
    }
  }

  /// Create multiple sample orders for testing
  static Future<void> createMultipleSampleOrders() async {
    final orders = [
      {
        'title': 'Electronics Package',
        'category': 'Electronics',
        'items': ['Laptop', 'Mouse', 'Keyboard'],
        'cost': 3500.0,
        'status': 'pending',
      },
      {
        'title': 'Documents Envelope',
        'category': 'Documents',
        'items': ['Certificates', 'ID Cards'],
        'cost': 1500.0,
        'status': 'in_progress',
      },
      {
        'title': 'Fashion Items',
        'category': 'Clothing',
        'items': ['Dress', 'Shoes', 'Bag'],
        'cost': 2800.0,
        'status': 'completed',
      },
    ];

    for (int i = 0; i < orders.length; i++) {
      final order = orders[i];
      
      final orderData = {
        'pickupData': {
          'senderName': 'Sender ${i + 1}',
          'fullAddress': '${100 + i} Test Street, Lagos',
          'phone': '+234 80${i + 1} 000 000${i + 1}',
          'state': 'Lagos',
        },
        'receiverData': {
          'name': 'Receiver ${i + 1}',
          'address': '${200 + i} Delivery Avenue, Abuja',
          'phone': '+234 90${i + 1} 111 111${i + 1}',
          'state': 'Abuja',
        },
        'packageData': {
          'packageDetails': {
            'category': order['category'],
            'itemName': order['title'],
          }
        },
        'cargoItems': (order['items'] as List<String>).map((item) => {
          'itemName': item,
          'category': order['category'],
          'itemType': 'Package',
          'weight': 1.0,
          'quantity': 1,
          'durability': 'Average',
          'imagePaths': [],
        }).toList(),
        'paymentData': {
          'shippingCost': order['cost'],
          'vat': 0.0,
          'insurance': 0.0,
          'pickupCharge': 0.0,
          'isInsuranceFree': true,
          'isPickupFree': true,
          'total': order['cost'],
        },
        'orderDate': DateTime.now().subtract(Duration(days: i)).toIso8601String(),
        'trackingNumber': '#RO${10000 + i}',
        'status': order['status'],
      };

      await PackageDataService.saveCompletedOrder(orderData);
      
      if (order['status'] != 'completed') {
        await PackageDataService.saveCurrentShipment(orderData);
      }

      // Add small delay to ensure different timestamps
      await Future.delayed(Duration(milliseconds: 100));
    }

    print('Multiple sample orders created successfully!');
  }

  /// Print all stored shipment data for debugging
  static Future<void> printStoredData() async {
    try {
      print('\n=== CURRENT SHIPMENTS ===');
      final currentShipments = await PackageDataService.getCurrentShipments();
      for (int i = 0; i < currentShipments.length; i++) {
        final shipment = currentShipments[i];
        print('${i + 1}. ${shipment['trackingNumber']} - ${shipment['status']}');
        if (shipment['cargoItems'] != null) {
          final items = shipment['cargoItems'] as List;
          print('   Items: ${items.map((item) => item['itemName']).join(', ')}');
        }
      }

      print('\n=== COMPLETED ORDERS ===');
      final completedOrders = await PackageDataService.getCompletedOrders();
      for (int i = 0; i < completedOrders.length; i++) {
        final order = completedOrders[i];
        print('${i + 1}. ${order['trackingNumber']} - ${order['status']}');
        if (order['cargoItems'] != null) {
          final items = order['cargoItems'] as List;
          print('   Items: ${items.map((item) => item['itemName']).join(', ')}');
        }
      }

      print('\n=== SUMMARY ===');
      print('Current Shipments: ${currentShipments.length}');
      print('Completed Orders: ${completedOrders.length}');
      
    } catch (e) {
      print('Error printing stored data: $e');
    }
  }

  /// Clear all test data
  static Future<void> clearAllTestData() async {
    try {
      await PackageDataService.clearCurrentShipments();
      await PackageDataService.clearCompletedOrders();
      await PackageDataService.clearAllData();
      print('All test data cleared successfully!');
    } catch (e) {
      print('Error clearing test data: $e');
    }
  }

  /// Test analytics calculations
  static Future<void> testAnalytics() async {
    try {
      print('\n=== TESTING ANALYTICS ===');

      // Clear existing data first
      await clearAllTestData();

      // Create test orders with different statuses
      await createMultipleSampleOrders();

      // Load data for analytics calculation
      final currentShipments = await PackageDataService.getCurrentShipments();
      final completedOrders = await PackageDataService.getCompletedOrders();

      // Calculate analytics
      final pendingShipments = currentShipments.where((shipment) =>
        shipment['status'] == 'pending' || shipment['status'] == 'in_progress'
      ).toList();

      final completedShipments = completedOrders.where((order) =>
        order['status'] == 'completed'
      ).toList();

      double totalSpent = 0.0;
      for (final order in completedOrders) {
        if (order['paymentData'] != null) {
          final paymentData = order['paymentData'] as Map<String, dynamic>;
          totalSpent += (paymentData['total'] ?? 0.0).toDouble();
        }
      }

      print('Analytics Results:');
      print('- Packages Sent: ${completedOrders.length}');
      print('- Packages Received: ${completedShipments.length}');
      print('- Current Shipments: ${pendingShipments.length}');
      print('- Total Spent: ₦${totalSpent.toStringAsFixed(2)}');

      print('\nBreakdown by Status:');
      final statusCounts = <String, int>{};
      for (final shipment in currentShipments) {
        final status = shipment['status'] ?? 'unknown';
        statusCounts[status] = (statusCounts[status] ?? 0) + 1;
      }
      for (final order in completedOrders) {
        final status = order['status'] ?? 'unknown';
        statusCounts[status] = (statusCounts[status] ?? 0) + 1;
      }

      statusCounts.forEach((status, count) {
        print('- $status: $count');
      });

    } catch (e) {
      print('Error testing analytics: $e');
    }
  }
}
