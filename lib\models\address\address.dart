/// Address model for client address management
class Address {
  final String uuid;
  final DateTime created;
  final String name;
  final int phoneNumber;
  final String street;
  final String city;
  final String state;
  final String country;
  final double longitude;
  final double latitude;
  final String type;

  const Address({
    required this.uuid,
    required this.created,
    required this.name,
    required this.phoneNumber,
    required this.street,
    required this.city,
    required this.state,
    required this.country,
    required this.longitude,
    required this.latitude,
    required this.type,
  });

  /// Create Address from JSON
  factory Address.fromJson(Map<String, dynamic> json) {
    return Address(
      uuid: json['uuid'] as String,
      created: json['created'] != null
          ? DateTime.parse(json['created'] as String)
          : DateTime.now(), // Default to current time if not provided
      name: json['name'] as String,
      phoneNumber: _parsePhoneNumber(json['phoneNumber']),
      street: json['street'] as String,
      city: json['city'] as String,
      state: json['state'] as String,
      country: json['country'] as String,
      longitude: (json['longitude'] as num).toDouble(),
      latitude: (json['latitude'] as num).toDouble(),
      type: json['type'] as String,
    );
  }

  /// Helper method to parse phone number from various formats
  static int _parsePhoneNumber(dynamic phoneNumber) {
    if (phoneNumber is int) {
      return phoneNumber;
    } else if (phoneNumber is String) {
      // Remove all non-digit characters and parse as int
      final digitsOnly = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
      return int.tryParse(digitsOnly) ?? 0;
    }
    return 0;
  }

  /// Convert Address to JSON
  Map<String, dynamic> toJson() {
    return {
      'uuid': uuid,
      'created': created.toIso8601String(),
      'name': name,
      'phoneNumber': phoneNumber,
      'street': street,
      'city': city,
      'state': state,
      'country': country,
      'longitude': longitude,
      'latitude': latitude,
      'type': type,
    };
  }

  /// Create a copy of this address with updated fields
  Address copyWith({
    String? uuid,
    DateTime? created,
    String? name,
    int? phoneNumber,
    String? street,
    String? city,
    String? state,
    String? country,
    double? longitude,
    double? latitude,
    String? type,
  }) {
    return Address(
      uuid: uuid ?? this.uuid,
      created: created ?? this.created,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      street: street ?? this.street,
      city: city ?? this.city,
      state: state ?? this.state,
      country: country ?? this.country,
      longitude: longitude ?? this.longitude,
      latitude: latitude ?? this.latitude,
      type: type ?? this.type,
    );
  }

  @override
  String toString() {
    return 'Address(uuid: $uuid, name: $name, street: $street, city: $city, state: $state, country: $country)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Address && other.uuid == uuid;
  }

  @override
  int get hashCode => uuid.hashCode;

  /// Get formatted address string
  String get formattedAddress {
    return '$street, $city, $state, $country';
  }

  /// Get display name with phone number
  String get displayName {
    return '$name (+$phoneNumber)';
  }
}
