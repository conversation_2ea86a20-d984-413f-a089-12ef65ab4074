import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'package:rideoon/services/map_service.dart';
import 'package:rideoon/services/geocoding_service.dart';
import 'package:rideoon/services/package_data_service.dart';
import 'package:rideoon/providers/address_provider.dart';
import 'package:rideoon/providers/order_provider.dart';
import 'package:rideoon/providers/package_provider.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';

/// Map view for confirming location from address
class LocationConfirmationMap extends StatefulWidget {
  final String address;
  final String title;
  final LatLng? initialCoordinates;

  const LocationConfirmationMap({
    super.key,
    required this.address,
    required this.title,
    this.initialCoordinates,
  });

  @override
  State<LocationConfirmationMap> createState() => _LocationConfirmationMapState();
}

class _LocationConfirmationMapState extends State<LocationConfirmationMap>
    with TickerProviderStateMixin {
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  LatLng? _selectedLocation;
  String _currentAddress = '';
  GoogleMapController? _mapController;
  final Set<Marker> _markers = {};

  // Search functionality
  bool _showSearchOverlay = false;
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  List<Map<String, dynamic>> _searchResults = [];
  List<Map<String, dynamic>> _locationHistory = [];
  bool _isSearching = false;
  Timer? _searchDebounceTimer;

  // Bottom sheet functionality
  AnimationController? _bottomSheetController;
  Animation<double>? _bottomSheetAnimation;
  double _bottomSheetHeight = 120.0; // Initial slightly open height
  final double _maxBottomSheetHeight = 400.0;
  bool _isBottomSheetExpanded = false;

  CameraPosition _initialCameraPosition = const CameraPosition(
    target: LatLng(6.5244, 3.3792), // Lagos, Nigeria default
    zoom: 15.0,
  );

  @override
  void initState() {
    super.initState();
    _currentAddress = widget.address;

    // Initialize bottom sheet animation
    _bottomSheetController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _bottomSheetAnimation = Tween<double>(
      begin: _bottomSheetHeight,
      end: _maxBottomSheetHeight,
    ).animate(CurvedAnimation(
      parent: _bottomSheetController!,
      curve: Curves.easeInOut,
    ));

    _initializeMap();
    _loadLocationHistory();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    _bottomSheetController?.dispose();
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  Future<void> _initializeMap() async {
    try {
      await MapService.initialize();

      LatLng? coordinates = widget.initialCoordinates;

      print('🗺️ [LocationConfirmationMap] Initializing map...');
      print('   - Initial coordinates: $coordinates');
      print('   - Address: ${widget.address}');

      // Prioritize initial coordinates if provided
      if (coordinates != null) {
        print('✅ [LocationConfirmationMap] Using provided coordinates: ${coordinates.latitude}, ${coordinates.longitude}');
        _selectedLocation = coordinates;
        _initialCameraPosition = CameraPosition(
          target: coordinates,
          zoom: 16.0,
        );

        // Get address for the coordinates if current address is empty or generic
        if (_currentAddress.isEmpty || _currentAddress == widget.address) {
          try {
            final addressFromCoords = await GeocodingService.getAddressFromCoordinates(coordinates);
            if (addressFromCoords != null && addressFromCoords.isNotEmpty) {
              _currentAddress = addressFromCoords;
              print('📍 [LocationConfirmationMap] Address from coordinates: $addressFromCoords');
            }
          } catch (e) {
            print('⚠️ [LocationConfirmationMap] Could not get address from coordinates: $e');
          }
        }

        // Add marker for the location
        _addLocationMarker(coordinates);
      } else if (widget.address.isNotEmpty) {
        // If no initial coordinates provided, geocode the address
        print('🔍 [LocationConfirmationMap] Geocoding address: ${widget.address}');
        coordinates = await GeocodingService.getCoordinatesFromAddress(widget.address);

        if (coordinates != null) {
          print('✅ [LocationConfirmationMap] Geocoded coordinates: ${coordinates.latitude}, ${coordinates.longitude}');
          _selectedLocation = coordinates;
          _initialCameraPosition = CameraPosition(
            target: coordinates,
            zoom: 16.0,
          );
          _addLocationMarker(coordinates);
        }
      }

      // Fallback to current location if no coordinates found
      if (coordinates == null) {
        print('🔄 [LocationConfirmationMap] Using current location as fallback');
        final currentLocation = await MapService.getCurrentLocation();
        if (currentLocation != null &&
            currentLocation.latitude != null &&
            currentLocation.longitude != null) {
          final fallbackCoords = LatLng(currentLocation.latitude!, currentLocation.longitude!);
          _selectedLocation = fallbackCoords;
          _initialCameraPosition = CameraPosition(
            target: fallbackCoords,
            zoom: 16.0,
          );
          _addLocationMarker(fallbackCoords);
          print('📍 [LocationConfirmationMap] Using current location: ${fallbackCoords.latitude}, ${fallbackCoords.longitude}');
        }
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = false;
        });
        print('✅ [LocationConfirmationMap] Map initialized successfully');
      }
    } catch (e) {
      print('❌ [LocationConfirmationMap] Error initializing map: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = e.toString();
        });
      }
    }
  }

  void _addLocationMarker(LatLng position) {
    _markers.clear();
    _markers.add(
      Marker(
        markerId: const MarkerId('selected_location'),
        position: position,
        infoWindow: InfoWindow(
          title: widget.title,
          snippet: _currentAddress,
        ),
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
      ),
    );
  }

  Future<void> _onMapTap(LatLng position) async {
    // Immediately update the selected location and marker
    setState(() {
      _selectedLocation = position;
      _addLocationMarker(position);
    });

    // Update address based on new coordinates (async operation)
    try {
      final address = await GeocodingService.getAddressFromCoordinates(position);
      if (address != null && mounted) {
        setState(() {
          _currentAddress = address;
          _addLocationMarker(position); // Refresh marker with new address
        });
      }
    } catch (e) {
      // Handle geocoding errors gracefully
      if (mounted) {
        setState(() {
          _currentAddress = 'Selected location';
        });
      }
    }

    // Move camera to new position
    _mapController?.animateCamera(
      CameraUpdate.newLatLng(position),
    );
  }

  /// Load location history based on the type (pickup or receiver)
  Future<void> _loadLocationHistory() async {
    try {
      List<Map<String, dynamic>> history;
      if (widget.title.toLowerCase().contains('pickup')) {
        history = await PackageDataService.getPickupLocationHistory();
      } else {
        history = await PackageDataService.getReceiverLocationHistory();
      }

      setState(() {
        _locationHistory = history;
      });
    } catch (e) {
      print('Error loading location history: $e');
    }
  }

  /// Search for locations based on query with debouncing
  void _searchLocations(String query) {
    // Cancel previous timer
    _searchDebounceTimer?.cancel();

    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      return;
    }

    // Set loading state immediately for better UX
    setState(() {
      _isSearching = true;
    });

    // Debounce search requests
    _searchDebounceTimer = Timer(const Duration(milliseconds: 500), () async {
      try {
        final results = await GeocodingService.searchPlaces(query);
        if (mounted) {
          setState(() {
            _searchResults = results;
            _isSearching = false;
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _searchResults = [];
            _isSearching = false;
          });
        }
        print('Error searching locations: $e');
      }
    });
  }

  /// Select a location from search results or history
  Future<void> _selectLocation(Map<String, dynamic> locationData) async {
    final coordinates = LatLng(locationData['latitude'], locationData['longitude']);
    final address = locationData['address'];

    setState(() {
      _selectedLocation = coordinates;
      _currentAddress = address;
      _showSearchOverlay = false;
      _searchController.clear();
      _addLocationMarker(coordinates);
    });

    // Move camera to selected location
    _mapController?.animateCamera(
      CameraUpdate.newLatLng(coordinates),
    );

    // Save to location history
    await _saveToLocationHistory(locationData);
  }

  /// Save location to appropriate history
  Future<void> _saveToLocationHistory(Map<String, dynamic> locationData) async {
    try {
      if (widget.title.toLowerCase().contains('pickup')) {
        await PackageDataService.savePickupLocationToHistory(locationData);
      } else {
        await PackageDataService.saveReceiverLocationToHistory(locationData);
      }
      await _loadLocationHistory(); // Refresh history
    } catch (e) {
      print('Error saving to location history: $e');
    }
  }

  /// Toggle bottom sheet expansion
  void _toggleBottomSheet() {
    if (_bottomSheetController == null) return;

    setState(() {
      _isBottomSheetExpanded = !_isBottomSheetExpanded;
    });

    if (_isBottomSheetExpanded) {
      _bottomSheetController!.forward();
    } else {
      _bottomSheetController!.reverse();
    }
  }

  void _confirmLocation() {
    if (_selectedLocation != null) {
      // Ensure we have a valid address
      final addressToReturn = _currentAddress.isNotEmpty ? _currentAddress : widget.address;

      print('✅ [LocationConfirmationMap] Confirming location:');
      print('   - Coordinates: ${_selectedLocation!.latitude}, ${_selectedLocation!.longitude}');
      print('   - Address: $addressToReturn');

      final result = {
        'coordinates': _selectedLocation,
        'address': addressToReturn,
        'latitude': _selectedLocation!.latitude,
        'longitude': _selectedLocation!.longitude,
      };

      print('📤 [LocationConfirmationMap] Returning result: $result');
      Navigator.of(context).pop(result);
    } else {
      print('❌ [LocationConfirmationMap] No location selected');
    }
  }

  /// Build search content (results or history)
  Widget _buildSearchContent() {
    if (_isSearching) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: AppColors.primary,
              strokeWidth: 2,
            ),
            const SizedBox(height: 16),
            Text(
              'Searching...',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
                color: AppColors.black.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
      );
    }

    if (_searchController.text.isNotEmpty && _searchResults.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Search Results',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'Bricolage Grotesque',
              fontWeight: FontWeight.w600,
              color: AppColors.black,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              itemCount: _searchResults.length,
              itemBuilder: (context, index) {
                final result = _searchResults[index];
                return _buildLocationItem(
                  result,
                  Icons.location_on,
                  isSearchResult: true,
                );
              },
            ),
          ),
        ],
      );
    }

    if (_searchController.text.isNotEmpty && _searchResults.isEmpty && !_isSearching) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: AppColors.black.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'No results found',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'Bricolage Grotesque',
                fontWeight: FontWeight.w600,
                color: AppColors.black.withValues(alpha: 0.6),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try searching with different keywords',
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
                color: AppColors.black.withValues(alpha: 0.5),
              ),
            ),
          ],
        ),
      );
    }

    // Show location history when no search is active
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_locationHistory.isNotEmpty) ...[
          Text(
            'Recent Locations',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'Bricolage Grotesque',
              fontWeight: FontWeight.w600,
              color: AppColors.black,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              itemCount: _locationHistory.length,
              itemBuilder: (context, index) {
                final location = _locationHistory[index];
                return _buildLocationItem(
                  location,
                  Icons.history,
                  isSearchResult: false,
                );
              },
            ),
          ),
        ] else ...[
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.location_history,
                    size: 64,
                    color: AppColors.black.withValues(alpha: 0.3),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No recent locations',
                    style: TextStyle(
                      fontSize: 16,
                      fontFamily: 'Bricolage Grotesque',
                      fontWeight: FontWeight.w600,
                      color: AppColors.black.withValues(alpha: 0.6),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Your recent searches will appear here',
                    style: TextStyle(
                      fontSize: 14,
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      color: AppColors.black.withValues(alpha: 0.5),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// Build individual location item
  Widget _buildLocationItem(Map<String, dynamic> locationData, IconData icon, {required bool isSearchResult}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isSearchResult
                ? AppColors.primary.withValues(alpha: 0.1)
                : AppColors.black.withValues(alpha: 0.05),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            size: 20,
            color: isSearchResult
                ? AppColors.primary
                : AppColors.black.withValues(alpha: 0.6),
          ),
        ),
        title: Text(
          locationData['address'] ?? '',
          style: TextStyle(
            fontSize: 14,
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
            color: AppColors.black,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        onTap: () => _selectLocation(locationData),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Full screen map
          if (_isLoading)
            Container(
              color: const Color(0xFFF5F5FF),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: AppColors.primary,
                      strokeWidth: 3,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Loading location...',
                      style: TextStyle(
                        fontSize: 16,
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        color: AppColors.black.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            )
          else if (_hasError)
            Container(
              color: const Color(0xFFF5F5FF),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: AppColors.error,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Unable to load location',
                      style: TextStyle(
                        fontSize: 18,
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w600,
                        color: AppColors.black,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Please check your internet connection and try again.',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        color: AppColors.black.withValues(alpha: 0.6),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _isLoading = true;
                          _hasError = false;
                        });
                        _initializeMap();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: AppColors.white,
                      ),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            )
          else
            GoogleMap(
              initialCameraPosition: _initialCameraPosition,
              onMapCreated: (GoogleMapController controller) {
                _mapController = controller;
              },
              markers: _markers,
              onTap: _onMapTap,
              myLocationEnabled: true,
              myLocationButtonEnabled: false,
              zoomControlsEnabled: false,
              mapToolbarEnabled: false,
              compassEnabled: false,
              padding: EdgeInsets.only(
                bottom: _isBottomSheetExpanded ? _maxBottomSheetHeight : _bottomSheetHeight,
              ),
            ),

          // Back button (separate from search bar)
          Positioned(
            left: _getHorizontalPadding(context),
            top: MediaQuery.of(context).padding.top + _getSpacing(context, 16),
            child: GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                width: _getIconSize(context, 40),
                height: _getIconSize(context, 40),
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(_getBorderRadius(context, 20)),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.black.withValues(alpha: 0.2),
                      blurRadius: _getSpacing(context, 8),
                      offset: Offset(0, _getSpacing(context, 2)),
                    ),
                  ],
                ),
                child: Icon(
                  Icons.arrow_back,
                  color: AppColors.black.withValues(alpha: 0.8),
                  size: _getIconSize(context, 20),
                ),
              ),
            ),
          ),

          // Google Maps style search bar (separate)
          Positioned(
            left: _getHorizontalPadding(context) + _getIconSize(context, 40) + _getSpacing(context, 16),
            right: _getHorizontalPadding(context),
            top: MediaQuery.of(context).padding.top + _getSpacing(context, 16),
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _showSearchOverlay = true;
                });
                Future.delayed(const Duration(milliseconds: 100), () {
                  _searchFocusNode.requestFocus();
                });
              },
              child: Container(
                height: _getSpacing(context, 48),
                padding: EdgeInsets.symmetric(
                  horizontal: _getSpacing(context, 16),
                  vertical: _getSpacing(context, 12)
                ),
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(_getBorderRadius(context, 24)),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.black.withValues(alpha: 0.2),
                      blurRadius: _getSpacing(context, 8),
                      offset: Offset(0, _getSpacing(context, 2)),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.search,
                      color: AppColors.black.withValues(alpha: 0.6),
                      size: _getIconSize(context, 20),
                    ),
                    SizedBox(width: _getSpacing(context, 12)),
                    Expanded(
                      child: Text(
                        _currentAddress.isNotEmpty
                            ? _currentAddress
                            : 'Search here',
                        style: TextStyle(
                          fontSize: _getFontSize(context, 16),
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w400,
                          color: _currentAddress.isNotEmpty
                              ? AppColors.black.withValues(alpha: 0.8)
                              : AppColors.black.withValues(alpha: 0.6),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // My location button (Google Maps style)
          Positioned(
            right: 16,
            bottom: (_isBottomSheetExpanded ? _maxBottomSheetHeight : _bottomSheetHeight) + 16,
            child: Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(28),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.black.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                onPressed: () async {
                  final location = await MapService.getCurrentLocation();
                  if (location != null &&
                      location.latitude != null &&
                      location.longitude != null) {
                    final currentPos = LatLng(location.latitude!, location.longitude!);
                    await _onMapTap(currentPos);
                  }
                },
                icon: Icon(
                  Icons.my_location,
                  color: AppColors.black.withValues(alpha: 0.8),
                  size: 24,
                ),
              ),
            ),
          ),

          // Search overlay (Google Maps style)
          if (_showSearchOverlay)
            Positioned.fill(
              child: Material(
                color: AppColors.white,
                child: SafeArea(
                  child: Column(
                    children: [
                      // Search header with better styling
                      Container(
                        margin: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppColors.white,
                          borderRadius: BorderRadius.circular(28),
                          border: Border.all(
                            color: AppColors.black.withValues(alpha: 0.15),
                            width: 1.5,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.black.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            // Back button
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  _showSearchOverlay = false;
                                  _searchController.clear();
                                  _searchResults = [];
                                });
                                _searchFocusNode.unfocus();
                              },
                              child: Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(24),
                                ),
                                child: Icon(
                                  Icons.arrow_back,
                                  color: AppColors.black.withValues(alpha: 0.8),
                                  size: 24,
                                ),
                              ),
                            ),

                            // Search input
                            Expanded(
                              child: Container(
                                height: 48,
                                child: TextField(
                                  controller: _searchController,
                                  focusNode: _searchFocusNode,
                                  autofocus: true,
                                  onChanged: (value) {
                                    _searchLocations(value);
                                  },
                                  decoration: InputDecoration(
                                    hintText: 'Search for location...',
                                    hintStyle: TextStyle(
                                      color: AppColors.black.withValues(alpha: 0.5),
                                      fontSize: 16,
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w400,
                                    ),
                                    border: InputBorder.none,
                                    focusedBorder: InputBorder.none,
                                    enabledBorder: InputBorder.none,
                                    errorBorder: InputBorder.none,
                                    disabledBorder: InputBorder.none,
                                    contentPadding: const EdgeInsets.symmetric(vertical: 12),
                                  ),
                                  style: TextStyle(
                                    color: AppColors.black,
                                    fontSize: 16,
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                            ),

                            // Clear button
                            if (_searchController.text.isNotEmpty)
                              GestureDetector(
                                onTap: () {
                                  _searchController.clear();
                                  setState(() {
                                    _searchResults = [];
                                  });
                                },
                                child: Container(
                                  width: 48,
                                  height: 48,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(24),
                                  ),
                                  child: Icon(
                                    Icons.clear,
                                    color: AppColors.black.withValues(alpha: 0.6),
                                    size: 20,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),

                      // Search results and history
                      Expanded(
                        child: _buildSearchContent(),
                      ),
                    ],
                  ),
                ),
              ),
            ),

          // Google Maps style bottom sheet
          if (_bottomSheetAnimation != null)
            AnimatedBuilder(
              animation: _bottomSheetAnimation!,
              builder: (context, child) {
                return Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: GestureDetector(
                    onTap: _toggleBottomSheet,
                    child: Container(
                      height: _isBottomSheetExpanded
                          ? _bottomSheetAnimation!.value
                          : _bottomSheetHeight,
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.black.withValues(alpha: 0.2),
                          blurRadius: 16,
                          offset: const Offset(0, -4),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Handle bar and tappable header
                        GestureDetector(
                          onTap: _toggleBottomSheet,
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: Column(
                              children: [
                                Container(
                                  width: 40,
                                  height: 4,
                                  decoration: BoxDecoration(
                                    color: AppColors.black.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                ),
                                if (!_isBottomSheetExpanded) ...[
                                  const SizedBox(height: 8),
                                  Text(
                                    'Pull up to confirm location',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w400,
                                      color: AppColors.black.withValues(alpha: 0.5),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),

                        // Content
                        Expanded(
                          child: _isBottomSheetExpanded
                              ? _buildExpandedBottomSheet()
                              : _buildCollapsedBottomSheet(),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          )
          else
            // Fallback bottom sheet when animation is not ready
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: GestureDetector(
                onTap: _toggleBottomSheet,
                child: Container(
                  height: _bottomSheetHeight,
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.black.withValues(alpha: 0.2),
                        blurRadius: 16,
                        offset: const Offset(0, -4),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Handle bar
                      Container(
                        margin: const EdgeInsets.only(top: 8),
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: AppColors.black.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),

                      // Content
                      Expanded(
                        child: _buildCollapsedBottomSheet(),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Build collapsed bottom sheet (Google Maps "Latest in the area" style)
  Widget _buildCollapsedBottomSheet() {
    return GestureDetector(
      onTap: _toggleBottomSheet,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.location_on,
                color: AppColors.primary,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    widget.title,
                    style: TextStyle(
                      fontSize: 16,
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                      color: AppColors.black,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (_currentAddress.isNotEmpty) ...[
                    const SizedBox(height: 2),
                    Text(
                      _currentAddress,
                      style: TextStyle(
                        fontSize: 12,
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        color: AppColors.black.withValues(alpha: 0.6),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build expanded bottom sheet with confirm button
  Widget _buildExpandedBottomSheet() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Location info
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.location_on,
                    color: AppColors.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        widget.title,
                        style: TextStyle(
                          fontSize: 18,
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w600,
                          color: AppColors.black,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _currentAddress.isNotEmpty
                            ? _currentAddress
                            : 'Tap on the map to select location',
                        style: TextStyle(
                          fontSize: 14,
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w400,
                          color: AppColors.black.withValues(alpha: 0.6),
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Instruction text
            Text(
              'Tap on the map to adjust the location if needed',
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
                color: AppColors.black.withValues(alpha: 0.5),
              ),
            ),

            const SizedBox(height: 24),

            // Confirm button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _selectedLocation != null ? _confirmLocation : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: AppColors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  'Confirm Location',
                  style: TextStyle(
                    fontSize: 16,
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),

            // Safe area padding
            SizedBox(height: MediaQuery.of(context).padding.bottom + 16),
          ],
        ),
      ),
    );
  }

  // Responsive helper methods (matching home_view.dart patterns)
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 40; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet
    } else {
      spacing = baseSpacing; // Mobile
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2; // Tablet
    } else {
      fontSize = baseFontSize; // Mobile
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2; // Tablet
    } else {
      iconSize = baseIconSize; // Mobile
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2; // Tablet
    } else {
      borderRadius = baseBorderRadius; // Mobile
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }
}
