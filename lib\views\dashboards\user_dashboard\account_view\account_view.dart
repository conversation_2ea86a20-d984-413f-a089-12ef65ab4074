import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/push_notfication.dart';
import 'package:rideoon/views/authentication/userauth/sign_in.dart';
import 'package:rideoon/views/onboarding/user_type.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/providers/auth_provider.dart';
import 'package:rideoon/views/dashboards/user_dashboard/account_view/edit_profile_view.dart';
import 'package:rideoon/views/dashboards/user_dashboard/account_view/address_view.dart';
import 'package:rideoon/views/dashboards/user_dashboard/account_view/change_password_view.dart';
import 'package:rideoon/views/dashboards/user_dashboard/account_view/customer_support_view.dart';

/// Account view page for user dashboard
///
/// This page will contain user account management features,
/// including profile settings, preferences, and account information.
class AccountView extends StatefulWidget {
  /// Whether the parent dashboard is showing a header
  final bool hasHeader;

  const AccountView({super.key, this.hasHeader = false});

  @override
  State<AccountView> createState() => _AccountViewState();
}

class _AccountViewState extends State<AccountView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5FF),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Top spacing (adjust based on whether header is shown)
              SizedBox(height: widget.hasHeader
                ? MediaQuery.of(context).padding.top + 95 // Space for header + underline
                : MediaQuery.of(context).size.height * 0.07), // Original spacing

              // Header (only show if no parent header)
              if (!widget.hasHeader) _buildHeader(context),

              SizedBox(height: _getSpacing(context, 16)),

              // Subtitle (only show if no parent header)
              if (!widget.hasHeader) _buildSubtitle(context),

              SizedBox(height: _getSpacing(context, 32)),

              // Profile banner with stats
              _buildProfileBanner(context),

              SizedBox(height: _getSpacing(context, 32)),

              // Account options
              _buildAccountOptions(context),

              SizedBox(height: _getSpacing(context, 100)), // Extra space for bottom nav
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Text(
        'Account',
        style: TextStyle(
          fontSize: _getFontSize(context, 28),
          fontWeight: FontWeight.bold,
          fontFamily: 'Poppins',
          color: AppColors.black,
        ),
      ),
    );
  }

  Widget _buildSubtitle(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Text(
        'Manage your profile and account settings',
        style: TextStyle(
          fontSize: _getFontSize(context, 16),
          fontFamily: 'Poppins',
          color: AppColors.black.withValues(alpha: 0.6),
        ),
      ),
    );
  }

  Widget _buildProfileBanner(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(_getSpacing(context, 24)),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.primary,
              AppColors.primary.withValues(alpha: 0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 20)),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withValues(alpha: 0.3),
              blurRadius: _getSpacing(context, 20),
              offset: Offset(0, _getSpacing(context, 8)),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          children: [
            // Avatar and basic info
            Row(
              children: [
                Container(
                  width: _getAvatarSize(context),
                  height: _getAvatarSize(context),
                  decoration: BoxDecoration(
                    color: AppColors.white.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: AppColors.white,
                      width: 3,
                    ),
                  ),
                  child: Icon(
                    Icons.person,
                    size: _getAvatarSize(context) * 0.6,
                    color: AppColors.white,
                  ),
                ),
                SizedBox(width: _getSpacing(context, 16)),
                Expanded(
                  child: Consumer<AuthProvider>(
                    builder: (context, authProvider, child) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            authProvider.userFullName,
                            style: TextStyle(
                              fontSize: _getNameFontSize(context),
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Poppins',
                              color: AppColors.white,
                            ),
                          ),
                          SizedBox(height: _getSpacing(context, 4)),
                          Text(
                            'User ID: ${authProvider.currentUser?.uuid.substring(0, 8) ?? 'US001234'}',
                            style: TextStyle(
                              fontSize: _getSubtitleFontSize(context),
                              fontFamily: 'Poppins',
                              color: AppColors.white.withValues(alpha: 0.9),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ],
            ),

            SizedBox(height: _getSpacing(context, 20)),

            // Contact info
            Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                return Row(
                  children: [
                    Expanded(
                      child: _buildContactInfo(
                        context,
                        icon: Icons.email_outlined,
                        label: 'Email',
                        value: authProvider.userEmail,
                      ),
                    ),
                    SizedBox(width: _getSpacing(context, 16)),
                    Expanded(
                      child: _buildContactInfo(
                        context,
                        icon: Icons.phone_outlined,
                        label: 'Phone',
                        value: authProvider.userPhoneNumber,
                      ),
                    ),
                  ],
                );
              },
            ),

            SizedBox(height: _getSpacing(context, 24)),

            // User stats
            Container(
              padding: EdgeInsets.all(_getSpacing(context, 16)),
              decoration: BoxDecoration(
                color: AppColors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
                border: Border.all(
                  color: AppColors.white.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Text(
                    'Account Overview',
                    style: TextStyle(
                      fontSize: _getFontSize(context, 16),
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Poppins',
                      color: AppColors.white,
                    ),
                  ),
                  SizedBox(height: _getSpacing(context, 16)),
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatItem(
                          context,
                          icon: Icons.local_shipping,
                          title: 'Packages Sent',
                          value: '156',
                        ),
                      ),
                      Expanded(
                        child: _buildStatItem(
                          context,
                          icon: Icons.account_balance_wallet,
                          title: 'Spent',
                          value: '₦25,450',
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: _getSpacing(context, 12)),
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatItem(
                          context,
                          icon: Icons.schedule,
                          title: 'Member',
                          value: '2 years',
                        ),
                      ),
                      Expanded(
                        child: _buildStatItem(
                          context,
                          icon: Icons.inbox,
                          title: 'Packages Received',
                          value: '89',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfo(BuildContext context, {required IconData icon, required String label, required String value}) {
    return Container(
      padding: EdgeInsets.all(_getSpacing(context, 12)),
      decoration: BoxDecoration(
        color: AppColors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
        border: Border.all(
          color: AppColors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: _getIconSize(context, 16),
                color: AppColors.white.withValues(alpha: 0.8),
              ),
              SizedBox(width: _getSpacing(context, 6)),
              Text(
                label,
                style: TextStyle(
                  fontSize: _getFontSize(context, 12),
                  fontFamily: 'Poppins',
                  color: AppColors.white.withValues(alpha: 0.8),
                ),
              ),
            ],
          ),
          SizedBox(height: _getSpacing(context, 4)),
          Text(
            value,
            style: TextStyle(
              fontSize: _getFontSize(context, 13),
              fontWeight: FontWeight.w500,
              fontFamily: 'Poppins',
              color: AppColors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, {required IconData icon, required String title, required String value}) {
    return Column(
      children: [
        Icon(
          icon,
          size: _getIconSize(context, 24),
          color: AppColors.white.withValues(alpha: 0.9),
        ),
        SizedBox(height: _getSpacing(context, 8)),
        Text(
          value,
          style: TextStyle(
            fontSize: _getFontSize(context, 18),
            fontWeight: FontWeight.bold,
            fontFamily: 'Poppins',
            color: AppColors.white,
          ),
        ),
        SizedBox(height: _getSpacing(context, 2)),
        Text(
          title,
          style: TextStyle(
            fontSize: _getFontSize(context, 12),
            fontFamily: 'Poppins',
            color: AppColors.white.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }

  void _handleSignOut() {
    // Show logout confirmation dialog
    PushNotificationDialog.show(
      context,
      title: 'Sign Out Confirmation',
      description: 'You are trying to sign out of your user account. Do you want to register as a rider or go back to your user login screen?',
      declineButtonText: 'Register as Rider',
      acceptButtonText: 'Back to Login',
      icon: Icons.logout,
      iconBackgroundColor: AppColors.error,
      iconColor: AppColors.error,
      onDecline: () {
        // Navigate to user type selection
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const UserTypeSelection()),
          (route) => false,
        );
      },
      onAccept: () {
        // Navigate to user sign in
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const UserSignIn()),
          (route) => false,
        );
      },
    );
  }

  Widget _buildAccountOptions(BuildContext context) {
    final options = [
      {
        'icon': Icons.person_outline,
        'title': 'Edit Profile',
        'subtitle': 'Update your personal information',
        'route': 'edit_profile',
      },
      {
        'icon': Icons.location_on_outlined,
        'title': 'Address Management',
        'subtitle': 'Manage your delivery addresses',
        'route': 'address',
      },
      {
        'icon': Icons.security_outlined,
        'title': 'Change Password',
        'subtitle': 'Update your account password',
        'route': 'change_password',
      },
      {
        'icon': Icons.help_outline,
        'title': 'Customer Support',
        'subtitle': 'Get help and contact support',
        'route': 'customer_support',
      },
    ];

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Account Settings',
            style: TextStyle(
              fontSize: _getFontSize(context, 18),
              fontWeight: FontWeight.bold,
              fontFamily: 'Poppins',
              color: AppColors.black,
            ),
          ),
          SizedBox(height: _getSpacing(context, 16)),
          ...options.map((option) => _buildOptionItem(
            context,
            icon: option['icon'] as IconData,
            title: option['title'] as String,
            subtitle: option['subtitle'] as String,
            route: option['route'] as String,
          )),

          // Sign out option (separate styling)
          SizedBox(height: _getSpacing(context, 16)),
          _buildSignOutOption(context),
        ],
      ),
    );
  }

  Widget _buildOptionItem(BuildContext context, {required IconData icon, required String title, required String subtitle, required String route}) {
    return Padding(
      padding: EdgeInsets.only(bottom: _getSpacing(context, 12)),
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(
          horizontal: _getSpacing(context, 16),
          vertical: _getSpacing(context, 8),
        ),
        tileColor: AppColors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
          side: BorderSide(
            color: AppColors.black.withValues(alpha: 0.05),
            width: 1,
          ),
        ),
        leading: Container(
          width: _getIconContainerSize(context),
          height: _getIconContainerSize(context),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
          ),
          child: Icon(
            icon,
            size: _getIconSize(context, 20),
            color: AppColors.primary,
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: _getTitleFontSize(context),
            fontWeight: FontWeight.w500,
            fontFamily: 'Poppins',
            color: AppColors.black,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: _getSubtitleFontSize(context),
            fontFamily: 'Poppins',
            color: AppColors.black.withValues(alpha: 0.6),
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: _getArrowIconSize(context),
          color: AppColors.black.withValues(alpha: 0.4),
        ),
        onTap: () {
          _navigateToPage(context, route, title);
        },
      ),
    );
  }

  Widget _buildSignOutOption(BuildContext context) {
    return ListTile(
      contentPadding: EdgeInsets.symmetric(
        horizontal: _getSpacing(context, 16),
        vertical: _getSpacing(context, 8),
      ),
      tileColor: AppColors.error.withValues(alpha: 0.05),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
        side: BorderSide(
          color: AppColors.error.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      leading: Container(
        width: _getIconContainerSize(context),
        height: _getIconContainerSize(context),
        decoration: BoxDecoration(
          color: AppColors.error.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
        ),
        child: Icon(
          Icons.logout,
          size: _getIconSize(context, 20),
          color: AppColors.error,
        ),
      ),
      title: Text(
        'Sign Out',
        style: TextStyle(
          fontSize: _getTitleFontSize(context),
          fontWeight: FontWeight.w500,
          fontFamily: 'Poppins',
          color: AppColors.error,
        ),
      ),
      subtitle: Text(
        'Sign out of your account',
        style: TextStyle(
          fontSize: _getSubtitleFontSize(context),
          fontFamily: 'Poppins',
          color: AppColors.error.withValues(alpha: 0.7),
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: _getArrowIconSize(context),
        color: AppColors.error.withValues(alpha: 0.6),
      ),
      onTap: () => _handleSignOut(),
    );
  }

  void _navigateToPage(BuildContext context, String route, String title) {
    switch (route) {
      case 'edit_profile':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const EditProfileView(),
          ),
        );
        break;
      case 'address':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const AddressView(),
          ),
        );
        break;
      case 'change_password':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const ChangePasswordView(),
          ),
        );
        break;
      case 'customer_support':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const CustomerSupportView(),
          ),
        );
        break;
      default:
        Toast.info('$title functionality to be implemented');
    }
  }

  // Responsive helper methods (matching rider profile pattern)
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16;
    } else if (screenWidth > 600) {
      basePadding = 40;
    } else {
      basePadding = 24;
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8;
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2;
    } else {
      fontSize = baseFontSize;
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8;
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2;
    } else {
      iconSize = baseIconSize;
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6;
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2;
    } else {
      borderRadius = baseBorderRadius;
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }

  // Legacy helper methods for existing components
  double _getAvatarSize(BuildContext context) {
    return _getIconSize(context, 70);
  }

  double _getIconContainerSize(BuildContext context) {
    return _getIconSize(context, 40);
  }

  double _getNameFontSize(BuildContext context) {
    return _getFontSize(context, 20);
  }

  double _getSubtitleFontSize(BuildContext context) {
    return _getFontSize(context, 14);
  }

  double _getTitleFontSize(BuildContext context) {
    return _getFontSize(context, 16);
  }

  double _getArrowIconSize(BuildContext context) {
    return _getIconSize(context, 16);
  }
}
