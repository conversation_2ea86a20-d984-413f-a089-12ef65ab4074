/// Model representing user account information
class Account {
  final String uuid;
  final DateTime created;
  final DateTime updated;
  final String? avatar;
  final String firstName;
  final String lastName;
  final String phoneNumber;
  final String email;
  final String? address;
  final bool state;
  final bool secured;
  final bool verified;
  final String type; // "client" or "delivery"

  const Account({
    required this.uuid,
    required this.created,
    required this.updated,
    this.avatar,
    required this.firstName,
    required this.lastName,
    required this.phoneNumber,
    required this.email,
    this.address,
    required this.state,
    required this.secured,
    required this.verified,
    required this.type,
  });

  /// Get the full name of the user
  String get fullName => '$firstName $lastName';

  /// Check if the user is a client
  bool get isClient => type.toLowerCase() == 'client';

  /// Check if the user is a delivery partner/rider
  bool get isRider => type.toLowerCase() == 'delivery';

  /// Create an Account from JSON data
  factory Account.fromJson(Map<String, dynamic> json) {
    return Account(
      uuid: json['uuid'] as String,
      created: DateTime.parse(json['created'] as String),
      updated: DateTime.parse(json['updated'] as String),
      avatar: json['avatar'] as String?,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      phoneNumber: json['phoneNumber'].toString(), // Handle both string and number
      email: json['email'] as String,
      address: json['address'] as String?,
      state: json['state'] as bool,
      secured: json['secured'] as bool,
      verified: json['verified'] as bool,
      type: json['type'] as String,
    );
  }

  /// Convert the account to JSON format
  Map<String, dynamic> toJson() {
    return {
      'uuid': uuid,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
      if (avatar != null) 'avatar': avatar,
      'firstName': firstName,
      'lastName': lastName,
      'phoneNumber': phoneNumber,
      'email': email,
      if (address != null) 'address': address,
      'state': state,
      'secured': secured,
      'verified': verified,
      'type': type,
    };
  }

  /// Create a copy of this account with some fields updated
  Account copyWith({
    String? uuid,
    DateTime? created,
    DateTime? updated,
    String? avatar,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? email,
    String? address,
    bool? state,
    bool? secured,
    bool? verified,
    String? type,
  }) {
    return Account(
      uuid: uuid ?? this.uuid,
      created: created ?? this.created,
      updated: updated ?? this.updated,
      avatar: avatar ?? this.avatar,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      address: address ?? this.address,
      state: state ?? this.state,
      secured: secured ?? this.secured,
      verified: verified ?? this.verified,
      type: type ?? this.type,
    );
  }

  @override
  String toString() {
    return 'Account(uuid: $uuid, fullName: $fullName, email: $email, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Account &&
        other.uuid == uuid &&
        other.email == email &&
        other.type == type;
  }

  @override
  int get hashCode {
    return Object.hash(uuid, email, type);
  }
}
