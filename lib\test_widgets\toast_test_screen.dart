import 'package:flutter/material.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';

/// Test screen to verify toast functionality
/// 
/// This screen provides buttons to test all toast types and ensure
/// the toast system is working correctly throughout the app.
class ToastTestScreen extends StatelessWidget {
  const ToastTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Toast Test Screen'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Test Toast System',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                fontFamily: 'Poppins',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            
            // Success Toast Test
            ElevatedButton.icon(
              onPressed: () {
                Toast.success('✅ Success toast is working perfectly!');
              },
              icon: const Icon(Icons.check_circle),
              label: const Text('Test Success Toast'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
                foregroundColor: AppColors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            const SizedBox(height: 16),
            
            // Error Toast Test
            ElevatedButton.icon(
              onPressed: () {
                Toast.error('❌ Error toast is working correctly!');
              },
              icon: const Icon(Icons.error),
              label: const Text('Test Error Toast'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: AppColors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            const SizedBox(height: 16),
            
            // Warning Toast Test
            ElevatedButton.icon(
              onPressed: () {
                Toast.warning('⚠️ Warning toast is functioning properly!');
              },
              icon: const Icon(Icons.warning),
              label: const Text('Test Warning Toast'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.warning,
                foregroundColor: AppColors.black,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            const SizedBox(height: 16),
            
            // Info Toast Test
            ElevatedButton.icon(
              onPressed: () {
                Toast.info('ℹ️ Info toast is working as expected!');
              },
              icon: const Icon(Icons.info),
              label: const Text('Test Info Toast'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            const SizedBox(height: 16),
            
            // Custom Toast Test
            ElevatedButton.icon(
              onPressed: () {
                Toast.custom(
                  message: '🚴‍♂️ Custom toast with rider icon is working!',
                  backgroundColor: AppColors.accept,
                  icon: Icons.directions_bike,
                  duration: const Duration(seconds: 4),
                );
              },
              icon: const Icon(Icons.directions_bike),
              label: const Text('Test Custom Toast'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.accept,
                foregroundColor: AppColors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            const SizedBox(height: 32),
            
            // Multiple Toasts Test
            ElevatedButton.icon(
              onPressed: () {
                Toast.info('First toast message');
                Future.delayed(const Duration(milliseconds: 500), () {
                  Toast.success('Second toast message');
                });
                Future.delayed(const Duration(milliseconds: 1000), () {
                  Toast.warning('Third toast message');
                });
              },
              icon: const Icon(Icons.layers),
              label: const Text('Test Multiple Toasts'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.rating,
                foregroundColor: AppColors.black,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            const SizedBox(height: 16),
            
            // Clear All Toasts
            OutlinedButton.icon(
              onPressed: () {
                Toast.removeAll();
              },
              icon: const Icon(Icons.clear_all),
              label: const Text('Clear All Toasts'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.error,
                side: const BorderSide(color: AppColors.error),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            
            const Spacer(),
            
            // Instructions
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Instructions:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      fontFamily: 'Poppins',
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• Tap any button to test the corresponding toast type\n'
                    '• Toasts appear at the top of the screen\n'
                    '• They auto-dismiss after a few seconds\n'
                    '• You can manually close them by tapping the X\n'
                    '• Multiple toasts can be shown simultaneously',
                    style: TextStyle(
                      fontSize: 14,
                      fontFamily: 'Poppins',
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
