import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/services/package_data_service.dart';

/// Instant Deliveries view page for rider dashboard
///
/// This page contains instant delivery management features,
/// including available instant deliveries and active instant deliveries.
class DeliveriesView extends StatefulWidget {
  const DeliveriesView({super.key});

  @override
  State<DeliveriesView> createState() => _DeliveriesViewState();
}

class _DeliveriesViewState extends State<DeliveriesView> with SingleTickerProviderStateMixin {
  TabController? _tabController;

  List<Map<String, dynamic>> _availableDeliveries = [];
  List<Map<String, dynamic>> _activeDeliveries = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadInstantDeliveries();
  }

  /// Load instant deliveries from saved checkout data
  Future<void> _loadInstantDeliveries() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Load completed orders from PackageDataService
      final completedOrders = await PackageDataService.getCompletedOrders();

      // Filter for instant deliveries only
      final instantDeliveries = completedOrders.where((order) {
        final packageData = order['packageData'] as Map<String, dynamic>?;
        final deliveryType = packageData?['deliveryType'] ?? 'instant';
        return deliveryType == 'instant';
      }).toList();

      // Convert to delivery format and categorize
      final availableDeliveries = <Map<String, dynamic>>[];
      final activeDeliveries = <Map<String, dynamic>>[];

      for (final order in instantDeliveries) {
        final deliveryData = _convertOrderToDelivery(order);

        // Categorize based on status
        final status = order['status'] ?? 'pending';
        if (status == 'pending' || status == 'available') {
          availableDeliveries.add(deliveryData);
        } else if (status == 'in_progress' || status == 'accepted' || status == 'picked_up') {
          activeDeliveries.add({
            ...deliveryData,
            'status': status,
            'acceptedTime': _formatTimestamp(order['timestamp']),
          });
        }
      }

      setState(() {
        _availableDeliveries = availableDeliveries;
        _activeDeliveries = activeDeliveries;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading instant deliveries: $e');
      setState(() {
        _availableDeliveries = [];
        _activeDeliveries = [];
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5FF),
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 7% gap from top of screen
            SizedBox(height: MediaQuery.of(context).size.height * 0.07),

            // Header
            _buildHeader(context),

            SizedBox(height: _getSpacing(context, 16)),

            // Subtitle
            _buildSubtitle(context),

            SizedBox(height: _getSpacing(context, 24)),

            // Tab bar
            _buildTabBar(context),

            SizedBox(height: _getSpacing(context, 16)),

            // Tab views
            Expanded(
              child: TabBarView(
                controller: _tabController!,
                children: [
                  _buildAvailableTab(context),
                  _buildActiveTab(context),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Center(
      child: Text(
        'Instant Deliveries',
        style: TextStyle(
          color: const Color(0xFF414141),
          fontSize: _getFontSize(context, 20),
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildSubtitle(BuildContext context) {
    return Center(
      child: Text(
        'Manage your instant delivery tasks',
        style: TextStyle(
          color: const Color(0xFF9A9A9A),
          fontSize: _getFontSize(context, 10),
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w500,
          letterSpacing: 0.20,
        ),
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      decoration: BoxDecoration(
        color: AppColors.black.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
      ),
      child: TabBar(
        controller: _tabController!,
        labelColor: AppColors.white,
        unselectedLabelColor: AppColors.black.withValues(alpha: 0.7),
        labelStyle: TextStyle(
          fontSize: _getFontSize(context, 13),
          fontWeight: FontWeight.w500,
          fontFamily: 'Poppins',
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: _getFontSize(context, 13),
          fontWeight: FontWeight.w400,
          fontFamily: 'Poppins',
        ),
        indicator: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        tabs: const [
          Tab(text: 'Available'),
          Tab(text: 'Active'),
        ],
      ),
    );
  }

  Widget _buildAvailableTab(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingState(context);
    }

    if (_availableDeliveries.isEmpty) {
      return _buildEmptyState(context, 'No Available Instant Deliveries', 'Check back later for new delivery opportunities');
    }

    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
        child: Column(
          children: _availableDeliveries.map((delivery) =>
            Padding(
              padding: EdgeInsets.only(bottom: _getSpacing(context, 12)),
              child: _buildAvailableDeliveryCard(context, delivery),
            ),
          ).toList(),
        ),
      ),
    );
  }

  Widget _buildActiveTab(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingState(context);
    }

    if (_activeDeliveries.isEmpty) {
      return _buildEmptyState(context, 'No Active Instant Deliveries', 'Accept available deliveries to see them here');
    }

    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
        child: Column(
          children: _activeDeliveries.map((delivery) =>
            Padding(
              padding: EdgeInsets.only(bottom: _getSpacing(context, 12)),
              child: _buildActiveDeliveryCard(context, delivery),
            ),
          ).toList(),
        ),
      ),
    );
  }

  Widget _buildAvailableDeliveryCard(BuildContext context, Map<String, dynamic> delivery) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 20)),
      decoration: ShapeDecoration(
        color: const Color(0xFFFEFEFE),
        shape: RoundedRectangleBorder(
          side: BorderSide(
            width: 1,
            color: AppColors.black.withValues(alpha: 0.07),
          ),
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with package type and amount
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Package type badge
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: _getSpacing(context, 8),
                  vertical: _getSpacing(context, 4),
                ),
                decoration: ShapeDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(_getBorderRadius(context, 10)),
                  ),
                ),
                child: Text(
                  delivery['packageType'],
                  style: TextStyle(
                    color: AppColors.primary,
                    fontSize: _getFontSize(context, 10),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              // Amount
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: _getSpacing(context, 8),
                  vertical: _getSpacing(context, 4),
                ),
                decoration: ShapeDecoration(
                  color: const Color(0x1C009951),
                  shape: RoundedRectangleBorder(
                    side: BorderSide(
                      width: 1,
                      color: AppColors.black.withValues(alpha: 0.03),
                    ),
                    borderRadius: BorderRadius.circular(_getBorderRadius(context, 10)),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.account_balance_wallet,
                      size: _getIconSize(context, 10),
                      color: const Color(0xFF14AE5C),
                    ),
                    SizedBox(width: _getSpacing(context, 4)),
                    Text(
                      '₦${delivery['amount']}',
                      style: TextStyle(
                        color: const Color(0xFF14AE5C),
                        fontSize: _getFontSize(context, 10),
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                        height: 1.60,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: _getSpacing(context, 20)),

          // Pickup location component
          _buildLocationComponent(
            context,
            icon: Icons.location_on,
            iconColor: const Color(0xFF9713E7),
            title: 'Pickup Location',
            location: delivery['pickupLocation'],
            address: delivery['pickupAddress'],
            isCompleted: false,
            isLast: false,
          ),

          SizedBox(height: _getSpacing(context, 20)),

          // Delivery location component
          _buildLocationComponent(
            context,
            icon: Icons.flag,
            iconColor: const Color(0xFF14AE5C),
            title: 'Delivery Location',
            location: delivery['deliveryLocation'],
            address: delivery['deliveryAddress'],
            isCompleted: false,
            isLast: true,
          ),

          SizedBox(height: _getSpacing(context, 16)),

          // Distance and action button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Distance: ${delivery['distance']}',
                style: TextStyle(
                  color: const Color(0xFF9A9A9A),
                  fontSize: _getFontSize(context, 10),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  // TODO: Implement accept delivery
                  Toast.success('Delivery accepted! You will be notified when pickup is ready.');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    horizontal: _getSpacing(context, 16),
                    vertical: _getSpacing(context, 8),
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  'Accept',
                  style: TextStyle(
                    fontSize: _getFontSize(context, 12),
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Poppins',
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActiveDeliveryCard(BuildContext context, Map<String, dynamic> delivery) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 20)),
      decoration: ShapeDecoration(
        color: const Color(0xFFFEFEFE),
        shape: RoundedRectangleBorder(
          side: BorderSide(
            width: 1,
            color: AppColors.black.withValues(alpha: 0.07),
          ),
          borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with package type and amount
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Package type badge
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: _getSpacing(context, 8),
                  vertical: _getSpacing(context, 4),
                ),
                decoration: ShapeDecoration(
                  color: AppColors.warning.withValues(alpha: 0.1),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(_getBorderRadius(context, 10)),
                  ),
                ),
                child: Text(
                  delivery['packageType'],
                  style: TextStyle(
                    color: AppColors.warning,
                    fontSize: _getFontSize(context, 10),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              // Amount
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: _getSpacing(context, 8),
                  vertical: _getSpacing(context, 4),
                ),
                decoration: ShapeDecoration(
                  color: const Color(0x1C009951),
                  shape: RoundedRectangleBorder(
                    side: BorderSide(
                      width: 1,
                      color: AppColors.black.withValues(alpha: 0.03),
                    ),
                    borderRadius: BorderRadius.circular(_getBorderRadius(context, 10)),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.account_balance_wallet,
                      size: _getIconSize(context, 10),
                      color: const Color(0xFF14AE5C),
                    ),
                    SizedBox(width: _getSpacing(context, 4)),
                    Text(
                      '₦${delivery['amount']}',
                      style: TextStyle(
                        color: const Color(0xFF14AE5C),
                        fontSize: _getFontSize(context, 10),
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w500,
                        height: 1.60,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: _getSpacing(context, 20)),

          // Pickup location component (completed)
          _buildLocationComponent(
            context,
            icon: Icons.location_on,
            iconColor: const Color(0xFF9713E7),
            title: 'Pickup Location',
            location: delivery['pickupLocation'],
            address: delivery['pickupAddress'],
            isCompleted: delivery['status'] == 'picked_up' || delivery['status'] == 'en_route',
            isLast: false,
          ),

          SizedBox(height: _getSpacing(context, 20)),

          // Delivery location component
          _buildLocationComponent(
            context,
            icon: Icons.flag,
            iconColor: const Color(0xFF14AE5C),
            title: 'Delivery Location',
            location: delivery['deliveryLocation'],
            address: delivery['deliveryAddress'],
            isCompleted: false,
            isLast: true,
          ),

          SizedBox(height: _getSpacing(context, 16)),

          // Status and accepted time
          Container(
            padding: EdgeInsets.all(_getSpacing(context, 12)),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
            ),
            child: Row(
              children: [
                Icon(
                  delivery['status'] == 'picked_up' ? Icons.inventory : Icons.local_shipping,
                  size: _getIconSize(context, 16),
                  color: AppColors.warning,
                ),
                SizedBox(width: _getSpacing(context, 8)),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        delivery['status'] == 'picked_up' ? 'Package Picked Up' : 'En Route to Delivery',
                        style: TextStyle(
                          color: AppColors.warning,
                          fontSize: _getFontSize(context, 12),
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        'Accepted: ${delivery['acceptedTime']}',
                        style: TextStyle(
                          color: AppColors.black.withValues(alpha: 0.6),
                          fontSize: _getFontSize(context, 10),
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: _getSpacing(context, 16)),

          // Distance and action button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Distance: ${delivery['distance']}',
                style: TextStyle(
                  color: const Color(0xFF9A9A9A),
                  fontSize: _getFontSize(context, 10),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  // TODO: Implement delivery completion
                  if (delivery['status'] == 'picked_up') {
                    Toast.success('Delivery marked as completed! Payment will be processed.');
                  } else {
                    Toast.info('Status updated successfully.');
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.warning,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    horizontal: _getSpacing(context, 16),
                    vertical: _getSpacing(context, 8),
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  delivery['status'] == 'picked_up' ? 'Mark Delivered' : 'Update Status',
                  style: TextStyle(
                    fontSize: _getFontSize(context, 12),
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Poppins',
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLocationComponent(
    BuildContext context, {
    required IconData icon,
    required Color iconColor,
    required String title,
    required String location,
    required String address,
    required bool isCompleted,
    required bool isLast,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: _getIconSize(context, 19),
              height: _getIconSize(context, 19),
              decoration: BoxDecoration(
                color: const Color(0x23A9A9A9),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Container(
                  width: _getIconSize(context, 9),
                  height: _getIconSize(context, 9),
                  decoration: BoxDecoration(
                    color: isCompleted ? iconColor : const Color(0xFFA5A5A5),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: _getSpacing(context, 40),
                color: AppColors.black.withValues(alpha: 0.1),
              ),
          ],
        ),
        SizedBox(width: _getSpacing(context, 16)),
        // Content
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: isCompleted
                      ? AppColors.black.withValues(alpha: 0.4)
                      : const Color(0xFFA5A5A5),
                  fontSize: _getFontSize(context, 10),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                  height: 0.95,
                ),
              ),
              SizedBox(height: _getSpacing(context, 4)),
              Text(
                location,
                style: TextStyle(
                  color: const Color(0xFF111111),
                  fontSize: _getFontSize(context, 12),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w600,
                  height: 0.95,
                ),
              ),
              SizedBox(height: _getSpacing(context, 8)),
              Text(
                address,
                style: TextStyle(
                  color: AppColors.black,
                  fontSize: _getFontSize(context, 10),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                  height: 0.95,
                  letterSpacing: 0.20,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }



  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 40; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet
    } else {
      spacing = baseSpacing; // Mobile
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2; // Tablet
    } else {
      fontSize = baseFontSize; // Mobile
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2; // Tablet
    } else {
      iconSize = baseIconSize; // Mobile
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2; // Tablet
    } else {
      borderRadius = baseBorderRadius; // Mobile
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }

  /// Convert order data to delivery format
  Map<String, dynamic> _convertOrderToDelivery(Map<String, dynamic> order) {
    final pickupData = order['pickupData'] as Map<String, dynamic>? ?? {};
    final receiverData = order['receiverData'] as Map<String, dynamic>? ?? {};
    final paymentData = order['paymentData'] as Map<String, dynamic>? ?? {};
    final cargoItems = order['cargoItems'] as List? ?? [];

    // Get package type from cargo items or package data
    String packageType = 'Package';
    if (cargoItems.isNotEmpty) {
      packageType = cargoItems.first['category'] ?? 'Package';
    } else if (order['packageData'] != null) {
      final packageData = order['packageData'] as Map<String, dynamic>;
      if (packageData['packageDetails'] != null) {
        final packageDetails = packageData['packageDetails'] as Map<String, dynamic>;
        packageType = packageDetails['category'] ?? 'Package';
      }
    }

    return {
      'id': order['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
      'pickupLocation': _getLocationName(pickupData),
      'pickupAddress': pickupData['fullAddress'] ?? 'No address provided',
      'deliveryLocation': _getLocationName(receiverData),
      'deliveryAddress': receiverData['address'] ?? 'No address provided',
      'amount': (paymentData['total'] ?? 0.0).toStringAsFixed(0),
      'distance': _calculateDistance(pickupData, receiverData),
      'packageType': packageType,
      'trackingNumber': order['trackingNumber'] ?? '#RO00000',
      'orderData': order, // Keep original order data for reference
    };
  }

  /// Get location name from address data
  String _getLocationName(Map<String, dynamic> addressData) {
    if (addressData['senderName'] != null) {
      return '${addressData['senderName']}, ${addressData['state'] ?? 'Unknown'}';
    } else if (addressData['name'] != null) {
      return '${addressData['name']}, ${addressData['state'] ?? 'Unknown'}';
    } else {
      return addressData['state'] ?? 'Unknown Location';
    }
  }

  /// Calculate approximate distance (placeholder implementation)
  String _calculateDistance(Map<String, dynamic> pickup, Map<String, dynamic> delivery) {
    // This is a placeholder - in a real app, you'd use actual coordinates
    // to calculate distance using Google Maps API or similar
    return '${(2.0 + (pickup.hashCode % 10)).toStringAsFixed(1)} km';
  }

  /// Format timestamp for display
  String? _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return null;

    try {
      final dateTime = timestamp is int
          ? DateTime.fromMillisecondsSinceEpoch(timestamp)
          : DateTime.parse(timestamp.toString());

      return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return null;
    }
  }

  /// Build loading state
  Widget _buildLoadingState(BuildContext context) {
    return Center(
      child: CircularProgressIndicator(
        color: AppColors.primary,
      ),
    );
  }

  /// Build empty state
  Widget _buildEmptyState(BuildContext context, String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_shipping_outlined,
            size: 64,
            color: AppColors.black.withValues(alpha: 0.3),
          ),
          SizedBox(height: _getSpacing(context, 16)),
          Text(
            title,
            style: TextStyle(
              fontSize: _getFontSize(context, 18),
              fontWeight: FontWeight.w600,
              fontFamily: 'Poppins',
              color: AppColors.black.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: _getSpacing(context, 8)),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: _getFontSize(context, 14),
              fontFamily: 'Poppins',
              color: AppColors.black.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
