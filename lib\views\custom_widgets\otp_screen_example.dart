import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/otp_screen.dart';

/// Example usage of the OTPVerificationScreen widget
///
/// This demonstrates how to integrate the OTP verification screen
/// into your authentication flow with proper callbacks and navigation.
class OTPScreenExample extends StatefulWidget {
  const OTPScreenExample({super.key});

  @override
  State<OTPScreenExample> createState() => _OTPScreenExampleState();
}

class _OTPScreenExampleState extends State<OTPScreenExample> {
  String _selectedMethod = 'email';
  String _contactInfo = '<EMAIL>';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('OTP Screen Examples'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'OTP Verification Examples',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.black,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Choose a verification method to test the OTP screen:',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.black,
              ),
            ),
            const SizedBox(height: 32),

            // Email verification example
            ElevatedButton(
              onPressed: () => _showOTPScreen('email', '<EMAIL>'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Email Verification (<EMAIL>)',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ),

            const SizedBox(height: 16),

            // SMS verification example
            ElevatedButton(
              onPressed: () => _showOTPScreen('sms', '+234 8134 400 500'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
                foregroundColor: AppColors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'SMS Verification (+234 8134 400 500)',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ),

            const SizedBox(height: 32),

            // Instructions
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.primary.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: AppColors.primary,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'Demo Instructions',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    '• Enter "1234" as the OTP code to simulate successful verification\n'
                    '• Any other code will show an error message\n'
                    '• Use the resend button after the countdown expires\n'
                    '• Test responsive design on different screen sizes',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.black,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showOTPScreen(String method, String contact) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OTPVerificationScreen(
          verificationMethod: method,
          contactInfo: contact,
          onVerificationSuccess: (otp) {
            // Handle successful verification
            _handleVerificationSuccess(otp, method, contact);
          },
          onResendOTP: () {
            // Handle resend OTP request
            _handleResendOTP(method, contact);
          },
          onBack: () {
            // Handle back navigation
            Navigator.of(context).pop();
          },
        ),
      ),
    );
  }

  void _handleVerificationSuccess(String otp, String method, String contact) {
    // Show success message and navigate back
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Verification successful! OTP: $otp\nMethod: $method\nContact: $contact',
        ),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 3),
      ),
    );

    // Navigate back to example screen
    Navigator.of(context).pop();

    // In a real app, you would navigate to the next screen in your flow
    // For example: Navigator.pushReplacement(...) to main app screen
  }

  void _handleResendOTP(String method, String contact) {
    // Simulate resending OTP
    print('Resending OTP via $method to $contact');
    
    // In a real app, you would make an API call here
    // Example:
    // await authService.resendOTP(method: method, contact: contact);
  }
}

/// Integration example showing how to use OTP screen in forgot password flow
class ForgotPasswordOTPExample extends StatelessWidget {
  final String recoveryMethod;
  final String contactInfo;

  const ForgotPasswordOTPExample({
    super.key,
    required this.recoveryMethod,
    required this.contactInfo,
  });

  @override
  Widget build(BuildContext context) {
    return OTPVerificationScreen(
      verificationMethod: recoveryMethod,
      contactInfo: contactInfo,
      onVerificationSuccess: (otp) {
        // Navigate to reset password screen
        _navigateToResetPassword(context, otp);
      },
      onResendOTP: () {
        // Resend OTP for password recovery
        _resendPasswordRecoveryOTP(context);
      },
      onBack: () {
        // Go back to forgot password method selection
        Navigator.of(context).pop();
      },
    );
  }

  void _navigateToResetPassword(BuildContext context, String otp) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('OTP verified! Navigate to reset password screen.'),
        backgroundColor: AppColors.success,
      ),
    );
    
    // TODO: Navigate to reset password screen
    // Navigator.pushReplacement(context, MaterialPageRoute(
    //   builder: (context) => ResetPasswordScreen(verificationToken: otp),
    // ));
  }

  void _resendPasswordRecoveryOTP(BuildContext context) {
    // TODO: Implement resend logic for password recovery
    print('Resending password recovery OTP via $recoveryMethod to $contactInfo');
  }
}

/// Integration example for sign-up verification flow
class SignUpOTPExample extends StatelessWidget {
  final String userEmail;
  final String userPhone;

  const SignUpOTPExample({
    super.key,
    required this.userEmail,
    required this.userPhone,
  });

  @override
  Widget build(BuildContext context) {
    return OTPVerificationScreen(
      verificationMethod: 'email', // or 'sms' based on user preference
      contactInfo: userEmail,
      onVerificationSuccess: (otp) {
        // Complete sign-up process
        _completeSignUp(context, otp);
      },
      onResendOTP: () {
        // Resend verification OTP
        _resendSignUpOTP(context);
      },
      onBack: () {
        // Go back to sign-up form
        Navigator.of(context).pop();
      },
    );
  }

  void _completeSignUp(BuildContext context, String otp) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Account verified! Welcome to RideOn!'),
        backgroundColor: AppColors.success,
      ),
    );
    
    // TODO: Complete sign-up and navigate to main app
    // Navigator.pushAndRemoveUntil(context, MaterialPageRoute(
    //   builder: (context) => MainAppScreen(),
    // ), (route) => false);
  }

  void _resendSignUpOTP(BuildContext context) {
    // TODO: Implement resend logic for sign-up verification
    print('Resending sign-up verification OTP to $userEmail');
  }
}
