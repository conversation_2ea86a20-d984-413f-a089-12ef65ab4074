/// Model representing a Package in the RideOn app
class Package {
  final String uuid;
  final DateTime created;
  final DateTime updated;
  final String price;
  final int quantity;
  final List<dynamic> category;
  final String? label;
  final String? detail;
  final bool fragile;
  final String packagePickupType;
  final DateTime? packagePickupDate;
  final Map<String, dynamic>? weight;
  final String pickupAddress; // UUID reference
  final String deliveryAddress; // UUID reference
  final String type;
  final String collection; // UUID reference to Collection

  const Package({
    required this.uuid,
    required this.created,
    required this.updated,
    required this.price,
    required this.quantity,
    required this.category,
    this.label,
    this.detail,
    required this.fragile,
    required this.packagePickupType,
    this.packagePickupDate,
    this.weight,
    required this.pickupAddress,
    required this.deliveryAddress,
    required this.type,
    required this.collection,
  });

  /// Create Package from JSON
  factory Package.fromJson(Map<String, dynamic> json) {
    return Package(
      uuid: json['uuid'] as String,
      created: DateTime.parse(json['created'] as String),
      updated: DateTime.parse(json['updated'] as String),
      price: json['price'] as String,
      quantity: json['quantity'] as int,
      category: json['category'] as List<dynamic>? ?? [],
      label: json['label'] as String?,
      detail: json['detail'] as String?,
      fragile: json['fragile'] as bool? ?? false,
      packagePickupType: json['packagePickupType'] as String,
      packagePickupDate: json['packagePickupDate'] != null 
          ? DateTime.parse(json['packagePickupDate'] as String)
          : null,
      weight: json['weight'] as Map<String, dynamic>?,
      pickupAddress: json['pickupAddress'] as String,
      deliveryAddress: json['deliveryAddress'] as String,
      type: json['type'] as String,
      collection: json['collection'] as String,
    );
  }

  /// Convert Package to JSON
  Map<String, dynamic> toJson() {
    return {
      'uuid': uuid,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
      'price': price,
      'quantity': quantity,
      'category': category,
      if (label != null) 'label': label,
      if (detail != null) 'detail': detail,
      'fragile': fragile,
      'packagePickupType': packagePickupType,
      if (packagePickupDate != null) 'packagePickupDate': packagePickupDate!.toIso8601String(),
      if (weight != null) 'weight': weight,
      'pickupAddress': pickupAddress,
      'deliveryAddress': deliveryAddress,
      'type': type,
      'collection': collection,
    };
  }

  /// Create a copy with updated fields
  Package copyWith({
    String? uuid,
    DateTime? created,
    DateTime? updated,
    String? price,
    int? quantity,
    List<dynamic>? category,
    String? label,
    String? detail,
    bool? fragile,
    String? packagePickupType,
    DateTime? packagePickupDate,
    Map<String, dynamic>? weight,
    String? pickupAddress,
    String? deliveryAddress,
    String? type,
    String? collection,
  }) {
    return Package(
      uuid: uuid ?? this.uuid,
      created: created ?? this.created,
      updated: updated ?? this.updated,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      category: category ?? this.category,
      label: label ?? this.label,
      detail: detail ?? this.detail,
      fragile: fragile ?? this.fragile,
      packagePickupType: packagePickupType ?? this.packagePickupType,
      packagePickupDate: packagePickupDate ?? this.packagePickupDate,
      weight: weight ?? this.weight,
      pickupAddress: pickupAddress ?? this.pickupAddress,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      type: type ?? this.type,
      collection: collection ?? this.collection,
    );
  }

  @override
  String toString() {
    return 'Package(uuid: $uuid, label: $label, quantity: $quantity, fragile: $fragile)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Package && other.uuid == uuid;
  }

  @override
  int get hashCode => uuid.hashCode;
}

/// Request model for creating a new package
class CreatePackageRequest {
  final int quantity;
  final List<dynamic> category;
  final String? label;
  final String? detail;
  final bool fragile;
  final String packagePickupType;
  final DateTime? packagePickupDate;
  final Map<String, dynamic>? weight;
  final String pickupAddressUuid;
  final String deliveryAddressUuid;

  const CreatePackageRequest({
    required this.quantity,
    required this.category,
    this.label,
    this.detail,
    this.fragile = false,
    required this.packagePickupType,
    this.packagePickupDate,
    this.weight,
    required this.pickupAddressUuid,
    required this.deliveryAddressUuid,
  });

  Map<String, dynamic> toJson() {
    return {
      'quantity': quantity,
      'category': category,
      if (label != null) 'label': label,
      if (detail != null) 'detail': detail,
      'fragile': fragile,
      'packagePickupType': packagePickupType,
      if (packagePickupDate != null) 'packagePickupDate': packagePickupDate!.toIso8601String(),
      if (weight != null) 'weight': weight,
      'pickupAddress': pickupAddressUuid,
      'deliveryAddress': deliveryAddressUuid,
    };
  }
}

/// Request model for updating a package
class UpdatePackageRequest {
  final int? quantity;
  final List<dynamic>? category;
  final String? label;
  final String? detail;
  final bool? fragile;
  final String? packagePickupType;
  final DateTime? packagePickupDate;
  final Map<String, dynamic>? weight;
  final String? pickupAddressUuid;
  final String? deliveryAddressUuid;

  const UpdatePackageRequest({
    this.quantity,
    this.category,
    this.label,
    this.detail,
    this.fragile,
    this.packagePickupType,
    this.packagePickupDate,
    this.weight,
    this.pickupAddressUuid,
    this.deliveryAddressUuid,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {};
    
    if (quantity != null) json['quantity'] = quantity;
    if (category != null) json['category'] = category;
    if (label != null) json['label'] = label;
    if (detail != null) json['detail'] = detail;
    if (fragile != null) json['fragile'] = fragile;
    if (packagePickupType != null) json['packagePickupType'] = packagePickupType;
    if (packagePickupDate != null) json['packagePickupDate'] = packagePickupDate!.toIso8601String();
    if (weight != null) json['weight'] = weight;
    if (pickupAddressUuid != null) json['pickupAddress'] = pickupAddressUuid;
    if (deliveryAddressUuid != null) json['deliveryAddress'] = deliveryAddressUuid;
    
    return json;
  }
}

/// Model for package categories
class PackageCategory {
  final String id;
  final String name;
  final String? description;
  final String? icon;
  final bool active;

  const PackageCategory({
    required this.id,
    required this.name,
    this.description,
    this.icon,
    this.active = true,
  });

  factory PackageCategory.fromJson(Map<String, dynamic> json) {
    return PackageCategory(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      icon: json['icon'] as String?,
      active: json['active'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      if (description != null) 'description': description,
      if (icon != null) 'icon': icon,
      'active': active,
    };
  }
}
