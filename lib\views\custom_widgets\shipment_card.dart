import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';

/// Data model for shipment information
class ShipmentData {
  final String id;
  final String title;
  final String trackingNumber;
  final ShipmentStatus status;
  final List<TrackingStep> trackingSteps;
  final bool isExpanded;

  const ShipmentData({
    required this.id,
    required this.title,
    required this.trackingNumber,
    required this.status,
    required this.trackingSteps,
    this.isExpanded = false,
  });

  ShipmentData copyWith({
    String? id,
    String? title,
    String? trackingNumber,
    ShipmentStatus? status,
    List<TrackingStep>? trackingSteps,
    bool? isExpanded,
  }) {
    return ShipmentData(
      id: id ?? this.id,
      title: title ?? this.title,
      trackingNumber: trackingNumber ?? this.trackingNumber,
      status: status ?? this.status,
      trackingSteps: trackingSteps ?? this.trackingSteps,
      isExpanded: isExpanded ?? this.isExpanded,
    );
  }
}

/// Shipment status enum
enum ShipmentStatus {
  pending,
  inProgress,
  completed,
  cancelled,
}

/// Extension for shipment status
extension ShipmentStatusExtension on ShipmentStatus {
  String get displayName {
    switch (this) {
      case ShipmentStatus.pending:
        return 'Pending';
      case ShipmentStatus.inProgress:
        return 'In progress';
      case ShipmentStatus.completed:
        return 'Completed';
      case ShipmentStatus.cancelled:
        return 'Cancelled';
    }
  }

  Color get color {
    switch (this) {
      case ShipmentStatus.pending:
        return AppColors.pending;
      case ShipmentStatus.inProgress:
        return AppColors.warning;
      case ShipmentStatus.completed:
        return AppColors.success;
      case ShipmentStatus.cancelled:
        return AppColors.error;
    }
  }

  Color get textColor {
    switch (this) {
      case ShipmentStatus.pending:
      case ShipmentStatus.inProgress:
      case ShipmentStatus.completed:
        return const Color(0xFF1E1E1E);
      case ShipmentStatus.cancelled:
        return AppColors.white;
    }
  }
}

/// Data model for tracking steps
class TrackingStep {
  final String title;
  final String? address;
  final String? time;
  final bool isCompleted;

  const TrackingStep({
    required this.title,
    this.address,
    this.time,
    required this.isCompleted,
  });
}

/// Reusable shipment card widget
/// 
/// A customizable widget for displaying shipment information with tracking details.
/// Supports expansion/collapse functionality and responsive design.
class ShipmentCard extends StatelessWidget {
  /// The shipment data to display
  final ShipmentData shipment;
  
  /// Callback when the expand/collapse button is tapped
  final VoidCallback? onExpandToggle;
  
  /// Callback when the card is tapped
  final VoidCallback? onCardTap;
  
  /// Whether to show the tracking timeline
  final bool showTrackingTimeline;
  
  /// Custom icon for the shipment
  final IconData? customIcon;
  
  /// Custom background color for the card
  final Color? backgroundColor;

  const ShipmentCard({
    super.key,
    required this.shipment,
    this.onExpandToggle,
    this.onCardTap,
    this.showTrackingTimeline = true,
    this.customIcon,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onCardTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(_getSpacing(context, 26)),
        decoration: ShapeDecoration(
          color: backgroundColor ?? const Color(0xFFFEFEFE),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius(context, 28)),
          ),
          shadows: [
            BoxShadow(
              color: AppColors.black.withValues(alpha: 0.1),
              blurRadius: _getSpacing(context, 8),
              offset: Offset(0, _getSpacing(context, 2)),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section
            _buildHeader(context),
            
            if (showTrackingTimeline && shipment.isExpanded) ...[
              SizedBox(height: _getSpacing(context, 20)),
              
              // Collapse/Expand button
              _buildExpandButton(context),
              
              SizedBox(height: _getSpacing(context, 20)),
              
              // Tracking timeline
              _buildTrackingTimeline(context),
            ] else if (showTrackingTimeline) ...[
              SizedBox(height: _getSpacing(context, 20)),
              
              // Collapse/Expand button
              _buildExpandButton(context),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        // Package icon
        Container(
          width: _getIconSize(context, 53),
          height: _getIconSize(context, 53),
          decoration: ShapeDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            shape: const OvalBorder(),
          ),
          child: Icon(
            customIcon ?? Icons.inventory_2_outlined,
            size: _getIconSize(context, 24),
            color: AppColors.primary,
          ),
        ),
        SizedBox(width: _getSpacing(context, 15)),
        
        // Package info
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                shipment.title,
                style: TextStyle(
                  color: AppColors.black.withValues(alpha: 0.86),
                  fontSize: _getFontSize(context, 16),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                  height: 0.95,
                  letterSpacing: -0.80,
                ),
              ),
              SizedBox(height: _getSpacing(context, 4)),
              Text(
                shipment.trackingNumber,
                style: TextStyle(
                  color: const Color(0xFFABA9AD),
                  fontSize: _getFontSize(context, 12),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                  height: 0.95,
                  letterSpacing: -0.60,
                ),
              ),
            ],
          ),
        ),
        
        // Status badge
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: _getSpacing(context, 20),
            vertical: _getSpacing(context, 7),
          ),
          decoration: ShapeDecoration(
            color: shipment.status.color,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 36)),
            ),
          ),
          child: Text(
            shipment.status.displayName,
            style: TextStyle(
              color: shipment.status.textColor,
              fontSize: _getFontSize(context, 12),
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
              height: 0.95,
              letterSpacing: -0.60,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildExpandButton(BuildContext context) {
    if (onExpandToggle == null) return const SizedBox.shrink();
    
    return GestureDetector(
      onTap: onExpandToggle,
      child: Text(
        shipment.isExpanded ? 'Collapse Delivery Details' : 'Expand Delivery Details',
        style: TextStyle(
          color: AppColors.primary,
          fontSize: _getFontSize(context, 12),
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w500,
          height: 0.95,
          letterSpacing: -0.60,
        ),
      ),
    );
  }

  Widget _buildTrackingTimeline(BuildContext context) {
    return Column(
      children: shipment.trackingSteps.asMap().entries.map((entry) {
        final index = entry.key;
        final step = entry.value;
        final isLast = index == shipment.trackingSteps.length - 1;
        
        return _buildTimelineItem(
          context,
          step: step,
          isLast: isLast,
        );
      }).toList(),
    );
  }

  Widget _buildTimelineItem(
    BuildContext context, {
    required TrackingStep step,
    bool isLast = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: _getIconSize(context, 13),
              height: _getIconSize(context, 13),
              decoration: ShapeDecoration(
                color: step.isCompleted 
                    ? AppColors.primary.withValues(alpha: 0.3)
                    : AppColors.black.withValues(alpha: 0.3),
                shape: const OvalBorder(),
              ),
              child: Center(
                child: Container(
                  width: _getIconSize(context, 9),
                  height: _getIconSize(context, 9),
                  decoration: ShapeDecoration(
                    color: step.isCompleted ? AppColors.primary : Colors.transparent,
                    shape: const OvalBorder(),
                  ),
                ),
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: _getSpacing(context, 40),
                color: AppColors.black.withValues(alpha: 0.1),
              ),
          ],
        ),
        
        SizedBox(width: _getSpacing(context, 10)),
        
        // Content
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                step.title,
                style: TextStyle(
                  color: step.isCompleted 
                      ? AppColors.black 
                      : const Color(0xFFDADADA),
                  fontSize: _getFontSize(context, 12),
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                  height: 0.95,
                  letterSpacing: -0.60,
                ),
              ),
              if (step.time?.isNotEmpty == true) ...[
                SizedBox(height: _getSpacing(context, 4)),
                Text(
                  step.time!,
                  style: TextStyle(
                    color: AppColors.primary,
                    fontSize: _getFontSize(context, 8),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                    height: 0.95,
                    letterSpacing: -0.40,
                  ),
                ),
              ],
              if (step.address?.isNotEmpty == true) ...[
                SizedBox(height: _getSpacing(context, 4)),
                Text(
                  step.address!,
                  style: TextStyle(
                    color: step.isCompleted 
                        ? const Color(0xFFABA9AD)
                        : const Color(0x6B3B3B3B),
                    fontSize: _getFontSize(context, 8),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                    height: 0.95,
                    letterSpacing: -0.40,
                  ),
                ),
              ],
              if (!isLast) SizedBox(height: _getSpacing(context, 16)),
            ],
          ),
        ),
      ],
    );
  }

  // Responsive helper methods
  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8;
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2;
    } else {
      fontSize = baseFontSize;
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8;
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2;
    } else {
      iconSize = baseIconSize;
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6;
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2;
    } else {
      borderRadius = baseBorderRadius;
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }
}
