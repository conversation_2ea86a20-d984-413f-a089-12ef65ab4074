import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/rider_side_menu.dart';
import 'package:rideoon/views/custom_widgets/push_notfication.dart';
import 'package:rideoon/views/onboarding/user_type.dart';
import 'package:rideoon/views/dashboards/rider_dashboard/dashboard_view/dashboard_view.dart';
import 'package:rideoon/views/dashboards/rider_dashboard/deliveries_view/deliveries_view.dart';
import 'package:rideoon/views/dashboards/rider_dashboard/scheduled_deliveries_view/scheduled_deliveries_view.dart';
import 'package:rideoon/views/dashboards/rider_dashboard/history_view/history_view.dart';
import 'package:rideoon/views/dashboards/rider_dashboard/profile_view/profile_view.dart';
import 'package:rideoon/views/dashboards/rider_dashboard/messages_view/messages_view.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/views/onboarding/user_type.dart';

/// Main dashboard screen for riders with side menu navigation
class RiderDashboardScreens extends StatefulWidget {
  const RiderDashboardScreens({super.key});

  @override
  State<RiderDashboardScreens> createState() => _RiderDashboardScreensState();
}

class _RiderDashboardScreensState extends State<RiderDashboardScreens>
    with TickerProviderStateMixin {
  int _selectedIndex = 0;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  bool _isMenuOpen = false;
  AnimationController? _animationController;
  Animation<double>? _slideAnimation;
  Animation<double>? _fadeAnimation;

  // Navigation history tracking
  final List<int> _navigationHistory = [0]; // Start with dashboard (index 0)

  // Sample user profile data
  final UserProfile _userProfile = const UserProfile(
    name: 'John Rider',
    email: '<EMAIL>',
    avatarUrl: null,
  );

  // Sample menu items
  late List<SideMenuItem> _menuItems;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Slide animation for menu
    _slideAnimation = Tween<double>(
      begin: -1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController!,
      curve: Curves.easeInOut,
    ));

    // Fade animation for overlay
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController!,
      curve: Curves.easeInOut,
    ));

    _menuItems = [
      SideMenuItem(
        icon: Icons.dashboard,
        label: 'Dashboard',
        isSelected: _selectedIndex == 0,
        onTap: () => _onMenuItemTapped(0),
      ),
      SideMenuItem(
        icon: Icons.delivery_dining,
        label: 'Instant Deliveries',
        isSelected: _selectedIndex == 1,
        onTap: () => _onMenuItemTapped(1),
      ),
      SideMenuItem(
        icon: Icons.schedule,
        label: 'Scheduled Deliveries',
        isSelected: _selectedIndex == 2,
        onTap: () => _onMenuItemTapped(2),
      ),
      SideMenuItem(
        icon: Icons.history,
        label: 'History',
        isSelected: _selectedIndex == 3,
        onTap: () => _onMenuItemTapped(3),
      ),
      SideMenuItem(
        icon: Icons.account_circle,
        label: 'Profile',
        isSelected: _selectedIndex == 4,
        onTap: () => _onMenuItemTapped(4),
      ),
      SideMenuItem(
        icon: Icons.message,
        label: 'Messages',
        isSelected: _selectedIndex == 5,
        onTap: () => _onMenuItemTapped(5),
      ),
      SideMenuItem(
        icon: Icons.logout,
        label: 'Logout',
        isSelected: false,
        onTap: () => _handleLogout(),
      ),
    ];
  }

  @override
  void dispose() {
    _animationController?.dispose();
    super.dispose();
  }

  void _onMenuItemTapped(int index) {
    setState(() {
      // Only add to history if it's a different page
      if (_selectedIndex != index) {
        _navigationHistory.add(index);
      }
      _selectedIndex = index;

      // Update menu items selection state
      for (int i = 0; i < _menuItems.length - 1; i++) {
        _menuItems[i] = SideMenuItem(
          icon: _menuItems[i].icon,
          label: _menuItems[i].label,
          badgeCount: _menuItems[i].badgeCount,
          color: _menuItems[i].color,
          isSelected: i == index,
          onTap: _menuItems[i].onTap,
        );
      }
    });
    _closeMenu(); // Close menu
  }

  void _handleLogout() {
    _closeMenu(); // Close menu

    // Show logout confirmation dialog
    _showLogoutConfirmation(context);
  }

  void _showLogoutConfirmation(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          constraints: const BoxConstraints(maxWidth: 320),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: AppColors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 8),
                spreadRadius: 2,
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    color: AppColors.error.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.logout,
                    size: 28,
                    color: AppColors.error,
                  ),
                ),

                const SizedBox(height: 20),

                // Title
                Text(
                  'Logout Confirmation',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: AppColors.black,
                    fontSize: 18,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w600,
                  ),
                ),

                const SizedBox(height: 12),

                // Description
                Text(
                  'Are you sure you want to logout? You will need to sign in again to access your account.',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: AppColors.black.withValues(alpha: 0.7),
                    fontSize: 14,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w400,
                    height: 1.4,
                  ),
                ),

                const SizedBox(height: 24),

                // Action buttons
                Row(
                  children: [
                    // Cancel button
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          backgroundColor: AppColors.white,
                          foregroundColor: AppColors.black,
                          side: BorderSide(
                            width: 1,
                            color: AppColors.black.withValues(alpha: 0.2),
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          elevation: 0,
                        ),
                        child: Text(
                          'Cancel',
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    // Logout button
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          _performLogout();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.error,
                          foregroundColor: AppColors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          elevation: 0,
                        ),
                        child: Text(
                          'Logout',
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _performLogout() {
    // Navigate to user type selection screen
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (context) => const UserTypeSelection(),
      ),
      (route) => false, // Remove all previous routes
    );

    // Show success toast
    Toast.success('Logged out successfully');
  }

  void _toggleMenu() {
    print('Toggle menu called. Current state: $_isMenuOpen'); // Debug
    if (_isMenuOpen) {
      _closeMenu();
    } else {
      setState(() {
        _isMenuOpen = true;
      });
      _animationController?.forward();
      print('Menu opened, animation started'); // Debug
    }
  }

  void _closeMenu() {
    print('Close menu called'); // Debug
    if (_animationController != null) {
      _animationController!.reverse().then((_) {
        if (mounted) {
          setState(() {
            _isMenuOpen = false;
          });
          print('Menu closed'); // Debug
        }
      });
    } else {
      setState(() {
        _isMenuOpen = false;
      });
    }
  }

  void _handleBackNavigation() {
    if (_navigationHistory.length > 1) {
      setState(() {
        // Remove current page from history
        _navigationHistory.removeLast();
        // Navigate to previous page
        int previousIndex = _navigationHistory.last;
        _selectedIndex = previousIndex;

        // Update menu items selection state
        for (int i = 0; i < _menuItems.length - 1; i++) {
          _menuItems[i] = SideMenuItem(
            icon: _menuItems[i].icon,
            label: _menuItems[i].label,
            badgeCount: _menuItems[i].badgeCount,
            color: _menuItems[i].color,
            isSelected: i == previousIndex,
            onTap: _menuItems[i].onTap,
          );
        }
      });
    }
  }

  bool _canNavigateBack() {
    return _navigationHistory.length > 1;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: AppColors.white,
      body: Stack(
        children: [
          // Main content
          _getSelectedPage(),

          // Animated sidebar overlay
          if (_isMenuOpen)
            _animationController != null
                ? AnimatedBuilder(
                    animation: _animationController!,
                    builder: (context, child) {
                      return GestureDetector(
                        onTap: _closeMenu,
                        child: Container(
                          color: AppColors.black.withValues(alpha: 0.4 * (_fadeAnimation?.value ?? 1.0)),
                          child: Stack(
                            children: [
                              // Animated sidebar
                              Positioned(
                                left: (_slideAnimation?.value ?? 0.0) * 260, // Slide from left
                                top: 0,
                                child: RiderSideMenu(
                                  userProfile: _userProfile,
                                  menuItems: _menuItems,
                                  onClose: _closeMenu,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  )
                : GestureDetector(
                    onTap: _closeMenu,
                    child: Container(
                      color: AppColors.black.withValues(alpha: 0.4),
                      child: Stack(
                        children: [
                          // Static sidebar (fallback)
                          Positioned(
                            left: 0,
                            top: 0,
                            child: RiderSideMenu(
                              userProfile: _userProfile,
                              menuItems: _menuItems,
                              onClose: _closeMenu,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

          // Floating toggle button (menu)
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            left: 16,
            child: _buildFloatingToggle(context),
          ),

          // Back arrow button (only show if can navigate back)
          if (_canNavigateBack())
            Positioned(
              top: MediaQuery.of(context).padding.top + 16,
              right: 16,
              child: _buildBackButton(context),
            ),
        ],
      ),
    );
  }

  Widget _buildFloatingToggle(BuildContext context) {
    return GestureDetector(
      onTap: _toggleMenu,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: _isMenuOpen ? 80 : 48,
        height: 48,
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: AppColors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
              spreadRadius: 0,
            ),
          ],
        ),
        child: _isMenuOpen
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '<',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.black.withValues(alpha: 0.8),
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Back',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.black.withValues(alpha: 0.8),
                      fontFamily: 'Poppins',
                    ),
                  ),
                ],
              )
            : Icon(
                Icons.menu,
                size: 24,
                color: AppColors.black.withValues(alpha: 0.8),
              ),
      ),
    );
  }

  Widget _buildBackButton(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _handleBackNavigation();
      },
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: AppColors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Center(
          child: Text(
            '<',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.black.withValues(alpha: 0.8),
            ),
          ),
        ),
      ),
    );
  }

  Widget _getSelectedPage() {
    switch (_selectedIndex) {
      case 0:
        return const DashboardView();
      case 1:
        return const DeliveriesView();
      case 2:
        return const ScheduledDeliveriesView();
      case 3:
        return const HistoryView();
      case 4:
        return const ProfileView();
      case 5:
        return const MessagesView();
      default:
        return const DashboardView();
    }
  }
}

