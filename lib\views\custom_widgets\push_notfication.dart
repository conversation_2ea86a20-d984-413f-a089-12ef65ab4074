import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'dart:io';

/// A reusable push notification permission dialog widget
///
/// This widget displays a modal dialog asking for push notification permissions
/// with customizable title, description, and action buttons.
///
/// Features:
/// - Responsive design for mobile, tablet, and smartwatch
/// - Customizable title and description text
/// - Configurable button actions and labels
/// - Consistent styling with app theme
/// - Accessibility support
class PushNotificationDialog extends StatelessWidget {
  /// The title text displayed at the top of the dialog
  final String title;

  /// The description text explaining the notification request
  final String description;

  /// The text for the decline button (default: "No, thanks")
  final String declineButtonText;

  /// The text for the accept button (default: "Yes, sure")
  final String acceptButtonText;

  /// Callback function when the decline button is pressed
  final VoidCallback? onDecline;

  /// Callback function when the accept button is pressed
  final VoidCallback? onAccept;

  /// Whether to show the dialog with a barrier that can be dismissed
  final bool barrierDismissible;

  /// The icon to display at the top of the dialog
  final IconData? icon;

  /// The color of the icon background
  final Color? iconBackgroundColor;

  /// The color of the icon
  final Color? iconColor;

  const PushNotificationDialog({
    super.key,
    this.title = 'Push notifications',
    this.description = 'Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been',
    this.declineButtonText = 'No, thanks',
    this.acceptButtonText = 'Yes, sure',
    this.onDecline,
    this.onAccept,
    this.barrierDismissible = true,
    this.icon,
    this.iconBackgroundColor,
    this.iconColor,
  });

  /// Static method to show the push notification dialog
  static Future<bool?> show(
    BuildContext context, {
    String? title,
    String? description,
    String? declineButtonText,
    String? acceptButtonText,
    VoidCallback? onDecline,
    VoidCallback? onAccept,
    bool barrierDismissible = true,
    IconData? icon,
    Color? iconBackgroundColor,
    Color? iconColor,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (BuildContext context) => PushNotificationDialog(
        title: title ?? 'Push notifications',
        description: description ?? 'Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been',
        declineButtonText: declineButtonText ?? 'No, thanks',
        acceptButtonText: acceptButtonText ?? 'Yes, sure',
        onDecline: onDecline,
        onAccept: onAccept,
        barrierDismissible: barrierDismissible,
        icon: icon,
        iconBackgroundColor: iconBackgroundColor,
        iconColor: iconColor,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: _buildDialogContent(context),
    );
  }

  Widget _buildDialogContent(BuildContext context) {
    return Container(
      width: _getDialogWidth(context),
      constraints: BoxConstraints(maxWidth: _getMaxWidth(context)),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(_getBorderRadius(context)),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: _getSpacing(context, 20),
            offset: Offset(0, _getSpacing(context, 8)),
            spreadRadius: _getSpacing(context, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(_getContentPadding(context)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon
            Container(
              width: _getIconSize(context),
              height: _getIconSize(context),
              decoration: BoxDecoration(
                color: (iconBackgroundColor ?? AppColors.primary).withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon ?? Icons.info_outline,
                size: _getIconInnerSize(context),
                color: iconColor ?? AppColors.primary,
              ),
            ),

            SizedBox(height: _getSpacing(context, 20)),

            // Title
            _buildTitle(context),

            SizedBox(height: _getSpacing(context, 12)),

            // Description
            _buildDescription(context),

            SizedBox(height: _getSpacing(context, 24)),

            // Action buttons
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Text(
      title,
      textAlign: TextAlign.center,
      style: TextStyle(
        color: AppColors.black,
        fontSize: _getTitleFontSize(context),
        fontFamily: 'Poppins',
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildDescription(BuildContext context) {
    return Text(
      description,
      textAlign: TextAlign.center,
      style: TextStyle(
        color: AppColors.black.withValues(alpha: 0.7),
        fontSize: _getBodyFontSize(context),
        fontFamily: 'Poppins',
        fontWeight: FontWeight.w400,
        height: 1.4,
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        // Decline button
        _buildDeclineButton(context),

        SizedBox(width: _getSpacing(context, 12)),

        // Accept button
        _buildAcceptButton(context),
      ],
    );
  }

  Widget _buildDeclineButton(BuildContext context) {
    return Expanded(
      child: SizedBox(
        height: _getButtonHeight(context),
        child: OutlinedButton(
          onPressed: () {
            Navigator.of(context).pop(false);
            onDecline?.call();
          },
          style: OutlinedButton.styleFrom(
            backgroundColor: AppColors.white,
            foregroundColor: AppColors.black,
            side: BorderSide(
              width: 1,
              color: AppColors.black.withValues(alpha: 0.3),
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
            ),
            padding: EdgeInsets.symmetric(vertical: _getSpacing(context, 12)),
            elevation: 0,
          ),
          child: Text(
            declineButtonText,
            style: TextStyle(
              fontSize: _getButtonFontSize(context),
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAcceptButton(BuildContext context) {
    return Expanded(
      child: SizedBox(
        height: _getButtonHeight(context),
        child: ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop(true);
            onAccept?.call();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
            ),
            padding: EdgeInsets.symmetric(vertical: _getSpacing(context, 12)),
            elevation: 0,
          ),
          child: Text(
            acceptButtonText,
            style: TextStyle(
              fontSize: _getButtonFontSize(context),
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  // Responsive helper methods
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return screenWidth * 0.90; // Smartwatch - 90% width (compressed from 95%)
    } else if (screenWidth > 600) {
      return 320; // Tablet - compressed from 400px
    } else {
      return screenWidth * 0.80; // Mobile - compressed from 85% to 80%
    }
  }

  double _getMaxWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 280; // Smartwatch max width
    } else if (screenWidth > 600) {
      return 320; // Tablet max width (compressed)
    } else {
      return 320; // Mobile max width (compressed)
    }
  }

  double _getContentPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 16; // Smartwatch - smaller padding
    } else if (screenWidth > 600) {
      return 28; // Tablet - compressed from 32px
    } else {
      return 24; // Mobile - standard padding
    }
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch - reduced spacing
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.0; // Tablet - no increase (compressed from 1.2)
    } else {
      spacing = baseSpacing; // Mobile - standard spacing
    }

    if (isShortScreen) {
      spacing = spacing * 0.8; // Reduce for short screens
    }

    return spacing;
  }

  double _getIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 40; // Smartwatch - smaller icon
    } else if (screenWidth > 600) {
      return 60; // Tablet - slightly larger
    } else {
      return 56; // Mobile - standard size
    }
  }

  double _getIconInnerSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 20; // Smartwatch - smaller inner icon
    } else if (screenWidth > 600) {
      return 30; // Tablet - slightly larger
    } else {
      return 28; // Mobile - standard size
    }
  }

  double _getTitleFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 14; // Smartwatch - smaller text
    } else if (screenWidth > 600) {
      baseSize = 20; // Tablet - larger text
    } else {
      baseSize = 18; // Mobile - compressed from 16px to 18px
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9; // Reduce for short screens
    }

    return baseSize;
  }

  double _getBodyFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10; // Smartwatch - very small text
    } else if (screenWidth > 600) {
      baseSize = 16; // Tablet - larger text
    } else {
      baseSize = 14; // Mobile - compressed from 12px to 14px
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9; // Reduce for short screens
    }

    return baseSize;
  }

  double _getButtonFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10; // Smartwatch - small button text
    } else if (screenWidth > 600) {
      baseSize = 16; // Tablet - larger button text
    } else {
      baseSize = 14; // Mobile - compressed from 12px to 14px
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9; // Reduce for short screens
    }

    return baseSize;
  }

  double _getBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 12; // Smartwatch - increased from 8px
    } else if (screenWidth > 600) {
      return 16; // Tablet - increased from 12px
    } else {
      return 16; // Mobile - increased from 10px to match compressed style
    }
  }

  double _getButtonBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 6; // Smartwatch - increased from 4px
    } else if (screenWidth > 600) {
      return 8; // Tablet - increased from 6px
    } else {
      return 8; // Mobile - increased from 5px to match compressed style
    }
  }

  double _getButtonHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseHeight;
    if (screenWidth < 300) {
      baseHeight = 36; // Smartwatch - increased from 32px
    } else if (screenWidth > 600) {
      baseHeight = 48; // Tablet - increased from 44px
    } else {
      baseHeight = 44; // Mobile - increased from 38px to match compressed style
    }

    if (isShortScreen) {
      baseHeight = baseHeight * 0.9; // Reduce for short screens
    }

    return baseHeight;
  }
}

/// Image Preview Dialog using the same container style as PushNotificationDialog
class ImagePreviewDialog extends StatelessWidget {
  final String imagePath;
  final String title;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final String confirmButtonText;
  final String cancelButtonText;

  const ImagePreviewDialog({
    super.key,
    required this.imagePath,
    this.title = 'Image Preview',
    this.onConfirm,
    this.onCancel,
    this.confirmButtonText = 'Add Image',
    this.cancelButtonText = 'Cancel',
  });

  /// Static method to show the image preview dialog
  static Future<bool?> show(
    BuildContext context, {
    required String imagePath,
    String? title,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    String? confirmButtonText,
    String? cancelButtonText,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) => ImagePreviewDialog(
        imagePath: imagePath,
        title: title ?? 'Image Preview',
        onConfirm: onConfirm,
        onCancel: onCancel,
        confirmButtonText: confirmButtonText ?? 'Add Image',
        cancelButtonText: cancelButtonText ?? 'Cancel',
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: _buildDialogContent(context),
    );
  }

  Widget _buildDialogContent(BuildContext context) {
    return Container(
      width: _getDialogWidth(context),
      constraints: BoxConstraints(maxWidth: _getMaxWidth(context)),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(_getBorderRadius(context)),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: _getSpacing(context, 20),
            offset: Offset(0, _getSpacing(context, 8)),
            spreadRadius: _getSpacing(context, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(_getContentPadding(context)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            _buildTitle(context),

            SizedBox(height: _getSpacing(context, 16)),

            // Image preview
            _buildImagePreview(context),

            SizedBox(height: _getSpacing(context, 20)),

            // Action buttons
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Text(
      title,
      textAlign: TextAlign.center,
      style: TextStyle(
        color: AppColors.black,
        fontSize: _getTitleFontSize(context),
        fontFamily: 'Poppins',
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildImagePreview(BuildContext context) {
    return Container(
      width: double.infinity,
      height: _getImageHeight(context),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(_getBorderRadius(context)),
        border: Border.all(
          color: AppColors.black.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(_getBorderRadius(context)),
        child: Image.file(
          File(imagePath),
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: AppColors.black.withValues(alpha: 0.1),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.image_not_supported,
                    color: AppColors.black.withValues(alpha: 0.4),
                    size: _getSpacing(context, 48),
                  ),
                  SizedBox(height: _getSpacing(context, 8)),
                  Text(
                    'Failed to load image',
                    style: TextStyle(
                      color: AppColors.black.withValues(alpha: 0.6),
                      fontSize: _getBodyFontSize(context),
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        // Cancel button
        _buildCancelButton(context),

        SizedBox(width: _getSpacing(context, 12)),

        // Confirm button
        _buildConfirmButton(context),
      ],
    );
  }

  Widget _buildCancelButton(BuildContext context) {
    return Expanded(
      child: SizedBox(
        height: _getButtonHeight(context),
        child: OutlinedButton(
          onPressed: () {
            Navigator.of(context).pop(false);
            onCancel?.call();
          },
          style: OutlinedButton.styleFrom(
            backgroundColor: AppColors.white,
            foregroundColor: AppColors.black,
            side: BorderSide(
              width: 1,
              color: AppColors.black.withValues(alpha: 0.3),
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
            ),
            padding: EdgeInsets.symmetric(vertical: _getSpacing(context, 12)),
            elevation: 0,
          ),
          child: Text(
            cancelButtonText,
            style: TextStyle(
              fontSize: _getButtonFontSize(context),
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildConfirmButton(BuildContext context) {
    return Expanded(
      child: SizedBox(
        height: _getButtonHeight(context),
        child: ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop(true);
            onConfirm?.call();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_getButtonBorderRadius(context)),
            ),
            padding: EdgeInsets.symmetric(vertical: _getSpacing(context, 12)),
            elevation: 0,
          ),
          child: Text(
            confirmButtonText,
            style: TextStyle(
              fontSize: _getButtonFontSize(context),
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  double _getImageHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    if (screenWidth < 300) {
      return 150; // Smartwatch - smaller image
    } else if (screenWidth > 600) {
      return 250; // Tablet - larger image
    } else {
      return 200; // Mobile - standard size
    }
  }

  // Responsive helper methods (reused from PushNotificationDialog)
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return screenWidth * 0.90;
    } else if (screenWidth > 600) {
      return 320;
    } else {
      return screenWidth * 0.80;
    }
  }

  double _getMaxWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 280;
    } else if (screenWidth > 600) {
      return 320;
    } else {
      return 320;
    }
  }

  double _getContentPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 16;
    } else if (screenWidth > 600) {
      return 28;
    } else {
      return 24;
    }
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.0;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getTitleFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 14;
    } else if (screenWidth > 600) {
      baseSize = 20;
    } else {
      baseSize = 18;
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getBodyFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10;
    } else if (screenWidth > 600) {
      baseSize = 16;
    } else {
      baseSize = 14;
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getButtonFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10;
    } else if (screenWidth > 600) {
      baseSize = 16;
    } else {
      baseSize = 14;
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 12;
    } else if (screenWidth > 600) {
      return 16;
    } else {
      return 16;
    }
  }

  double _getButtonBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 300) {
      return 6;
    } else if (screenWidth > 600) {
      return 8;
    } else {
      return 8;
    }
  }

  double _getButtonHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseHeight;
    if (screenWidth < 300) {
      baseHeight = 36;
    } else if (screenWidth > 600) {
      baseHeight = 48;
    } else {
      baseHeight = 44;
    }

    if (isShortScreen) {
      baseHeight = baseHeight * 0.9;
    }

    return baseHeight;
  }
}