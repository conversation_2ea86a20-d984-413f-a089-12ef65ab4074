import 'package:rideoon/models/auth/account.dart';

/// Response model for user sign-in API call
class SignInResponse {
  final int status;
  final String token;
  final Account account;

  const SignInResponse({
    required this.status,
    required this.token,
    required this.account,
  });

  /// Check if the sign-in was successful
  bool get isSuccess => status == 200;

  /// Create a SignInResponse from JSON data
  factory SignInResponse.fromJson(Map<String, dynamic> json) {
    return SignInResponse(
      status: json['status'] as int,
      token: json['token'] as String,
      account: Account.fromJson(json['account'] as Map<String, dynamic>),
    );
  }

  /// Convert the response to JSON format
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'token': token,
      'account': account.toJson(),
    };
  }

  /// Create a copy of this response with some fields updated
  SignInResponse copyWith({
    int? status,
    String? token,
    Account? account,
  }) {
    return SignInResponse(
      status: status ?? this.status,
      token: token ?? this.token,
      account: account ?? this.account,
    );
  }

  @override
  String toString() {
    return 'SignInResponse(status: $status, token: ${token.substring(0, 10)}..., account: ${account.fullName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SignInResponse &&
        other.status == status &&
        other.token == token &&
        other.account == account;
  }

  @override
  int get hashCode {
    return Object.hash(status, token, account);
  }
}
