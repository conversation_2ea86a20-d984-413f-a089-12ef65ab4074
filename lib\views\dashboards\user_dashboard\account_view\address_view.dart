import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/providers/address_provider.dart';
import 'package:rideoon/services/address_service.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/models/address/address_request.dart';
import 'package:rideoon/views/custom_widgets/push_notfication.dart';
import 'package:rideoon/views/dashboards/user_dashboard/send_a_package/location_confirmation_map.dart';
import 'package:rideoon/views/dashboards/user_dashboard/account_view/edit_address_view.dart';

/// Address Management view for user account
class AddressView extends StatefulWidget {
  const AddressView({super.key});

  @override
  State<AddressView> createState() => _AddressViewState();
}

class _AddressViewState extends State<AddressView> {
  @override
  void initState() {
    super.initState();
    // Load addresses initially
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<AddressProvider>(context, listen: false).loadAddresses();
    });
  }



  /// Get appropriate icon for address type
  IconData _getAddressIcon(String? type) {
    switch (type) {
      case 'pickup':
        return Icons.send;
      case 'receiver':
        return Icons.location_on;
      default:
        return Icons.place;
    }
  }

  /// Get display title for address
  String _getAddressTitle(Map<String, dynamic> address) {
    final name = address['name'] ?? 'Unknown';
    final type = address['type'];

    if (type == 'pickup') {
      return '$name (Pickup)';
    } else if (type == 'receiver') {
      return '$name (Delivery)';
    }
    return name;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5FF),
      appBar: _buildAppBar(context),
      body: SafeArea(
        child: Column(
          children: [
            // Add new address button
            Padding(
              padding: EdgeInsets.all(_getHorizontalPadding(context)),
              child: _buildAddNewAddressButton(context),
            ),

            SizedBox(height: _getSpacing(context, 16)),

            // Addresses list
            Expanded(
              child: Consumer<AddressProvider>(
                builder: (context, addressProvider, child) {
                  if (addressProvider.isLoading) {
                    return _buildLoadingState(context);
                  } else {
                    return _buildAddressesList(context, addressProvider.addresses);
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: const Color(0xFFF5F5FF),
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: AppColors.black,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        'Address Management',
        style: TextStyle(
          fontSize: _getFontSize(context, 20),
          fontWeight: FontWeight.bold,
          fontFamily: 'Poppins',
          color: AppColors.black,
        ),
      ),
    );
  }

  Widget _buildAddNewAddressButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () {
          _showAddAddressDialog(context);
        },
        icon: Icon(
          Icons.add,
          size: _getIconSize(context, 20),
          color: AppColors.white,
        ),
        label: Text(
          'Add New Address',
          style: TextStyle(
            fontSize: _getFontSize(context, 16),
            fontWeight: FontWeight.w600,
            fontFamily: 'Poppins',
            color: AppColors.white,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
          padding: EdgeInsets.symmetric(
            vertical: _getSpacing(context, 16),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
          ),
          elevation: 0,
        ),
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Center(
      child: CircularProgressIndicator(
        color: AppColors.primary,
      ),
    );
  }

  Widget _buildAddressesList(BuildContext context, List<Map<String, dynamic>> addresses) {
    if (addresses.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      itemCount: addresses.length,
      itemBuilder: (context, index) {
        final address = addresses[index];
        return Padding(
          padding: EdgeInsets.only(bottom: _getSpacing(context, 12)),
          child: _buildAddressCard(context, address, index),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_off,
            size: 64,
            color: AppColors.black.withValues(alpha: 0.3),
          ),
          SizedBox(height: _getSpacing(context, 16)),
          Text(
            'No Saved Addresses',
            style: TextStyle(
              fontSize: _getFontSize(context, 18),
              fontWeight: FontWeight.w600,
              fontFamily: 'Poppins',
              color: AppColors.black.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: _getSpacing(context, 8)),
          Text(
            'Add addresses from pickup or delivery forms\nto see them here',
            style: TextStyle(
              fontSize: _getFontSize(context, 14),
              fontFamily: 'Poppins',
              color: AppColors.black.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAddressCard(BuildContext context, Map<String, dynamic> address, int index) {
    final isDefault = address['isDefault'] ?? false;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 16)),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
        border: Border.all(
          color: isDefault 
              ? AppColors.primary.withValues(alpha: 0.3)
              : AppColors.black.withValues(alpha: 0.1),
          width: isDefault ? 2 : 1,
        ),
        boxShadow: isDefault
            ? [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  blurRadius: _getSpacing(context, 8),
                  offset: Offset(0, _getSpacing(context, 2)),
                ),
              ]
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and default badge
          Row(
            children: [
              Container(
                width: _getIconSize(context, 40),
                height: _getIconSize(context, 40),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
                ),
                child: Icon(
                  _getAddressIcon(address['type']),
                  size: _getIconSize(context, 20),
                  color: AppColors.primary,
                ),
              ),
              SizedBox(width: _getSpacing(context, 12)),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          _getAddressTitle(address),
                          style: TextStyle(
                            fontSize: _getFontSize(context, 16),
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Poppins',
                            color: AppColors.black,
                          ),
                        ),
                        if (isDefault) ...[
                          SizedBox(width: _getSpacing(context, 8)),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: _getSpacing(context, 8),
                              vertical: _getSpacing(context, 4),
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.primary,
                              borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
                            ),
                            child: Text(
                              'Default',
                              style: TextStyle(
                                fontSize: _getFontSize(context, 10),
                                fontWeight: FontWeight.w500,
                                fontFamily: 'Poppins',
                                color: AppColors.white,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
              // More options button
              PopupMenuButton<String>(
                icon: Icon(
                  Icons.more_vert,
                  size: _getIconSize(context, 20),
                  color: AppColors.black.withValues(alpha: 0.6),
                ),
                onSelected: (value) {
                  _handleAddressAction(context, value, address, index);
                },
                itemBuilder: (context) => [
                  if (!isDefault)
                    PopupMenuItem(
                      value: 'set_default',
                      child: Row(
                        children: [
                          Icon(Icons.star_outline, size: _getIconSize(context, 16)),
                          SizedBox(width: _getSpacing(context, 8)),
                          Text('Set as Default'),
                        ],
                      ),
                    ),
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit_outlined, size: _getIconSize(context, 16)),
                        SizedBox(width: _getSpacing(context, 8)),
                        Text('Edit'),
                      ],
                    ),
                  ),
                  if (!isDefault)
                    PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete_outline, size: _getIconSize(context, 16), color: AppColors.error),
                          SizedBox(width: _getSpacing(context, 8)),
                          Text('Delete', style: TextStyle(color: AppColors.error)),
                        ],
                      ),
                    ),
                ],
              ),
            ],
          ),

          SizedBox(height: _getSpacing(context, 12)),

          // Address details
          Text(
            _getDisplayAddress(address),
            style: TextStyle(
              fontSize: _getFontSize(context, 14),
              fontFamily: 'Poppins',
              color: AppColors.black.withValues(alpha: 0.8),
            ),
          ),
          SizedBox(height: _getSpacing(context, 4)),
          Text(
            _getDisplayLocation(address),
            style: TextStyle(
              fontSize: _getFontSize(context, 14),
              fontFamily: 'Poppins',
              color: AppColors.black.withValues(alpha: 0.6),
            ),
          ),
          SizedBox(height: _getSpacing(context, 8)),
          // Address type badge
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: _getSpacing(context, 8),
              vertical: _getSpacing(context, 4),
            ),
            decoration: BoxDecoration(
              color: address['type'] == 'pickup'
                  ? AppColors.primary.withValues(alpha: 0.1)
                  : AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(_getBorderRadius(context, 8)),
            ),
            child: Text(
              address['type'] == 'pickup' ? 'Pickup Address' : 'Delivery Address',
              style: TextStyle(
                fontSize: _getFontSize(context, 12),
                fontWeight: FontWeight.w500,
                fontFamily: 'Poppins',
                color: address['type'] == 'pickup'
                    ? AppColors.primary
                    : AppColors.success,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Get display address text
  String _getDisplayAddress(Map<String, dynamic> address) {
    final streetData = address['street'] ?? '';
    final cityData = address['city'] ?? '';
    final state = address['state'] ?? '';

    // Parse street and landmark
    final streetParts = streetData.split(', ');
    final street = streetParts.isNotEmpty ? streetParts[0] : '';
    final landmark = streetParts.length > 1 ? streetParts.sublist(1).join(', ') : '';

    // Parse city and LGA
    final cityParts = cityData.split(', ');
    final city = cityParts.isNotEmpty ? cityParts[0] : '';
    final lga = cityParts.length > 1 ? cityParts.sublist(1).join(', ') : '';

    // Build full address string
    final parts = <String>[];
    if (street.isNotEmpty) parts.add(street);
    if (landmark.isNotEmpty) parts.add('($landmark)');
    if (city.isNotEmpty) parts.add(city);
    if (lga.isNotEmpty) parts.add(lga);
    if (state.isNotEmpty) parts.add(state);

    return parts.isNotEmpty ? parts.join(', ') : 'No address';
  }

  /// Get display location text
  String _getDisplayLocation(Map<String, dynamic> address) {
    final state = address['state'] ?? '';
    final phone = address['phoneNumber']?.toString() ?? '';

    if (state.isNotEmpty && phone.isNotEmpty) {
      return '$state • $phone';
    } else if (state.isNotEmpty) {
      return state;
    } else if (phone.isNotEmpty) {
      return phone;
    }
    return 'No location info';
  }

  void _handleAddressAction(BuildContext context, String action, Map<String, dynamic> address, int index) {
    switch (action) {
      case 'set_default':
        // Note: Default functionality can be implemented later if needed
        Toast.info('Default address feature coming soon');
        break;
      case 'edit':
        _navigateToEditAddress(context, address);
        break;
      case 'delete':
        _showDeleteConfirmation(context, address, index);
        break;
    }
  }

  void _showAddAddressDialog(BuildContext context) {
    final nameController = TextEditingController();
    final streetController = TextEditingController();
    final landmarkController = TextEditingController();
    final cityController = TextEditingController();
    final lgaController = TextEditingController();
    final phoneController = TextEditingController();
    String selectedState = 'Lagos';
    String selectedType = 'pickup';
    bool isLoading = false;

    // Nigerian states for dropdown
    final nigerianStates = [
      'Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue',
      'Borno', 'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu',
      'FCT', 'Gombe', 'Imo', 'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi',
      'Kogi', 'Kwara', 'Lagos', 'Nasarawa', 'Niger', 'Ogun', 'Ondo', 'Osun',
      'Oyo', 'Plateau', 'Rivers', 'Sokoto', 'Taraba', 'Yobe', 'Zamfara'
    ];

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(
                'Add New Address',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Poppins',
                  color: AppColors.black,
                ),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Address Type Selection
                    DropdownButtonFormField<String>(
                      value: selectedType,
                      decoration: InputDecoration(
                        labelText: 'Address Type',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      items: [
                        DropdownMenuItem(value: 'pickup', child: Text('Pickup Address')),
                        DropdownMenuItem(value: 'receiver', child: Text('Delivery Address')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          selectedType = value!;
                        });
                      },
                    ),
                    SizedBox(height: 16),

                    // Name Field
                    TextFormField(
                      controller: nameController,
                      decoration: InputDecoration(
                        labelText: selectedType == 'pickup' ? 'Sender Name' : 'Receiver Name',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    SizedBox(height: 16),

                    // Street Address Field
                    TextFormField(
                      controller: streetController,
                      decoration: InputDecoration(
                        labelText: 'Street Address',
                        hintText: 'e.g., 123 Main Street',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      maxLines: 2,
                    ),
                    SizedBox(height: 16),

                    // Landmark Field
                    TextFormField(
                      controller: landmarkController,
                      decoration: InputDecoration(
                        labelText: 'Landmark (Optional)',
                        hintText: 'e.g., Near City Mall',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    SizedBox(height: 16),

                    // City Field
                    TextFormField(
                      controller: cityController,
                      decoration: InputDecoration(
                        labelText: 'City',
                        hintText: 'e.g., Ikeja',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    SizedBox(height: 16),

                    // LGA Field
                    TextFormField(
                      controller: lgaController,
                      decoration: InputDecoration(
                        labelText: 'Local Government Area',
                        hintText: 'e.g., Ikeja LGA',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    SizedBox(height: 16),

                    // State Dropdown
                    DropdownButtonFormField<String>(
                      value: selectedState,
                      decoration: InputDecoration(
                        labelText: 'State',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      items: nigerianStates.map((state) =>
                        DropdownMenuItem(value: state, child: Text(state))
                      ).toList(),
                      onChanged: (value) {
                        setState(() {
                          selectedState = value!;
                        });
                      },
                    ),
                    SizedBox(height: 16),

                    // Phone Field
                    TextFormField(
                      controller: phoneController,
                      decoration: InputDecoration(
                        labelText: 'Phone Number',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      keyboardType: TextInputType.phone,
                    ),
                    SizedBox(height: 16),

                    // Use Map Button
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: () async {
                          await _useMapToGetLocation(
                            streetController,
                            landmarkController,
                            cityController,
                            lgaController,
                            (newState) => setState(() => selectedState = newState),
                          );
                        },
                        icon: Icon(Icons.map, size: 20),
                        label: Text('Use Map to Get Location'),
                        style: OutlinedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: isLoading ? null : () {
                    Navigator.of(dialogContext).pop();
                  },
                  child: Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: isLoading ? null : () async {
                    if (nameController.text.trim().isEmpty ||
                        streetController.text.trim().isEmpty ||
                        cityController.text.trim().isEmpty ||
                        lgaController.text.trim().isEmpty ||
                        phoneController.text.trim().isEmpty) {
                      Toast.error('Please fill in all required fields');
                      return;
                    }

                    setState(() {
                      isLoading = true;
                    });

                    await _saveNewAddress(
                      dialogContext,
                      selectedType,
                      nameController.text.trim(),
                      streetController.text.trim(),
                      landmarkController.text.trim(),
                      cityController.text.trim(),
                      lgaController.text.trim(),
                      selectedState,
                      phoneController.text.trim(),
                    );
                  },
                  child: isLoading
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                          ),
                        )
                      : Text('Save Address'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// Use map to get location details and auto-fill address fields
  Future<void> _useMapToGetLocation(
    TextEditingController streetController,
    TextEditingController landmarkController,
    TextEditingController cityController,
    TextEditingController lgaController,
    Function(String) updateState,
  ) async {
    try {
      // Get current location or let user pick a location on map
      final result = await Navigator.of(context).push<Map<String, dynamic>>(
        MaterialPageRoute(
          builder: (context) => LocationConfirmationMap(
            address: streetController.text.isNotEmpty ? streetController.text : 'Select Location',
            title: 'Select Address Location',
            initialCoordinates: null,
          ),
        ),
      );

      if (result != null && mounted) {
        final address = result['address'] as String? ?? '';
        final latitude = result['latitude'] as double?;
        final longitude = result['longitude'] as double?;

        if (address.isNotEmpty) {
          // Parse the address to extract components
          final addressParts = address.split(', ');

          if (addressParts.isNotEmpty) {
            // Try to intelligently parse the address
            streetController.text = addressParts[0];

            if (addressParts.length > 1) {
              // Look for Nigerian state names to determine state
              final nigerianStates = [
                'Lagos', 'Abuja', 'Kano', 'Rivers', 'Oyo', 'Delta', 'Kaduna',
                'Anambra', 'Edo', 'Ogun', 'Cross River', 'Akwa Ibom', 'Imo',
                'Plateau', 'Borno', 'Bauchi', 'Sokoto', 'Katsina', 'Adamawa',
                'Taraba', 'Kebbi', 'Niger', 'Gombe', 'Kwara', 'Zamfara',
                'Enugu', 'Abia', 'Ebonyi', 'Nasarawa', 'Yobe', 'Jigawa',
                'Benue', 'Kogi', 'Ondo', 'Osun', 'Ekiti', 'Bayelsa'
              ];

              String? detectedState;
              String? detectedCity;
              String? detectedLGA;

              for (int i = addressParts.length - 1; i >= 1; i--) {
                final part = addressParts[i].trim();

                // Check if this part contains a Nigerian state
                for (final state in nigerianStates) {
                  if (part.toLowerCase().contains(state.toLowerCase())) {
                    detectedState = state;
                    break;
                  }
                }

                // If we found a state, the previous parts might be city/LGA
                if (detectedState != null) {
                  if (i > 1) {
                    detectedCity = addressParts[i - 1].trim();
                  }
                  if (i > 2) {
                    detectedLGA = addressParts[i - 2].trim();
                  }
                  break;
                }
              }

              // Update fields with detected values
              if (detectedCity != null && detectedCity.isNotEmpty) {
                cityController.text = detectedCity;
              }

              if (detectedLGA != null && detectedLGA.isNotEmpty) {
                lgaController.text = detectedLGA;
              }

              if (detectedState != null) {
                updateState(detectedState);
              }

              // If we couldn't parse properly, put remaining parts in landmark
              if (addressParts.length > 1 && detectedCity == null) {
                landmarkController.text = addressParts.sublist(1).join(', ');
              }
            }
          }

          Toast.success('Location details filled from map');
        }
      }
    } catch (e) {
      Toast.error('Failed to get location from map: $e');
    }
  }

  Future<void> _saveNewAddress(
    BuildContext dialogContext,
    String type,
    String name,
    String street,
    String landmark,
    String city,
    String lga,
    String state,
    String phone,
  ) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        Toast.error('Authentication required');
        return;
      }

      // Parse phone number to int
      final phoneNumber = int.tryParse(phone.replaceAll(RegExp(r'[^\d]'), ''));
      if (phoneNumber == null) {
        Toast.error('Invalid phone number');
        return;
      }

      // Create proper AddressRequest
      // Include landmark in street field if provided
      final fullStreet = landmark.isNotEmpty ? '$street, $landmark' : street;
      // Include LGA in city field
      final fullCity = lga.isNotEmpty ? '$city, $lga' : city;

      final addressRequest = AddressRequest(
        name: name,
        phoneNumber: phoneNumber,
        street: fullStreet,
        city: fullCity,
        state: state,
        country: 'Nigeria',
        type: type,
      );

      final response = await AddressService.createAddress(
        addressRequest,
        authToken: authToken,
      );

      if (response.success) {
        // Close dialog
        Navigator.of(dialogContext).pop();

        // Refresh the address list
        if (mounted) {
          final addressProvider = Provider.of<AddressProvider>(context, listen: false);
          addressProvider.refresh();
        }

        Toast.success('Address added successfully');
      } else {
        Toast.error('Failed to add address: ${response.message}');
      }
    } catch (e) {
      Toast.error('Failed to add address: $e');
    }
  }

  /// Navigate to edit address page
  Future<void> _navigateToEditAddress(BuildContext context, Map<String, dynamic> address) async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => EditAddressView(address: address),
      ),
    );

    // Refresh the address list if the address was updated
    if (result == true && mounted) {
      Provider.of<AddressProvider>(context, listen: false).refresh();
    }
  }

  void _showEditAddressDialog(BuildContext context, Map<String, dynamic> address, int index) {
    // Debug: Print address data to understand the structure
    print('Edit Address Data: $address');

    // Pre-populate controllers with existing data
    final nameController = TextEditingController(text: address['name']?.toString() ?? '');
    final phoneController = TextEditingController(text: address['phoneNumber']?.toString() ?? '');

    // Parse street and landmark from the street field
    final streetData = address['street']?.toString() ?? '';
    final streetParts = streetData.split(', ');
    final streetController = TextEditingController(text: streetParts.isNotEmpty ? streetParts[0] : '');
    final landmarkController = TextEditingController(text: streetParts.length > 1 ? streetParts.sublist(1).join(', ') : '');

    // Parse city and LGA from the city field
    final cityData = address['city']?.toString() ?? '';
    final cityParts = cityData.split(', ');
    final cityController = TextEditingController(text: cityParts.isNotEmpty ? cityParts[0] : '');
    final lgaController = TextEditingController(text: cityParts.length > 1 ? cityParts.sublist(1).join(', ') : '');

    // Nigerian states for dropdown
    final nigerianStates = [
      'Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue',
      'Borno', 'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu',
      'FCT', 'Gombe', 'Imo', 'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi',
      'Kogi', 'Kwara', 'Lagos', 'Nasarawa', 'Niger', 'Ogun', 'Ondo', 'Osun',
      'Oyo', 'Plateau', 'Rivers', 'Sokoto', 'Taraba', 'Yobe', 'Zamfara'
    ];

    // Ensure the state value is valid for our dropdown
    final addressState = address['state']?.toString().trim() ?? 'Lagos';
    String selectedState = nigerianStates.contains(addressState) ? addressState : 'Lagos';
    print('Address State: "$addressState", Selected State: "$selectedState"');

    // Ensure the type value is valid for our dropdown
    final addressType = address['type']?.toString().toLowerCase().trim() ?? 'pickup';
    String selectedType;
    if (addressType == 'pickup' || addressType == 'receiver') {
      selectedType = addressType;
    } else if (addressType == 'delivery' || addressType == 'deliver') {
      selectedType = 'receiver'; // Map delivery variants to receiver
    } else {
      selectedType = 'pickup'; // Default fallback
    }
    print('Address Type: "$addressType", Selected Type: "$selectedType"');
    bool isLoading = false;

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(
                'Edit Address',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Poppins',
                  color: AppColors.black,
                ),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Address Type Selection
                    DropdownButtonFormField<String>(
                      value: selectedType,
                      decoration: InputDecoration(
                        labelText: 'Address Type',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      items: [
                        DropdownMenuItem(value: 'pickup', child: Text('Pickup Address')),
                        DropdownMenuItem(value: 'receiver', child: Text('Delivery Address')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          selectedType = value!;
                        });
                      },
                    ),
                    SizedBox(height: 16),

                    // Name Field
                    TextFormField(
                      controller: nameController,
                      decoration: InputDecoration(
                        labelText: selectedType == 'pickup' ? 'Sender Name' : 'Receiver Name',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    SizedBox(height: 16),

                    // Street Address Field
                    TextFormField(
                      controller: streetController,
                      decoration: InputDecoration(
                        labelText: 'Street Address',
                        hintText: 'e.g., 123 Main Street',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      maxLines: 2,
                    ),
                    SizedBox(height: 16),

                    // Landmark Field
                    TextFormField(
                      controller: landmarkController,
                      decoration: InputDecoration(
                        labelText: 'Landmark (Optional)',
                        hintText: 'e.g., Near City Mall',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    SizedBox(height: 16),

                    // City Field
                    TextFormField(
                      controller: cityController,
                      decoration: InputDecoration(
                        labelText: 'City',
                        hintText: 'e.g., Ikeja',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    SizedBox(height: 16),

                    // LGA Field
                    TextFormField(
                      controller: lgaController,
                      decoration: InputDecoration(
                        labelText: 'Local Government Area',
                        hintText: 'e.g., Ikeja LGA',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    SizedBox(height: 16),

                    // State Dropdown
                    DropdownButtonFormField<String>(
                      value: selectedState,
                      decoration: InputDecoration(
                        labelText: 'State',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      items: nigerianStates.map((state) =>
                        DropdownMenuItem(value: state, child: Text(state))
                      ).toList(),
                      onChanged: (value) {
                        setState(() {
                          selectedState = value!;
                        });
                      },
                    ),
                    SizedBox(height: 16),

                    // Phone Field
                    TextFormField(
                      controller: phoneController,
                      decoration: InputDecoration(
                        labelText: 'Phone Number',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      keyboardType: TextInputType.phone,
                    ),
                    SizedBox(height: 16),

                    // Use Map Button
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: () async {
                          await _useMapToGetLocation(
                            streetController,
                            landmarkController,
                            cityController,
                            lgaController,
                            (newState) => setState(() => selectedState = newState),
                          );
                        },
                        icon: Icon(Icons.map, size: 20),
                        label: Text('Use Map to Get Location'),
                        style: OutlinedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: isLoading ? null : () {
                    Navigator.of(dialogContext).pop();
                  },
                  child: Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: isLoading ? null : () async {
                    if (nameController.text.trim().isEmpty ||
                        streetController.text.trim().isEmpty ||
                        cityController.text.trim().isEmpty ||
                        lgaController.text.trim().isEmpty ||
                        phoneController.text.trim().isEmpty) {
                      Toast.error('Please fill in all required fields');
                      return;
                    }

                    setState(() {
                      isLoading = true;
                    });

                    await _updateAddress(
                      dialogContext,
                      address['uuid'],
                      selectedType,
                      nameController.text.trim(),
                      streetController.text.trim(),
                      landmarkController.text.trim(),
                      cityController.text.trim(),
                      lgaController.text.trim(),
                      selectedState,
                      phoneController.text.trim(),
                    );
                  },
                  child: isLoading
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                          ),
                        )
                      : Text('Update Address'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _updateAddress(
    BuildContext dialogContext,
    String addressUuid,
    String type,
    String name,
    String street,
    String landmark,
    String city,
    String lga,
    String state,
    String phone,
  ) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) {
        Toast.error('Authentication required');
        return;
      }

      // Parse phone number to int
      final phoneNumber = int.tryParse(phone.replaceAll(RegExp(r'[^\d]'), ''));
      if (phoneNumber == null) {
        Toast.error('Invalid phone number');
        return;
      }

      // Create proper AddressRequest
      // Include landmark in street field if provided
      final fullStreet = landmark.isNotEmpty ? '$street, $landmark' : street;
      // Include LGA in city field
      final fullCity = lga.isNotEmpty ? '$city, $lga' : city;

      final addressRequest = AddressRequest(
        name: name,
        phoneNumber: phoneNumber,
        street: fullStreet,
        city: fullCity,
        state: state,
        country: 'Nigeria',
        type: type,
      );

      final response = await AddressService.updateAddress(
        addressUuid,
        addressRequest,
        authToken: authToken,
      );

      if (response.success) {
        // Close dialog
        Navigator.of(dialogContext).pop();

        // Refresh the address list
        if (mounted) {
          final addressProvider = Provider.of<AddressProvider>(context, listen: false);
          addressProvider.refresh();
        }

        Toast.success('Address updated successfully');
      } else {
        Toast.error('Failed to update address: ${response.message}');
      }
    } catch (e) {
      Toast.error('Failed to update address: $e');
    }
  }

  void _showDeleteConfirmation(BuildContext context, Map<String, dynamic> address, int index) {
    PushNotificationDialog.show(
      context,
      title: 'Delete Address',
      description: 'Are you sure you want to delete "${_getAddressTitle(address)}" address? This action cannot be undone.',
      acceptButtonText: 'Delete',
      declineButtonText: 'Cancel',
      icon: Icons.delete_outline,
      iconBackgroundColor: AppColors.error,
      iconColor: AppColors.error,
      onAccept: () async {
        try {
          final authToken = await AuthService.getAuthToken();
          if (authToken != null) {
            final response = await AddressService.deleteAddress(address['id'], authToken: authToken);
            if (response.success) {
              // Refresh the list using Provider
              if (mounted) {
                final addressProvider = Provider.of<AddressProvider>(context, listen: false);
                addressProvider.refresh();
              }
              Toast.success('Address deleted successfully');
            } else {
              Toast.error('Failed to delete address: ${response.message}');
            }
          } else {
            Toast.error('Authentication required to delete address');
          }
        } catch (e) {
          Toast.error('Failed to delete address: $e');
        }
      },
      onDecline: () {
        // Dialog will close automatically
      },
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16;
    } else if (screenWidth > 600) {
      basePadding = 40;
    } else {
      basePadding = 24;
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6;
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2;
    } else {
      spacing = baseSpacing;
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8;
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2;
    } else {
      fontSize = baseFontSize;
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8;
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2;
    } else {
      iconSize = baseIconSize;
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6;
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2;
    } else {
      borderRadius = baseBorderRadius;
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }
}
