import 'package:flutter_test/flutter_test.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/models/auth/sign_up_request.dart';
import 'package:rideoon/models/auth/sign_up_response.dart';
import 'package:rideoon/models/auth/sign_in_request.dart';
import 'package:rideoon/models/auth/sign_in_response.dart';
import 'package:rideoon/models/auth/account.dart';
import 'package:rideoon/models/api_response.dart';

void main() {
  group('AuthService Tests', () {
    test('SignUpRequest should serialize to JSON correctly', () {
      final request = SignUpRequest(
        email: '<EMAIL>',
        password: 'password123',
        repeatedPassword: 'password123',
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '**********',
      );

      final json = request.toJson();

      expect(json['email'], equals('<EMAIL>'));
      expect(json['password'], equals('password123'));
      expect(json['repeatedPassword'], equals('password123'));
      expect(json['firstName'], equals('John'));
      expect(json['lastName'], equals('Doe'));
      expect(json['phoneNumber'], equals('**********'));
    });

    test('SignUpResponse should deserialize from JSON correctly', () {
      final json = {
        'success': true,
        'message': 'User created successfully',
        'userId': '12345',
        'token': 'abc123',
      };

      final response = SignUpResponse.fromJson(json);

      expect(response.success, isTrue);
      expect(response.message, equals('User created successfully'));
      expect(response.userId, equals('12345'));
      expect(response.token, equals('abc123'));
    });

    test('ApiResponse should handle success correctly', () {
      final response = ApiResponse<String>.success(
        message: 'Success',
        data: 'test data',
        statusCode: 200,
      );

      expect(response.success, isTrue);
      expect(response.message, equals('Success'));
      expect(response.data, equals('test data'));
      expect(response.statusCode, equals(200));
    });

    test('ApiResponse should handle error correctly', () {
      final response = ApiResponse<String>.error(
        message: 'Error occurred',
        statusCode: 400,
      );

      expect(response.success, isFalse);
      expect(response.message, equals('Error occurred'));
      expect(response.data, isNull);
      expect(response.statusCode, equals(400));
    });

    test('AuthService email validation should work correctly', () {
      expect(AuthService.isValidEmail('<EMAIL>'), isTrue);
      expect(AuthService.isValidEmail('<EMAIL>'), isTrue);
      expect(AuthService.isValidEmail('invalid-email'), isFalse);
      expect(AuthService.isValidEmail('test@'), isFalse);
      expect(AuthService.isValidEmail('@example.com'), isFalse);
    });

    test('AuthService phone validation should work correctly', () {
      // Nigerian local format (0XXXXXXXXX)
      expect(AuthService.isValidPhoneNumber('09155660116'), isTrue);
      expect(AuthService.isValidPhoneNumber('08012345678'), isTrue);
      expect(AuthService.isValidPhoneNumber('07012345678'), isTrue);
      expect(AuthService.isValidPhoneNumber('09012345678'), isTrue);

      // Nigerian international format (+234XXXXXXXXX)
      expect(AuthService.isValidPhoneNumber('+2349155660116'), isTrue);
      expect(AuthService.isValidPhoneNumber('+2348012345678'), isTrue);

      // Nigerian international without + (234XXXXXXXXX)
      expect(AuthService.isValidPhoneNumber('2349155660116'), isTrue);
      expect(AuthService.isValidPhoneNumber('2348012345678'), isTrue);

      // With formatting characters
      expect(AuthService.isValidPhoneNumber('091-5566-0116'), isTrue);
      expect(AuthService.isValidPhoneNumber('(091) 5566-0116'), isTrue);
      expect(AuthService.isValidPhoneNumber('+234 ************'), isTrue);

      // International formats
      expect(AuthService.isValidPhoneNumber('+**********'), isTrue);
      expect(AuthService.isValidPhoneNumber('+447700900123'), isTrue);

      // Invalid formats
      expect(AuthService.isValidPhoneNumber('123'), isFalse);
      expect(AuthService.isValidPhoneNumber('abc'), isFalse);
      expect(AuthService.isValidPhoneNumber('0123456'), isFalse); // Too short
      expect(AuthService.isValidPhoneNumber('0**********'), isFalse); // Invalid Nigerian prefix
      expect(AuthService.isValidPhoneNumber(''), isFalse);
    });

    test('AuthService password validation should work correctly', () {
      expect(AuthService.isValidPassword('password123'), isTrue);
      expect(AuthService.isValidPassword('123456'), isTrue);
      expect(AuthService.isValidPassword('12345'), isFalse);
      expect(AuthService.isValidPassword(''), isFalse);
    });

    test('SignInRequest should serialize to JSON correctly', () {
      final request = SignInRequest(
        email: '<EMAIL>',
        password: 'password123',
      );

      final json = request.toJson();

      expect(json['email'], equals('<EMAIL>'));
      expect(json['password'], equals('password123'));
    });

    test('Account should deserialize from JSON correctly', () {
      final json = {
        'uuid': 'df0921a1-261a-40ba-915c-8465d258892d',
        'created': '2024-11-05T14:00:00.151Z',
        'updated': '2024-11-05T14:00:00.151Z',
        'avatar': 'site/images/john.png',
        'firstName': 'Emma',
        'lastName': 'Watson',
        'phoneNumber': '**********',
        'email': '<EMAIL>',
        'address': 'df0921a1-261a-40ba-915c-8465d258892d',
        'state': true,
        'secured': false,
        'verified': true,
        'type': 'client',
      };

      final account = Account.fromJson(json);

      expect(account.uuid, equals('df0921a1-261a-40ba-915c-8465d258892d'));
      expect(account.firstName, equals('Emma'));
      expect(account.lastName, equals('Watson'));
      expect(account.fullName, equals('Emma Watson'));
      expect(account.email, equals('<EMAIL>'));
      expect(account.type, equals('client'));
      expect(account.isClient, isTrue);
      expect(account.isRider, isFalse);
      expect(account.verified, isTrue);
    });

    test('SignInResponse should deserialize from JSON correctly', () {
      final json = {
        'status': 200,
        'token': '64nc576t7r98ct7n6578wn90cmu8r99id97ty7nc7w09',
        'account': {
          'uuid': 'df0921a1-261a-40ba-915c-8465d258892d',
          'created': '2024-11-05T14:00:00.151Z',
          'updated': '2024-11-05T14:00:00.151Z',
          'avatar': 'site/images/john.png',
          'firstName': 'Emma',
          'lastName': 'Watson',
          'phoneNumber': '**********',
          'email': '<EMAIL>',
          'address': 'df0921a1-261a-40ba-915c-8465d258892d',
          'state': true,
          'secured': false,
          'verified': true,
          'type': 'client',
        },
      };

      final response = SignInResponse.fromJson(json);

      expect(response.status, equals(200));
      expect(response.token, equals('64nc576t7r98ct7n6578wn90cmu8r99id97ty7nc7w09'));
      expect(response.isSuccess, isTrue);
      expect(response.account.firstName, equals('Emma'));
      expect(response.account.isClient, isTrue);
    });

    test('Account should handle rider type correctly', () {
      final json = {
        'uuid': 'df0921a1-261a-40ba-915c-8465d258892d',
        'created': '2024-11-05T14:00:00.151Z',
        'updated': '2024-11-05T14:00:00.151Z',
        'firstName': 'John',
        'lastName': 'Rider',
        'phoneNumber': '**********',
        'email': '<EMAIL>',
        'state': true,
        'secured': false,
        'verified': true,
        'type': 'delivery',
      };

      final account = Account.fromJson(json);

      expect(account.type, equals('delivery'));
      expect(account.isClient, isFalse);
      expect(account.isRider, isTrue);
    });
  });
}
