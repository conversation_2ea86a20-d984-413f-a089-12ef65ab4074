import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/user_bottom_nav.dart';
import 'package:rideoon/views/dashboards/user_dashboard/home_view/home_view.dart';
import 'package:rideoon/views/dashboards/user_dashboard/shipment_view/shipment_view.dart';
import 'package:rideoon/views/dashboards/user_dashboard/track_view/track_view.dart';
import 'package:rideoon/views/dashboards/user_dashboard/account_view/account_view.dart';
import 'package:rideoon/views/dashboards/user_dashboard/account_view/customer_support_view.dart';

/// Main dashboard screen for users with bottom navigation
class UserDashboardScreens extends StatefulWidget {
  const UserDashboardScreens({super.key});

  @override
  State<UserDashboardScreens> createState() => _UserDashboardScreensState();
}

class _UserDashboardScreensState extends State<UserDashboardScreens> {
  int _selectedIndex = 0;

  // Navigation history tracking
  final List<int> _navigationHistory = [0]; // Start with home (index 0)

  // Callback for shipment filter
  VoidCallback? _shipmentFilterCallback;

  // Bottom navigation items
  final List<BottomNavItem> _navItems = [
    const BottomNavItem(
      icon: Icons.home_outlined,
      activeIcon: Icons.home,
      label: 'Home',
    ),
    const BottomNavItem(
      icon: Icons.local_shipping_outlined,
      activeIcon: Icons.local_shipping,
      label: 'Shipment',
    ),
    const BottomNavItem(
      icon: Icons.track_changes_outlined,
      activeIcon: Icons.track_changes,
      label: 'Track',
    ),
    const BottomNavItem(
      icon: Icons.account_circle_outlined,
      activeIcon: Icons.account_circle,
      label: 'Account',
    ),
  ];

  void _onNavItemTapped(int index) {
    setState(() {
      // Only add to history if it's a different page
      if (_selectedIndex != index) {
        _navigationHistory.add(index);
      }
      _selectedIndex = index;
    });
  }

  void _handleBackNavigation() {
    if (_navigationHistory.length > 1) {
      setState(() {
        // Remove current page from history
        _navigationHistory.removeLast();
        // Navigate to previous page
        int previousIndex = _navigationHistory.last;
        _selectedIndex = previousIndex;
      });
    }
  }

  bool _canNavigateBack() {
    return _navigationHistory.length > 1;
  }

  String _getPageTitle() {
    switch (_selectedIndex) {
      case 0:
        return 'Home';
      case 1:
        return 'Shipments';
      case 2:
        return 'Track order';
      case 3:
        return 'Account';
      default:
        return 'Home';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Stack(
        children: [
          // Main content
          _getSelectedPage(),

          // Header (only show if not on home screen and can navigate back)
          if (_selectedIndex != 0 && _canNavigateBack())
            Positioned(
              top: MediaQuery.of(context).padding.top + 16,
              left: 16,
              right: 16,
              child: _buildHeader(context),
            ),
        ],
      ),
      bottomNavigationBar: UserBottomNavigation(
        items: _navItems,
        currentIndex: _selectedIndex,
        onTap: _onNavItemTapped,
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        Container(
          height: 60,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              // Back button
              GestureDetector(
                onTap: _handleBackNavigation,
                child: Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Center(
                    child: Icon(
                      Icons.chevron_left,
                      size: 28,
                      color: AppColors.black.withValues(alpha: 0.8),
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // Title
              Expanded(
                child: Center(
                  child: Text(
                    _getPageTitle(),
                    style: TextStyle(
                      color: AppColors.black,
                      fontSize: 20,
                      fontFamily: 'Bricolage Grotesque',
                      fontWeight: FontWeight.w500,
                      letterSpacing: -0.80,
                    ),
                  ),
                ),
              ),

              // Page-specific action icon
              _buildPageActionIcon(context),
            ],
          ),
        ),

        // Underline
        Container(
          margin: EdgeInsets.symmetric(
            horizontal: MediaQuery.of(context).size.width * 0.05, // 5% from edges
          ),
          height: 1,
          decoration: BoxDecoration(
            color: AppColors.black.withValues(alpha: 0.1),
          ),
        ),
      ],
    );
  }

  Widget _buildPageActionIcon(BuildContext context) {
    switch (_selectedIndex) {
      case 1: // Shipment view - Filter icon
        return GestureDetector(
          onTap: () => _handleShipmentFilter(),
          child: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: AppColors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Center(
              child: Icon(
                Icons.tune,
                size: 20,
                color: AppColors.black.withValues(alpha: 0.8),
              ),
            ),
          ),
        );
      case 2: // Track view - Support icon
      case 3: // Account view - Support icon
        return GestureDetector(
          onTap: () => _handleSupport(),
          child: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: AppColors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Center(
              child: Icon(
                Icons.help_outline,
                size: 20,
                color: AppColors.black.withValues(alpha: 0.8),
              ),
            ),
          ),
        );
      default:
        return const SizedBox(width: 48); // Empty space to maintain alignment
    }
  }

  void _handleShipmentFilter() {
    // Trigger filter functionality in shipment view
    print('Filter button tapped'); // Debug
    if (_shipmentFilterCallback != null) {
      print('Calling filter callback'); // Debug
      _shipmentFilterCallback!();
    } else {
      print('Filter callback is null'); // Debug
    }
  }

  void _handleSupport() {
    // Navigate to customer support view
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CustomerSupportView(),
      ),
    );
  }

  Widget _getSelectedPage() {
    switch (_selectedIndex) {
      case 0:
        return HomeView(
          onNavigateToShipment: () => _onNavItemTapped(1), // Switch to shipment tab
        );
      case 1:
        return ShipmentView(
          onNavigateToHome: () => _onNavItemTapped(0), // Switch to home tab
          hasHeader: _selectedIndex != 0 && _canNavigateBack(), // Pass header state
          onFilterCallback: (callback) => _shipmentFilterCallback = callback, // Set filter callback
        );
      case 2:
        return TrackView(
          onNavigateToHome: () => _onNavItemTapped(0), // Switch to home tab
          hasHeader: _selectedIndex != 0 && _canNavigateBack(), // Pass header state
        );
      case 3:
        return AccountView(
          hasHeader: _selectedIndex != 0 && _canNavigateBack(), // Pass header state
        );
      default:
        return HomeView(
          onNavigateToShipment: () => _onNavItemTapped(1), // Switch to shipment tab
        );
    }
  }
}


