import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';

/// Toast provider that wraps the entire app and provides toast functionality
/// 
/// This provider creates an overlay that can show toast messages from anywhere
/// in the app without context issues.
class ToastProvider extends StatefulWidget {
  final Widget child;

  const ToastProvider({
    super.key,
    required this.child,
  });

  @override
  State<ToastProvider> createState() => _ToastProviderState();

  /// Get the toast provider instance from context
  static _ToastProviderState? of(BuildContext context) {
    return context.findAncestorStateOfType<_ToastProviderState>();
  }
}

class _ToastProviderState extends State<ToastProvider>
    with TickerProviderStateMixin {
  final List<_ToastEntry> _toasts = [];
  static _ToastProviderState? _instance;

  @override
  void initState() {
    super.initState();
    _instance = this;
  }

  @override
  void dispose() {
    _instance = null;
    super.dispose();
  }

  /// Show a toast message
  void showToast({
    required String message,
    required Color backgroundColor,
    required IconData icon,
    Duration? duration,
    Color? textColor,
    Color? iconColor,
  }) {
    final entry = _ToastEntry(
      message: message,
      backgroundColor: backgroundColor,
      icon: icon,
      duration: duration ?? const Duration(seconds: 3),
      textColor: textColor ?? AppColors.white,
      iconColor: iconColor ?? AppColors.white,
      onRemove: _removeToast,
    );

    setState(() {
      _toasts.add(entry);
    });

    // Auto remove after duration
    Future.delayed(entry.duration, () {
      _removeToast(entry);
    });
  }

  void _removeToast(_ToastEntry entry) {
    if (mounted) {
      setState(() {
        _toasts.remove(entry);
      });
    }
  }

  /// Remove all toasts
  void removeAllToasts() {
    if (mounted) {
      setState(() {
        _toasts.clear();
      });
    }
  }

  /// Static methods for global access
  static void success(String message, {Duration? duration}) {
    _instance?.showToast(
      message: message,
      backgroundColor: AppColors.success,
      icon: Icons.check_circle,
      duration: duration,
    );
  }

  static void error(String message, {Duration? duration}) {
    _instance?.showToast(
      message: message,
      backgroundColor: AppColors.error,
      icon: Icons.error,
      duration: duration,
    );
  }

  static void warning(String message, {Duration? duration}) {
    _instance?.showToast(
      message: message,
      backgroundColor: AppColors.warning,
      icon: Icons.warning,
      duration: duration,
    );
  }

  static void info(String message, {Duration? duration}) {
    _instance?.showToast(
      message: message,
      backgroundColor: AppColors.primary,
      icon: Icons.info,
      duration: duration,
    );
  }

  static void custom({
    required String message,
    required Color backgroundColor,
    required IconData icon,
    Duration? duration,
    Color? textColor,
    Color? iconColor,
  }) {
    _instance?.showToast(
      message: message,
      backgroundColor: backgroundColor,
      icon: icon,
      duration: duration,
      textColor: textColor,
      iconColor: iconColor,
    );
  }

  static void removeAll() {
    _instance?.removeAllToasts();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topCenter, // Use explicit alignment instead of directional
      children: [
        widget.child,
        // Toast overlay
        Positioned(
          top: MediaQuery.of(context).padding.top + 16,
          left: 16,
          right: 16,
          child: Column(
            children: _toasts.map((toast) => toast.build(context)).toList(),
          ),
        ),
      ],
    );
  }
}

/// Individual toast entry
class _ToastEntry {
  final String message;
  final Color backgroundColor;
  final IconData icon;
  final Duration duration;
  final Color textColor;
  final Color iconColor;
  final Function(_ToastEntry) onRemove;

  _ToastEntry({
    required this.message,
    required this.backgroundColor,
    required this.icon,
    required this.duration,
    required this.textColor,
    required this.iconColor,
    required this.onRemove,
  });

  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: AppColors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: iconColor,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: TextStyle(
                  color: textColor,
                  fontSize: 14,
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            GestureDetector(
              onTap: () => onRemove(this),
              child: Icon(
                Icons.close,
                color: iconColor.withValues(alpha: 0.7),
                size: 18,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Global toast instance for easy access
class Toast {
  static void success(String message, {Duration? duration}) {
    _ToastProviderState.success(message, duration: duration);
  }

  static void error(String message, {Duration? duration}) {
    _ToastProviderState.error(message, duration: duration);
  }

  static void warning(String message, {Duration? duration}) {
    _ToastProviderState.warning(message, duration: duration);
  }

  static void info(String message, {Duration? duration}) {
    _ToastProviderState.info(message, duration: duration);
  }

  static void custom({
    required String message,
    required Color backgroundColor,
    required IconData icon,
    Duration? duration,
    Color? textColor,
    Color? iconColor,
  }) {
    _ToastProviderState.custom(
      message: message,
      backgroundColor: backgroundColor,
      icon: icon,
      duration: duration,
      textColor: textColor,
      iconColor: iconColor,
    );
  }

  static void removeAll() {
    _ToastProviderState.removeAll();
  }
}
