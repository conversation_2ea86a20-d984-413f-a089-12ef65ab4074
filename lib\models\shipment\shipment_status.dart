import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';

/// Shipment status enum with comprehensive status tracking
enum ShipmentStatus {
  // Initial states
  pending('Pending', 'Order has been placed and is awaiting processing'),
  confirmed('Confirmed', 'Order has been confirmed and is being prepared'),
  
  // Pickup states
  pickupScheduled('Pickup Scheduled', 'Pickup has been scheduled'),
  pickupInProgress('Pickup in Progress', 'Driver is on the way to pickup location'),
  pickedUp('Picked Up', 'Package has been picked up from sender'),
  
  // Transit states
  inTransit('In Transit', 'Package is on its way to destination'),
  atSortingFacility('At Sorting Facility', 'Package is at sorting facility'),
  outForDelivery('Out for Delivery', 'Package is out for delivery'),
  
  // Delivery states
  delivered('Delivered', 'Package has been successfully delivered'),
  partiallyDelivered('Partially Delivered', 'Some items have been delivered'),
  
  // Exception states
  delayed('Delayed', 'Delivery has been delayed'),
  failed('Failed Delivery', 'Delivery attempt failed'),
  returned('Returned', 'Package is being returned to sender'),
  
  // Final states
  completed('Completed', 'Order has been completed successfully'),
  cancelled('Cancelled', 'Order has been cancelled'),
  refunded('Refunded', 'Order has been refunded');

  const ShipmentStatus(this.displayName, this.description);
  final String displayName;
  final String description;

  /// Get status color for UI display
  Color get color {
    switch (this) {
      case ShipmentStatus.pending:
      case ShipmentStatus.confirmed:
      case ShipmentStatus.pickupScheduled:
        return AppColors.pending;
      
      case ShipmentStatus.pickupInProgress:
      case ShipmentStatus.pickedUp:
      case ShipmentStatus.inTransit:
      case ShipmentStatus.atSortingFacility:
      case ShipmentStatus.outForDelivery:
        return AppColors.warning;
      
      case ShipmentStatus.delivered:
      case ShipmentStatus.completed:
        return AppColors.success;
      
      case ShipmentStatus.partiallyDelivered:
        return AppColors.accept;
      
      case ShipmentStatus.delayed:
      case ShipmentStatus.failed:
        return AppColors.warning;
      
      case ShipmentStatus.returned:
      case ShipmentStatus.cancelled:
      case ShipmentStatus.refunded:
        return AppColors.error;
    }
  }

  /// Get text color for status display
  Color get textColor {
    switch (this) {
      case ShipmentStatus.returned:
      case ShipmentStatus.cancelled:
      case ShipmentStatus.refunded:
        return AppColors.white;
      default:
        return const Color(0xFF1E1E1E);
    }
  }

  /// Get icon for status display
  IconData get icon {
    switch (this) {
      case ShipmentStatus.pending:
      case ShipmentStatus.confirmed:
        return Icons.schedule;
      
      case ShipmentStatus.pickupScheduled:
      case ShipmentStatus.pickupInProgress:
        return Icons.local_shipping;
      
      case ShipmentStatus.pickedUp:
      case ShipmentStatus.inTransit:
        return Icons.flight_takeoff;
      
      case ShipmentStatus.atSortingFacility:
        return Icons.warehouse;
      
      case ShipmentStatus.outForDelivery:
        return Icons.delivery_dining;
      
      case ShipmentStatus.delivered:
      case ShipmentStatus.completed:
        return Icons.check_circle;
      
      case ShipmentStatus.partiallyDelivered:
        return Icons.check_circle_outline;
      
      case ShipmentStatus.delayed:
        return Icons.access_time;
      
      case ShipmentStatus.failed:
        return Icons.error_outline;
      
      case ShipmentStatus.returned:
        return Icons.keyboard_return;
      
      case ShipmentStatus.cancelled:
        return Icons.cancel;
      
      case ShipmentStatus.refunded:
        return Icons.money_off;
    }
  }

  /// Check if status indicates shipment is in progress
  bool get isInProgress {
    return [
      ShipmentStatus.confirmed,
      ShipmentStatus.pickupScheduled,
      ShipmentStatus.pickupInProgress,
      ShipmentStatus.pickedUp,
      ShipmentStatus.inTransit,
      ShipmentStatus.atSortingFacility,
      ShipmentStatus.outForDelivery,
    ].contains(this);
  }

  /// Check if status indicates shipment is completed
  bool get isCompleted {
    return [
      ShipmentStatus.delivered,
      ShipmentStatus.completed,
    ].contains(this);
  }

  /// Check if status indicates shipment has issues
  bool get hasIssues {
    return [
      ShipmentStatus.delayed,
      ShipmentStatus.failed,
      ShipmentStatus.returned,
      ShipmentStatus.cancelled,
      ShipmentStatus.refunded,
    ].contains(this);
  }

  /// Check if status allows cancellation
  bool get canBeCancelled {
    return [
      ShipmentStatus.pending,
      ShipmentStatus.confirmed,
      ShipmentStatus.pickupScheduled,
    ].contains(this);
  }

  /// Check if status allows tracking
  bool get canBeTracked {
    return ![
      ShipmentStatus.pending,
      ShipmentStatus.cancelled,
      ShipmentStatus.refunded,
    ].contains(this);
  }

  /// Get status from string
  static ShipmentStatus fromString(String value) {
    return ShipmentStatus.values.firstWhere(
      (status) => status.displayName.toLowerCase() == value.toLowerCase() ||
                  status.name.toLowerCase() == value.toLowerCase(),
      orElse: () => ShipmentStatus.pending,
    );
  }

  /// Get next possible statuses for workflow
  List<ShipmentStatus> get nextPossibleStatuses {
    switch (this) {
      case ShipmentStatus.pending:
        return [ShipmentStatus.confirmed, ShipmentStatus.cancelled];
      
      case ShipmentStatus.confirmed:
        return [ShipmentStatus.pickupScheduled, ShipmentStatus.cancelled];
      
      case ShipmentStatus.pickupScheduled:
        return [ShipmentStatus.pickupInProgress, ShipmentStatus.delayed, ShipmentStatus.cancelled];
      
      case ShipmentStatus.pickupInProgress:
        return [ShipmentStatus.pickedUp, ShipmentStatus.failed];
      
      case ShipmentStatus.pickedUp:
        return [ShipmentStatus.inTransit, ShipmentStatus.atSortingFacility];
      
      case ShipmentStatus.inTransit:
        return [ShipmentStatus.atSortingFacility, ShipmentStatus.outForDelivery, ShipmentStatus.delayed];
      
      case ShipmentStatus.atSortingFacility:
        return [ShipmentStatus.outForDelivery, ShipmentStatus.delayed];
      
      case ShipmentStatus.outForDelivery:
        return [ShipmentStatus.delivered, ShipmentStatus.failed, ShipmentStatus.delayed];
      
      case ShipmentStatus.delivered:
        return [ShipmentStatus.completed, ShipmentStatus.returned];
      
      case ShipmentStatus.failed:
        return [ShipmentStatus.outForDelivery, ShipmentStatus.returned];
      
      case ShipmentStatus.delayed:
        return [ShipmentStatus.outForDelivery, ShipmentStatus.cancelled];
      
      default:
        return [];
    }
  }
}
