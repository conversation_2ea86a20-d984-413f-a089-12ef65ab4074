import 'package:rideoon/models/core/address.dart';
import 'package:rideoon/models/shipment/shipment_status.dart';

/// Tracking event model for shipment tracking
class TrackingEvent {
  final String id;
  final ShipmentStatus status;
  final String title;
  final String? description;
  final DateTime timestamp;
  final Address? location;
  final String? driverName;
  final String? driverPhone;
  final Map<String, dynamic>? metadata;

  const TrackingEvent({
    required this.id,
    required this.status,
    required this.title,
    this.description,
    required this.timestamp,
    this.location,
    this.driverName,
    this.driverPhone,
    this.metadata,
  });

  /// Get formatted timestamp for display
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }

  /// Get detailed timestamp for display
  String get detailedTime {
    return '${timestamp.day}/${timestamp.month}/${timestamp.year} at ${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
  }

  /// Check if event has location information
  bool get hasLocation => location != null;

  /// Check if event has driver information
  bool get hasDriverInfo => driverName?.isNotEmpty == true || driverPhone?.isNotEmpty == true;

  /// Create TrackingEvent from JSON
  factory TrackingEvent.fromJson(Map<String, dynamic> json) {
    return TrackingEvent(
      id: json['id'] as String? ?? '',
      status: ShipmentStatus.fromString(json['status'] as String? ?? 'pending'),
      title: json['title'] as String? ?? '',
      description: json['description'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String? ?? DateTime.now().toIso8601String()),
      location: json['location'] != null 
          ? Address.fromJson(json['location'] as Map<String, dynamic>)
          : null,
      driverName: json['driverName'] as String?,
      driverPhone: json['driverPhone'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert TrackingEvent to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'status': status.name,
      'title': title,
      if (description != null) 'description': description,
      'timestamp': timestamp.toIso8601String(),
      if (location != null) 'location': location!.toJson(),
      if (driverName != null) 'driverName': driverName,
      if (driverPhone != null) 'driverPhone': driverPhone,
      if (metadata != null) 'metadata': metadata,
    };
  }

  /// Create a copy with updated fields
  TrackingEvent copyWith({
    String? id,
    ShipmentStatus? status,
    String? title,
    String? description,
    DateTime? timestamp,
    Address? location,
    String? driverName,
    String? driverPhone,
    Map<String, dynamic>? metadata,
  }) {
    return TrackingEvent(
      id: id ?? this.id,
      status: status ?? this.status,
      title: title ?? this.title,
      description: description ?? this.description,
      timestamp: timestamp ?? this.timestamp,
      location: location ?? this.location,
      driverName: driverName ?? this.driverName,
      driverPhone: driverPhone ?? this.driverPhone,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'TrackingEvent(status: ${status.displayName}, title: $title, time: $formattedTime)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TrackingEvent &&
        other.id == id &&
        other.status == status &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode {
    return Object.hash(id, status, timestamp);
  }
}

/// Tracking timeline model for complete shipment tracking
class TrackingTimeline {
  final String shipmentId;
  final List<TrackingEvent> events;
  final ShipmentStatus currentStatus;
  final DateTime lastUpdated;
  final double? estimatedProgress; // 0.0 to 1.0

  const TrackingTimeline({
    required this.shipmentId,
    required this.events,
    required this.currentStatus,
    required this.lastUpdated,
    this.estimatedProgress,
  });

  /// Get the latest tracking event
  TrackingEvent? get latestEvent {
    if (events.isEmpty) return null;
    return events.reduce((a, b) => a.timestamp.isAfter(b.timestamp) ? a : b);
  }

  /// Get events sorted by timestamp (newest first)
  List<TrackingEvent> get sortedEvents {
    final sorted = List<TrackingEvent>.from(events);
    sorted.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return sorted;
  }

  /// Get events sorted by timestamp (oldest first)
  List<TrackingEvent> get chronologicalEvents {
    final sorted = List<TrackingEvent>.from(events);
    sorted.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    return sorted;
  }

  /// Check if shipment is currently being tracked
  bool get isActive => currentStatus.canBeTracked;

  /// Get estimated delivery progress as percentage
  int get progressPercentage {
    if (estimatedProgress != null) {
      return (estimatedProgress! * 100).round();
    }
    
    // Calculate based on status
    switch (currentStatus) {
      case ShipmentStatus.pending:
      case ShipmentStatus.confirmed:
        return 10;
      case ShipmentStatus.pickupScheduled:
        return 20;
      case ShipmentStatus.pickupInProgress:
        return 30;
      case ShipmentStatus.pickedUp:
        return 40;
      case ShipmentStatus.inTransit:
        return 60;
      case ShipmentStatus.atSortingFacility:
        return 70;
      case ShipmentStatus.outForDelivery:
        return 90;
      case ShipmentStatus.delivered:
      case ShipmentStatus.completed:
        return 100;
      default:
        return 0;
    }
  }

  /// Create TrackingTimeline from JSON
  factory TrackingTimeline.fromJson(Map<String, dynamic> json) {
    return TrackingTimeline(
      shipmentId: json['shipmentId'] as String? ?? '',
      events: (json['events'] as List<dynamic>?)
          ?.map((e) => TrackingEvent.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      currentStatus: ShipmentStatus.fromString(json['currentStatus'] as String? ?? 'pending'),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String? ?? DateTime.now().toIso8601String()),
      estimatedProgress: (json['estimatedProgress'] as num?)?.toDouble(),
    );
  }

  /// Convert TrackingTimeline to JSON
  Map<String, dynamic> toJson() {
    return {
      'shipmentId': shipmentId,
      'events': events.map((e) => e.toJson()).toList(),
      'currentStatus': currentStatus.name,
      'lastUpdated': lastUpdated.toIso8601String(),
      if (estimatedProgress != null) 'estimatedProgress': estimatedProgress,
    };
  }

  /// Add a new tracking event
  TrackingTimeline addEvent(TrackingEvent event) {
    final updatedEvents = List<TrackingEvent>.from(events);
    updatedEvents.add(event);
    
    return TrackingTimeline(
      shipmentId: shipmentId,
      events: updatedEvents,
      currentStatus: event.status,
      lastUpdated: event.timestamp,
      estimatedProgress: estimatedProgress,
    );
  }

  /// Create a copy with updated fields
  TrackingTimeline copyWith({
    String? shipmentId,
    List<TrackingEvent>? events,
    ShipmentStatus? currentStatus,
    DateTime? lastUpdated,
    double? estimatedProgress,
  }) {
    return TrackingTimeline(
      shipmentId: shipmentId ?? this.shipmentId,
      events: events ?? this.events,
      currentStatus: currentStatus ?? this.currentStatus,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      estimatedProgress: estimatedProgress ?? this.estimatedProgress,
    );
  }

  @override
  String toString() {
    return 'TrackingTimeline(shipmentId: $shipmentId, status: ${currentStatus.displayName}, events: ${events.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TrackingTimeline &&
        other.shipmentId == shipmentId &&
        other.currentStatus == currentStatus &&
        other.lastUpdated == lastUpdated;
  }

  @override
  int get hashCode {
    return Object.hash(shipmentId, currentStatus, lastUpdated);
  }
}
