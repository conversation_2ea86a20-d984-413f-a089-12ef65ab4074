import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:rideoon/views/onboarding/onboarding_container.dart';
import 'package:rideoon/views/authentication/userauth/sign_in.dart';
import 'package:rideoon/views/authentication/userauth/sign_up.dart';
import 'package:rideoon/views/authentication/riderauth/sign_in.dart';
import 'package:rideoon/views/dashboards/user_dashboard/dashboard_screens.dart';
import 'package:rideoon/views/dashboards/rider_dashboard/dashboard_screens.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/providers/auth_provider.dart';
import 'package:rideoon/models/auth/sign_in_request.dart';

class OnboardingWrapper extends StatefulWidget {
  const OnboardingWrapper({super.key});

  @override
  State<OnboardingWrapper> createState() => _OnboardingWrapperState();
}

class _OnboardingWrapperState extends State<OnboardingWrapper> {
  bool _isLoading = true;
  bool _isFirstTime = true;
  bool _hasCompletedOnboarding = false;
  String? _userType;

  @override
  void initState() {
    super.initState();
    _checkAppState();
  }

  Future<void> _checkAppState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isFirstTime = prefs.getBool('is_first_time') ?? true;
      final hasCompletedOnboarding = prefs.getBool('has_completed_onboarding') ?? false;
      final userType = prefs.getString('user_type');

      // Check if remember me is enabled and try auto-login
      final isRememberMeEnabled = await AuthService.isRememberMeEnabled();
      if (isRememberMeEnabled && !isFirstTime) {
        final success = await _attemptAutoLogin();
        if (success) {
          return; // Auto-login successful, navigation handled in _attemptAutoLogin
        }
      }

      setState(() {
        _isFirstTime = isFirstTime;
        _hasCompletedOnboarding = hasCompletedOnboarding;
        _userType = userType;
        _isLoading = false;
      });
    } catch (e) {
      // Handle error gracefully
      setState(() {
        _isFirstTime = true;
        _hasCompletedOnboarding = false;
        _isLoading = false;
      });
    }
  }

  Future<bool> _attemptAutoLogin() async {
    try {
      // Check if AuthProvider already has authentication state
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      if (authProvider.isAuthenticated) {
        // User is already authenticated, navigate to dashboard
        final account = authProvider.currentUser!;
        if (mounted) {
          if (account.isClient) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => const UserDashboardScreens(),
              ),
            );
          } else if (account.isRider) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => const RiderDashboardScreens(),
              ),
            );
          } else {
            // Default to user dashboard if type is unclear
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => const UserDashboardScreens(),
              ),
            );
          }
          return true;
        }
      }

      // If not authenticated, try remember me credentials
      final credentials = await AuthService.getRememberMeCredentials();
      if (credentials != null) {
        final signInRequest = SignInRequest(
          email: credentials['email']!,
          password: credentials['password']!,
        );

        // Use AuthProvider for sign in
        final success = await authProvider.signIn(signInRequest, rememberMe: true);

        if (success) {
          final account = authProvider.currentUser!;
          if (mounted) {
            if (account.isClient) {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => const UserDashboardScreens(),
                ),
              );
            } else if (account.isRider) {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => const RiderDashboardScreens(),
                ),
              );
            } else {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => const UserDashboardScreens(),
                ),
              );
            }
            return true;
          }
        }
      }
    } catch (e) {
      // Auto-login failed, clear remember me credentials
      await AuthService.clearRememberMeCredentials();
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // If it's the first time, show onboarding
    if (_isFirstTime) {
      return const OnboardingContainer();
    }

    // If user has completed onboarding, go to sign up
    if (_hasCompletedOnboarding) {
      return const UserSignUp();
    }

    // If user has not completed onboarding but is not first time, go to sign in
    // This handles users who have opened the app before but didn't complete onboarding
    if (!_isFirstTime && !_hasCompletedOnboarding) {
      return const UserSignIn();
    }

    // For riders (if we ever need this in the future)
    if (_userType == 'rider') {
      return const RiderSignIn();
    }

    // Default fallback to onboarding
    return const OnboardingContainer();
  }
}

class OnboardingService {
  static const String _isFirstTimeKey = 'is_first_time';
  static const String _userTypeKey = 'user_type';
  static const String _hasCompletedOnboardingKey = 'has_completed_onboarding';

  /// Check if this is the first time the user is opening the app
  static Future<bool> isFirstTime() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isFirstTimeKey) ?? true;
  }

  /// Mark that the user has seen the app before
  static Future<void> setNotFirstTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isFirstTimeKey, false);
  }

  /// Save the user type (client or rider)
  static Future<void> setUserType(String userType) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userTypeKey, userType);
  }

  /// Get the saved user type
  static Future<String?> getUserType() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userTypeKey);
  }

  /// Mark that the user has completed onboarding
  static Future<void> setOnboardingCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_hasCompletedOnboardingKey, true);
    await setNotFirstTime();
  }

  /// Check if the user has completed onboarding
  static Future<bool> hasCompletedOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_hasCompletedOnboardingKey) ?? false;
  }

  /// Clear all onboarding data (useful for testing or logout)
  static Future<void> clearOnboardingData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_isFirstTimeKey);
    await prefs.remove(_userTypeKey);
    await prefs.remove(_hasCompletedOnboardingKey);
  }

  /// Reset to first time user (useful for testing)
  static Future<void> resetToFirstTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isFirstTimeKey, true);
    await prefs.remove(_userTypeKey);
    await prefs.remove(_hasCompletedOnboardingKey);
  }

  /// Mark that user has completed sign up (for future navigation to sign in)
  static Future<void> setSignUpCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('has_signed_up', true);
  }

  /// Check if user has completed sign up
  static Future<bool> hasSignedUp() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('has_signed_up') ?? false;
  }
}