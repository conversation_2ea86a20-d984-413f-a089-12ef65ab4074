import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:rideoon/services/config_service.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'package:rideoon/providers/address_provider.dart';
import 'package:rideoon/providers/auth_provider.dart';
import 'package:rideoon/providers/order_provider.dart';
import 'package:rideoon/providers/package_provider.dart';
import 'package:rideoon/providers/package_data_provider.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/dashboards/user_dashboard/cargo_management/add_cargo_items_view.dart';
import 'package:rideoon/views/dashboards/user_dashboard/cargo_management/add_single_cargo_item_view.dart';
import 'package:rideoon/views/dashboards/user_dashboard/checkout_and_cargo_view/checkout_view.dart';
import 'package:rideoon/views/dashboards/user_dashboard/order_management/orders_summary_view.dart';
import 'package:rideoon/views/onboarding/splash_screen.dart';

void main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize configuration service
  await ConfigService.initialize();

  // Validate required environment variables
  ConfigService.validateRequiredVariables();

  runApp(const RideOnApp());
}

class RideOnApp extends StatelessWidget {
  const RideOnApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Initialize AuthProvider and load stored auth data
        ChangeNotifierProvider(
          create: (context) {
            final authProvider = AuthProvider();
            // Initialize auth state asynchronously
            authProvider.initializeAuth();
            return authProvider;
          },
        ),
        // Initialize AddressProvider
        ChangeNotifierProvider(
          create: (context) => AddressProvider(),
        ),
        // Initialize OrderProvider
        ChangeNotifierProvider(
          create: (context) => OrderProvider(),
        ),
        // Initialize PackageProvider
        ChangeNotifierProvider(
          create: (context) => PackageProvider(),
        ),
        // Initialize PackageDataProvider
        ChangeNotifierProvider(
          create: (context) => PackageDataProvider(),
        ),
      ],
      child: ToastProvider(
        child: MaterialApp(
          title: 'RideOn',
          debugShowCheckedModeBanner: false,
          routes: {
            '/add-cargo-items': (context) => const AddCargoItemsView(),
            '/add-single-cargo-item': (context) => const AddSingleCargoItemView(),
            '/orders-summary': (context) => const OrdersSummaryView(),
            '/checkout': (context) => const CheckoutView(),
          },
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: AppColors.primary,
            brightness: Brightness.light,
          ),
          useMaterial3: true,
          fontFamily: 'Poppins',
          appBarTheme: const AppBarTheme(
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.white,
            elevation: 0,
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          textButtonTheme: TextButtonThemeData(
            style: TextButton.styleFrom(
              foregroundColor: AppColors.primary,
            ),
          ),
          inputDecorationTheme: InputDecorationTheme(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.primary),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
          ),
        ),
        home: const SplashScreen(),
        ),
      ),
    );
  }
}
