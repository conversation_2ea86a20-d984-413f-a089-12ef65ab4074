import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:rideoon/services/map_service.dart';
import 'package:rideoon/services/package_data_service.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
// import 'package:rideoon/views/custom_widgets/push_notfication.dart';
import 'package:rideoon/providers/toast_provider.dart';
import 'dart:math' as math;
import 'dart:io';
import 'dart:ui' as ui;
import 'dart:typed_data';
import 'package:flutter/services.dart';

/// Full-screen map view for live mapping
class FullScreenMapView extends StatefulWidget {
  const FullScreenMapView({super.key});

  @override
  State<FullScreenMapView> createState() => _FullScreenMapViewState();
}

class _FullScreenMapViewState extends State<FullScreenMapView> {
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  CameraPosition _initialCameraPosition = const CameraPosition(
    target: LatLng(6.5244, 3.3792), // Lagos, Nigeria default
    zoom: 12.0,
  );
  MapType _currentMapType = MapType.normal;

  // Pickup points data
  List<Map<String, dynamic>> _pickupPoints = [];
  Set<Marker> _pickupMarkers = {};
  LatLng? _currentUserLocation;

  // Route data
  Set<Polyline> _routePolylines = {};
  Map<String, dynamic>? _activeDelivery;
  GoogleMapController? _mapController;
  bool _isPickupCompleted = false;
  Set<Marker> _currentLocationMarkers = {};

  @override
  void initState() {
    super.initState();
    _initializeMap();
    _loadPickupPoints();
  }

  Future<void> _initializeMap() async {
    try {
      await MapService.initialize();
      final cameraPosition = await MapService.getCurrentLocationCameraPosition(zoom: 16.0);

      // Store current user location
      _currentUserLocation = cameraPosition.target;

      // Add custom current location marker
      await _addCustomCurrentLocationMarker();

      if (mounted) {
        setState(() {
          _initialCameraPosition = cameraPosition;
          _isLoading = false;
          _hasError = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = e.toString();
        });
      }
    }
  }

  /// Load pickup points from available deliveries
  Future<void> _loadPickupPoints() async {
    try {
      // Load completed orders from PackageDataService
      final completedOrders = await PackageDataService.getCompletedOrders();

      // Filter for pending/available orders only
      final availableOrders = completedOrders.where((order) {
        final status = order['status'] ?? 'pending';
        return status == 'pending' || status == 'available';
      }).toList();

      final pickupPoints = <Map<String, dynamic>>[];
      final markers = <Marker>{};

      for (final order in availableOrders) {
        final pickupData = order['pickupData'] as Map<String, dynamic>? ?? {};

        // Only add if we have coordinates
        if (pickupData['latitude'] != null && pickupData['longitude'] != null) {
          final lat = pickupData['latitude'] as double;
          final lng = pickupData['longitude'] as double;
          final location = LatLng(lat, lng);

          // Calculate distance from current location
          double distance = 0.0;
          if (_currentUserLocation != null) {
            distance = _calculateDistance(_currentUserLocation!, location);
          }

          final pickupPoint = {
            'id': order['id'],
            'location': location,
            'distance': distance,
            'orderData': order,
            'pickupData': pickupData,
            'senderName': pickupData['senderName'] ?? 'Unknown Sender',
            'address': pickupData['fullAddress'] ?? 'No address',
            'phone': pickupData['phone'] ?? 'No phone',
            'state': pickupData['state'] ?? 'Unknown State',
          };

          pickupPoints.add(pickupPoint);

          // Create marker for this pickup point
          final marker = Marker(
            markerId: MarkerId('pickup_${order['id']}'),
            position: location,
            icon: await _createPickupMarkerIcon(),
            infoWindow: InfoWindow(
              title: pickupData['senderName'] ?? 'Pickup Point',
              snippet: '${distance.toStringAsFixed(1)} km away',
            ),
            onTap: () => _showPickupPointDialog(pickupPoint),
          );

          markers.add(marker);
        }
      }

      if (mounted) {
        setState(() {
          _pickupPoints = pickupPoints;
          _pickupMarkers = markers;
        });
      }
    } catch (e) {
      print('Error loading pickup points: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Full-screen map
          if (_isLoading)
            Container(
              color: const Color(0xFFF5F5FF),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: AppColors.primary,
                      strokeWidth: 3,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Loading live map...',
                      style: TextStyle(
                        fontSize: 16,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w400,
                        color: AppColors.black.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            )
          else if (_hasError)
            Container(
              color: const Color(0xFFF5F5FF),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: AppColors.error.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.error_outline,
                        size: 64,
                        color: AppColors.error,
                      ),
                    ),
                    const SizedBox(height: 24),
                    Text(
                      'Map Error',
                      style: TextStyle(
                        fontSize: 24,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w600,
                        color: AppColors.black,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32),
                      child: Text(
                        'Unable to load map. Please check your location permissions and try again.',
                        style: TextStyle(
                          fontSize: 16,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w400,
                          color: AppColors.black.withValues(alpha: 0.6),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _isLoading = true;
                          _hasError = false;
                        });
                        _initializeMap();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: AppColors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            )
          else
            GoogleMap(
              initialCameraPosition: _initialCameraPosition,
              onMapCreated: (GoogleMapController controller) {
                MapService.setMapController(controller);
                _mapController = controller;
              },
              markers: {..._pickupMarkers, ..._getDeliveryMarkers(), ..._getCurrentLocationMarkers()},
              polylines: _routePolylines,
              myLocationEnabled: false, // Disable built-in location to avoid duplicates
              myLocationButtonEnabled: false,
              zoomControlsEnabled: false,
              mapToolbarEnabled: false,
              compassEnabled: true,
              trafficEnabled: true,
              buildingsEnabled: true,
              mapType: _currentMapType,
              onTap: (LatLng position) {
                // Handle map tap if needed
              },
            ),

          // Back button
          Positioned(
            left: 20,
            top: MediaQuery.of(context).padding.top + 16,
            child: GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.arrow_back,
                  color: AppColors.black.withValues(alpha: 0.8),
                  size: 24,
                ),
              ),
            ),
          ),

          // Map controls
          Positioned(
            right: 20,
            bottom: 120,
            child: Column(
              children: [
                // My location button
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: IconButton(
                    onPressed: () async {
                      // Update current location and move camera
                      final location = await MapService.getCurrentLocation();
                      if (location != null && location.latitude != null && location.longitude != null) {
                        _currentUserLocation = LatLng(location.latitude!, location.longitude!);
                        await _addCustomCurrentLocationMarker();
                        await MapService.moveCameraToLocation(_currentUserLocation!, zoom: 16.0);
                      }
                    },
                    icon: Icon(
                      Icons.my_location,
                      color: AppColors.primary,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                // Map type button
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: IconButton(
                    onPressed: _showMapTypeDialog,
                    icon: Icon(
                      Icons.layers,
                      color: AppColors.primary,
                      size: 24,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Bottom info panel or active delivery controls
          if (_activeDelivery != null)
            _buildActiveDeliveryControls(context)
          else
            Positioned(
              left: 20,
              right: 20,
              bottom: 40,
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.black.withValues(alpha: 0.1),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.success.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.location_on,
                        color: AppColors.success,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'Live Location Tracking',
                            style: TextStyle(
                              fontSize: 16,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w600,
                              color: AppColors.black,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Your location is being tracked for deliveries',
                            style: TextStyle(
                              fontSize: 14,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w400,
                              color: AppColors.black.withValues(alpha: 0.6),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: AppColors.success,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Build active delivery controls
  Widget _buildActiveDeliveryControls(BuildContext context) {
    final pickupData = _activeDelivery!['pickupData'] as Map<String, dynamic>? ?? {};
    final receiverData = _activeDelivery!['receiverData'] as Map<String, dynamic>? ?? {};
    final trackingNumber = _activeDelivery!['trackingNumber'] ?? '#RO00000';

    return Positioned(
      left: 20,
      right: 20,
      bottom: 40,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppColors.black.withValues(alpha: 0.1),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with tracking number
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.local_shipping,
                    color: AppColors.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Active Delivery',
                        style: TextStyle(
                          fontSize: 16,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w600,
                          color: AppColors.black,
                        ),
                      ),
                      Text(
                        trackingNumber,
                        style: TextStyle(
                          fontSize: 12,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w400,
                          color: AppColors.black.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    shape: BoxShape.circle,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Action buttons
            _buildDeliveryActionButtons(),
          ],
        ),
      ),
    );
  }

  /// Build delivery action buttons based on pickup status
  Widget _buildDeliveryActionButtons() {
    if (!_isPickupCompleted) {
      // Before pickup completion
      return Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _handlePickupComplete(),
              icon: Icon(Icons.check_circle, size: 18),
              label: Text('Pickup Complete'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _showDeliveryDetails(),
              icon: Icon(Icons.info_outline, size: 18),
              label: Text('Details'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.primary,
                side: BorderSide(color: AppColors.primary),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      );
    } else {
      // After pickup completion
      return Column(
        children: [
          // Status indicator
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.success.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: AppColors.success,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Package picked up - En route to delivery',
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                    color: AppColors.success,
                  ),
                ),
              ],
            ),
          ),
          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _showDeliveryDetails(),
                  icon: Icon(Icons.info_outline, size: 18),
                  label: Text('Details'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.primary,
                    side: BorderSide(color: AppColors.primary),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _handleDeliveryComplete(),
                  icon: Icon(Icons.flag, size: 18),
                  label: Text('Complete Delivery'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.success,
                    foregroundColor: AppColors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      );
    }
  }

  /// Handle pickup completion and switch to delivery route
  Future<void> _handlePickupComplete() async {
    if (_activeDelivery == null || _currentUserLocation == null) return;

    try {
      // Update status to picked up
      await _updateOrderStatus(_activeDelivery!['id'], 'picked_up');

      // Set pickup completed status
      setState(() {
        _isPickupCompleted = true;
        _routePolylines.clear();
      });

      // Plot route to delivery location
      final receiverData = _activeDelivery!['receiverData'] as Map<String, dynamic>? ?? {};
      final deliveryLat = receiverData['latitude'] as double?;
      final deliveryLng = receiverData['longitude'] as double?;

      if (deliveryLat != null && deliveryLng != null) {
        final deliveryLocation = LatLng(deliveryLat, deliveryLng);

        // Create route to delivery location
        await _createRoute(_currentUserLocation!, deliveryLocation, 'delivery');

        // Adjust camera to show the new route
        await _fitRouteInView(_currentUserLocation!, deliveryLocation);

        Toast.success('Pickup complete! Route plotted to delivery location.');
      }
    } catch (e) {
      print('Error completing pickup: $e');
      Toast.error('Failed to update pickup status.');
    }
  }

  /// Show delivery details
  void _showDeliveryDetails() {
    if (_activeDelivery == null) return;

    final pickupData = _activeDelivery!['pickupData'] as Map<String, dynamic>? ?? {};
    final receiverData = _activeDelivery!['receiverData'] as Map<String, dynamic>? ?? {};
    final paymentData = _activeDelivery!['paymentData'] as Map<String, dynamic>? ?? {};
    final cargoItems = _activeDelivery!['cargoItems'] as List? ?? [];

    // Get package type
    String packageType = 'Package';
    if (cargoItems.isNotEmpty) {
      packageType = cargoItems.first['category'] ?? 'Package';
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.75,
              maxWidth: MediaQuery.of(context).size.width * 0.9,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header with close button
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppColors.success,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: AppColors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Icon(
                          Icons.delivery_dining,
                          color: AppColors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Active Delivery',
                              style: TextStyle(
                                fontSize: 18,
                                fontFamily: 'Poppins',
                                fontWeight: FontWeight.w600,
                                color: AppColors.white,
                              ),
                            ),
                            Text(
                              _activeDelivery!['trackingNumber'] ?? '#RO00000',
                              style: TextStyle(
                                fontSize: 12,
                                fontFamily: 'Poppins',
                                fontWeight: FontWeight.w400,
                                color: AppColors.white.withValues(alpha: 0.8),
                              ),
                            ),
                          ],
                        ),
                      ),
                      GestureDetector(
                        onTap: () => Navigator.of(context).pop(),
                        child: Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: AppColors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.close,
                            color: AppColors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Content
                Flexible(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        // Status indicator
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.success.withValues(alpha: 0.05),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppColors.success.withValues(alpha: 0.1),
                            ),
                          ),
                          child: Row(
                            children: [
                              Container(
                                width: 32,
                                height: 32,
                                decoration: BoxDecoration(
                                  color: AppColors.success,
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.check,
                                  color: AppColors.white,
                                  size: 18,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Package Picked Up',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontFamily: 'Poppins',
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.success,
                                      ),
                                    ),
                                    Text(
                                      'Ready for delivery',
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontFamily: 'Poppins',
                                        fontWeight: FontWeight.w400,
                                        color: AppColors.black.withValues(alpha: 0.6),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 20),

                        // Pickup Details Section
                        _buildColoredSection(
                          title: 'Pickup Details',
                          icon: Icons.person_pin_circle,
                          backgroundColor: AppColors.primary,
                          content: Column(
                            children: [
                              _buildInfoRow('Sender', pickupData['senderName'] ?? 'Unknown', Icons.person),
                              _buildInfoRow('Phone', pickupData['phone'] ?? 'No phone', Icons.phone),
                              _buildInfoRow('State', pickupData['state'] ?? 'Unknown', Icons.location_city),
                              if (pickupData['landmark'] != null)
                                _buildInfoRow('Landmark', pickupData['landmark'], Icons.place),
                              const SizedBox(height: 12),
                              _buildAddressCard(pickupData['fullAddress'] ?? 'No address'),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Delivery Details Section
                        _buildColoredSection(
                          title: 'Delivery Details',
                          icon: Icons.location_on,
                          backgroundColor: AppColors.success,
                          content: Column(
                            children: [
                              _buildInfoRow('Receiver', receiverData['name'] ?? 'Unknown', Icons.person_outline),
                              _buildInfoRow('Phone', receiverData['phone'] ?? 'No phone', Icons.phone),
                              _buildInfoRow('State', receiverData['state'] ?? 'Unknown', Icons.location_city),
                              const SizedBox(height: 12),
                              _buildAddressCard(receiverData['address'] ?? 'No address'),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Product Images Section
                        if (cargoItems.isNotEmpty) ...[
                          _buildProductImagesSection(cargoItems),
                          const SizedBox(height: 16),
                        ],

                        const SizedBox(height: 20),

                        // Package Info Card
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withValues(alpha: 0.05),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppColors.primary.withValues(alpha: 0.1),
                            ),
                          ),
                          child: Row(
                            children: [
                              Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: AppColors.primary.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Icon(
                                  Icons.inventory_2,
                                  color: AppColors.primary,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Package Information',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontFamily: 'Poppins',
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.black,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'Type: $packageType',
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  fontFamily: 'Poppins',
                                                  fontWeight: FontWeight.w400,
                                                  color: AppColors.black.withValues(alpha: 0.7),
                                                ),
                                              ),
                                              Text(
                                                'Items: ${cargoItems.length} item(s)',
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  fontFamily: 'Poppins',
                                                  fontWeight: FontWeight.w400,
                                                  color: AppColors.black.withValues(alpha: 0.7),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                          decoration: BoxDecoration(
                                            color: AppColors.primary,
                                            borderRadius: BorderRadius.circular(20),
                                          ),
                                          child: Text(
                                            '₦${(paymentData['total'] ?? 0.0).toStringAsFixed(0)}',
                                            style: TextStyle(
                                              fontSize: 14,
                                              fontFamily: 'Poppins',
                                              fontWeight: FontWeight.w600,
                                              color: AppColors.white,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Action buttons
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(20),
                      bottomRight: Radius.circular(20),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.black.withValues(alpha: 0.05),
                        blurRadius: 10,
                        offset: const Offset(0, -2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppColors.black.withValues(alpha: 0.6),
                            side: BorderSide(color: AppColors.black.withValues(alpha: 0.2)),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          child: Text(
                            'Close',
                            style: TextStyle(
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        flex: 2,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            _handleDeliveryComplete();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.success,
                            foregroundColor: AppColors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            elevation: 0,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.check_circle, size: 20),
                              const SizedBox(width: 8),
                              Text(
                                'Complete Delivery',
                                style: TextStyle(
                                  fontFamily: 'Poppins',
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Handle delivery completion
  Future<void> _handleDeliveryComplete() async {
    if (_activeDelivery == null) return;

    try {
      // Update status to completed
      await _updateOrderStatus(_activeDelivery!['id'], 'completed');

      // Clear active delivery and routes
      setState(() {
        _activeDelivery = null;
        _isPickupCompleted = false; // Reset pickup status
        _routePolylines.clear();
        _pickupMarkers.clear();
      });

      // Refresh pickup points
      await _loadPickupPoints();

      Toast.success('Delivery completed successfully!');
    } catch (e) {
      print('Error completing delivery: $e');
      Toast.error('Failed to complete delivery.');
    }
  }

  void _showMapTypeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Map Type',
          style: TextStyle(
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w600,
            color: AppColors.black,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildMapTypeOption('Normal', MapType.normal),
            _buildMapTypeOption('Satellite', MapType.satellite),
            _buildMapTypeOption('Hybrid', MapType.hybrid),
            _buildMapTypeOption('Terrain', MapType.terrain),
          ],
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  Widget _buildMapTypeOption(String title, MapType mapType) {
    final isSelected = _currentMapType == mapType;

    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          fontFamily: 'Poppins',
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          color: isSelected ? AppColors.primary : AppColors.black,
        ),
      ),
      leading: Icon(
        isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
        color: isSelected ? AppColors.primary : AppColors.black.withValues(alpha: 0.5),
      ),
      onTap: () {
        setState(() {
          _currentMapType = mapType;
        });
        Navigator.pop(context);
      },
    );
  }

  /// Calculate distance between two points using Haversine formula
  double _calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    final double lat1Rad = point1.latitude * (math.pi / 180);
    final double lat2Rad = point2.latitude * (math.pi / 180);
    final double deltaLatRad = (point2.latitude - point1.latitude) * (math.pi / 180);
    final double deltaLngRad = (point2.longitude - point1.longitude) * (math.pi / 180);

    final double a = math.sin(deltaLatRad / 2) * math.sin(deltaLatRad / 2) +
        math.cos(lat1Rad) * math.cos(lat2Rad) *
        math.sin(deltaLngRad / 2) * math.sin(deltaLngRad / 2);
    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));

    return earthRadius * c;
  }

  /// Create custom marker icon for pickup points
  Future<BitmapDescriptor> _createPickupMarkerIcon() async {
    try {
      return await _createCustomMarker(
        color: AppColors.primary,
        icon: Icons.local_shipping,
        size: 60, // Smaller size like previous
      );
    } catch (e) {
      // Fallback to default marker
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueOrange);
    }
  }

  /// Create custom marker icon for delivery points
  Future<BitmapDescriptor> _createDeliveryMarkerIcon() async {
    try {
      return await _createCustomMarker(
        color: AppColors.success,
        icon: Icons.flag,
        size: 60, // Smaller size like previous
      );
    } catch (e) {
      // Fallback to default marker
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
    }
  }

  /// Create custom marker icon for user location - bold and visible
  Future<BitmapDescriptor> _createUserLocationMarkerIcon() async {
    try {
      return await _createUserLocationMarker();
    } catch (e) {
      // Fallback to default marker
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue);
    }
  }

  /// Create a custom marker with specified color and icon
  Future<BitmapDescriptor> _createCustomMarker({
    required Color color,
    required IconData icon,
    required int size,
  }) async {
    final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(pictureRecorder);
    final Paint paint = Paint()..color = color;
    final Paint borderPaint = Paint()
      ..color = AppColors.white
      ..strokeWidth = 4
      ..style = PaintingStyle.stroke;

    // Draw the marker background (circle)
    final double radius = size / 2.0;
    final Offset center = Offset(radius, radius);

    // Draw white border
    canvas.drawCircle(center, radius - 2, borderPaint);

    // Draw colored background
    canvas.drawCircle(center, radius - 4, paint);

    // Draw the icon
    final TextPainter textPainter = TextPainter(textDirection: TextDirection.ltr);
    textPainter.text = TextSpan(
      text: String.fromCharCode(icon.codePoint),
      style: TextStyle(
        fontSize: size * 0.5,
        fontFamily: icon.fontFamily,
        color: AppColors.white,
        fontWeight: FontWeight.bold,
      ),
    );
    textPainter.layout();

    final double iconX = (size - textPainter.width) / 2;
    final double iconY = (size - textPainter.height) / 2;
    textPainter.paint(canvas, Offset(iconX, iconY));

    // Convert to image
    final ui.Image markerAsImage = await pictureRecorder.endRecording().toImage(size, size);
    final ByteData? byteData = await markerAsImage.toByteData(format: ui.ImageByteFormat.png);
    final Uint8List uint8List = byteData!.buffer.asUint8List();

    return BitmapDescriptor.fromBytes(uint8List);
  }

  /// Create a bold, visible user location marker
  Future<BitmapDescriptor> _createUserLocationMarker() async {
    final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(pictureRecorder);
    final int size = 70; // Larger size for better visibility
    final double radius = size / 2.0;
    final Offset center = Offset(radius, radius);

    // Outer ring (white border)
    final Paint outerRingPaint = Paint()
      ..color = AppColors.white
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, radius - 2, outerRingPaint);

    // Middle ring (blue background)
    final Paint middleRingPaint = Paint()
      ..color = Colors.blue.shade600
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, radius - 6, middleRingPaint);

    // Inner circle (darker blue)
    final Paint innerCirclePaint = Paint()
      ..color = Colors.blue.shade800
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, radius - 12, innerCirclePaint);

    // Center dot (white)
    final Paint centerDotPaint = Paint()
      ..color = AppColors.white
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, radius - 20, centerDotPaint);

    // Add pulsing effect with outer transparent ring
    final Paint pulseRingPaint = Paint()
      ..color = Colors.blue.withValues(alpha: 0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4;
    canvas.drawCircle(center, radius - 1, pulseRingPaint);

    // Convert to image
    final ui.Picture picture = pictureRecorder.endRecording();
    final ui.Image image = await picture.toImage(size, size);
    final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    final Uint8List uint8List = byteData!.buffer.asUint8List();

    return BitmapDescriptor.fromBytes(uint8List);
  }

  /// Add custom current location marker
  Future<void> _addCustomCurrentLocationMarker() async {
    if (_currentUserLocation == null) return;

    try {
      final customIcon = await _createUserLocationMarkerIcon();

      final marker = Marker(
        markerId: MarkerId('current_location'),
        position: _currentUserLocation!,
        icon: customIcon,
        infoWindow: InfoWindow(
          title: 'Your Location',
          snippet: 'Current position',
        ),
        anchor: Offset(0.5, 0.5), // Center the marker
      );

      setState(() {
        _currentLocationMarkers.clear();
        _currentLocationMarkers.add(marker);
      });
    } catch (e) {
      print('Error creating custom location marker: $e');
      // Fallback to default marker
      final marker = Marker(
        markerId: MarkerId('current_location'),
        position: _currentUserLocation!,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        infoWindow: InfoWindow(
          title: 'Your Location',
          snippet: 'Current position',
        ),
      );

      setState(() {
        _currentLocationMarkers.clear();
        _currentLocationMarkers.add(marker);
      });
    }
  }

  /// Get current location markers
  Set<Marker> _getCurrentLocationMarkers() {
    return _currentLocationMarkers;
  }

  /// Show pickup point details dialog
  void _showPickupPointDialog(Map<String, dynamic> pickupPoint) {
    final orderData = pickupPoint['orderData'] as Map<String, dynamic>;
    final pickupData = pickupPoint['pickupData'] as Map<String, dynamic>;
    final receiverData = orderData['receiverData'] as Map<String, dynamic>? ?? {};
    final paymentData = orderData['paymentData'] as Map<String, dynamic>? ?? {};
    final cargoItems = orderData['cargoItems'] as List? ?? [];

    // Get package type
    String packageType = 'Package';
    if (cargoItems.isNotEmpty) {
      packageType = cargoItems.first['category'] ?? 'Package';
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.75,
              maxWidth: MediaQuery.of(context).size.width * 0.9,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header with close button
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: AppColors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Icon(
                          Icons.local_shipping,
                          color: AppColors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Delivery Request',
                              style: TextStyle(
                                fontSize: 18,
                                fontFamily: 'Poppins',
                                fontWeight: FontWeight.w600,
                                color: AppColors.white,
                              ),
                            ),
                            Text(
                              '${pickupPoint['distance'].toStringAsFixed(1)} km away',
                              style: TextStyle(
                                fontSize: 12,
                                fontFamily: 'Poppins',
                                fontWeight: FontWeight.w400,
                                color: AppColors.white.withValues(alpha: 0.8),
                              ),
                            ),
                          ],
                        ),
                      ),
                      GestureDetector(
                        onTap: () => Navigator.of(context).pop(),
                        child: Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: AppColors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.close,
                            color: AppColors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Content
                Flexible(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        // Tracking number card
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withValues(alpha: 0.05),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppColors.primary.withValues(alpha: 0.1),
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.qr_code,
                                color: AppColors.primary,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Tracking Number',
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontFamily: 'Poppins',
                                        fontWeight: FontWeight.w500,
                                        color: AppColors.black.withValues(alpha: 0.6),
                                      ),
                                    ),
                                    Text(
                                      orderData['trackingNumber'] ?? '#RO00000',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontFamily: 'Poppins',
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.primary,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 20),

                        // Pickup Details Section
                        _buildColoredSection(
                          title: 'Pickup Details',
                          icon: Icons.person_pin_circle,
                          backgroundColor: AppColors.primary,
                          content: Column(
                            children: [
                              _buildInfoRow('Sender', pickupData['senderName'] ?? 'Unknown', Icons.person),
                              _buildInfoRow('Phone', pickupData['phone'] ?? 'No phone', Icons.phone),
                              _buildInfoRow('State', pickupData['state'] ?? 'Unknown', Icons.location_city),
                              if (pickupData['landmark'] != null)
                                _buildInfoRow('Landmark', pickupData['landmark'], Icons.place),
                              const SizedBox(height: 12),
                              _buildAddressCard(pickupData['fullAddress'] ?? 'No address'),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Delivery Details Section
                        _buildColoredSection(
                          title: 'Delivery Details',
                          icon: Icons.location_on,
                          backgroundColor: AppColors.success,
                          content: Column(
                            children: [
                              _buildInfoRow('Receiver', receiverData['name'] ?? 'Unknown', Icons.person_outline),
                              _buildInfoRow('Phone', receiverData['phone'] ?? 'No phone', Icons.phone),
                              _buildInfoRow('State', receiverData['state'] ?? 'Unknown', Icons.location_city),
                              const SizedBox(height: 12),
                              _buildAddressCard(receiverData['address'] ?? 'No address'),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Product Images Section
                        if (cargoItems.isNotEmpty) ...[
                          _buildProductImagesSection(cargoItems),
                          const SizedBox(height: 16),
                        ],

                        const SizedBox(height: 20),

                        // Package Info Card
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.success.withValues(alpha: 0.05),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppColors.success.withValues(alpha: 0.1),
                            ),
                          ),
                          child: Row(
                            children: [
                              Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: AppColors.success.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Icon(
                                  Icons.inventory_2,
                                  color: AppColors.success,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Package Information',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontFamily: 'Poppins',
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.black,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'Type: $packageType',
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  fontFamily: 'Poppins',
                                                  fontWeight: FontWeight.w400,
                                                  color: AppColors.black.withValues(alpha: 0.7),
                                                ),
                                              ),
                                              Text(
                                                'Items: ${cargoItems.length} item(s)',
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  fontFamily: 'Poppins',
                                                  fontWeight: FontWeight.w400,
                                                  color: AppColors.black.withValues(alpha: 0.7),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                          decoration: BoxDecoration(
                                            color: AppColors.success,
                                            borderRadius: BorderRadius.circular(20),
                                          ),
                                          child: Text(
                                            '₦${(paymentData['total'] ?? 0.0).toStringAsFixed(0)}',
                                            style: TextStyle(
                                              fontSize: 14,
                                              fontFamily: 'Poppins',
                                              fontWeight: FontWeight.w600,
                                              color: AppColors.white,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Action buttons
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(20),
                      bottomRight: Radius.circular(20),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.black.withValues(alpha: 0.05),
                        blurRadius: 10,
                        offset: const Offset(0, -2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppColors.black.withValues(alpha: 0.6),
                            side: BorderSide(color: AppColors.black.withValues(alpha: 0.2)),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          child: Text(
                            'Close',
                            style: TextStyle(
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        flex: 2,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            _handleAcceptDelivery(orderData);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: AppColors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            elevation: 0,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.check_circle, size: 20),
                              const SizedBox(width: 8),
                              Text(
                                'Accept Delivery',
                                style: TextStyle(
                                  fontFamily: 'Poppins',
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Build detail card for pickup/delivery information
  Widget _buildDetailCard({
    required String title,
    required IconData icon,
    required Color iconColor,
    required List<Widget> details,
    required String address,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.black.withValues(alpha: 0.1),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: iconColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: iconColor,
                  size: 18,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w600,
                    color: AppColors.black,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Details
          ...details,

          const SizedBox(height: 8),

          // Address
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.black.withValues(alpha: 0.03),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              address,
              style: TextStyle(
                fontSize: 11,
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w400,
                color: AppColors.black.withValues(alpha: 0.7),
                height: 1.3,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// Build detail row for individual information
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 50,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 11,
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w500,
                color: AppColors.black.withValues(alpha: 0.6),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 11,
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w400,
                color: AppColors.black.withValues(alpha: 0.8),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// Build colored section with header and content
  Widget _buildColoredSection({
    required String title,
    required IconData icon,
    required Color backgroundColor,
    required Widget content,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: AppColors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    icon,
                    color: AppColors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w600,
                    color: AppColors.white,
                  ),
                ),
              ],
            ),
          ),
          // Content
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
            ),
            child: content,
          ),
        ],
      ),
    );
  }

  /// Build info row with icon
  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppColors.black.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 16,
              color: AppColors.black.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w500,
                    color: AppColors.black.withValues(alpha: 0.6),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w600,
                    color: AppColors.black,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build address card
  Widget _buildAddressCard(String address) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.black.withValues(alpha: 0.03),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: AppColors.black.withValues(alpha: 0.1),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.location_on_outlined,
            size: 16,
            color: AppColors.black.withValues(alpha: 0.5),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              address,
              style: TextStyle(
                fontSize: 13,
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w400,
                color: AppColors.black.withValues(alpha: 0.8),
                height: 1.3,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// Build cargo items section with complete details
  Widget _buildProductImagesSection(List cargoItems) {
    if (cargoItems.isEmpty) {
      return const SizedBox.shrink();
    }

    return _buildColoredSection(
      title: 'Cargo Items (${cargoItems.length})',
      icon: Icons.inventory_2,
      backgroundColor: Colors.orange,
      content: Column(
        children: cargoItems.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          return Column(
            children: [
              if (index > 0) const SizedBox(height: 16),
              _buildCargoItemCard(item, index + 1),
            ],
          );
        }).toList(),
      ),
    );
  }

  /// Build individual cargo item card
  Widget _buildCargoItemCard(Map<String, dynamic> item, int itemNumber) {
    final images = item['images'] as List? ?? [];
    final itemName = item['itemName'] ?? 'Unknown Item';
    final category = item['category'] ?? 'General';
    final itemType = item['itemType'] ?? 'Standard';

    // Convert numeric values to strings properly
    final weight = item['weight'] != null
        ? (item['weight'] is double
            ? '${(item['weight'] as double).toStringAsFixed(1)} kg'
            : '${item['weight']} kg')
        : 'Not specified';
    final quantity = item['quantity'] != null
        ? '${item['quantity']}'
        : '1';
    final durability = item['durability'] != null
        ? (item['durability'] is String
            ? item['durability'] as String
            : item['durability'].toString())
        : 'Standard';

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.black.withValues(alpha: 0.02),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.black.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Item header
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    '$itemNumber',
                    style: TextStyle(
                      fontSize: 14,
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w600,
                      color: Colors.orange,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      itemName,
                      style: TextStyle(
                        fontSize: 16,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w600,
                        color: AppColors.black,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      category,
                      style: TextStyle(
                        fontSize: 12,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w400,
                        color: AppColors.black.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Item details
          Row(
            children: [
              Expanded(
                child: Column(
                  children: [
                    _buildItemDetailRow('Type', itemType),
                    _buildItemDetailRow('Weight', weight),
                    _buildItemDetailRow('Quantity', quantity),
                    _buildItemDetailRow('Durability', durability),
                  ],
                ),
              ),

              // Images section
              if (images.isNotEmpty) ...[
                const SizedBox(width: 16),
                Container(
                  width: 100,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Images (${images.length})',
                        style: TextStyle(
                          fontSize: 12,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w500,
                          color: AppColors.black.withValues(alpha: 0.6),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        height: 60,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: images.length,
                          itemBuilder: (context, index) {
                            return Container(
                              margin: const EdgeInsets.only(right: 8),
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: AppColors.black.withValues(alpha: 0.1),
                                ),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: Image.file(
                                  File(images[index]),
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      color: AppColors.black.withValues(alpha: 0.05),
                                      child: Icon(
                                        Icons.image_not_supported,
                                        color: AppColors.black.withValues(alpha: 0.4),
                                        size: 20,
                                      ),
                                    );
                                  },
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  /// Build item detail row
  Widget _buildItemDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6),
      child: Row(
        children: [
          SizedBox(
            width: 60,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 11,
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w500,
                color: AppColors.black.withValues(alpha: 0.6),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 11,
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w600,
                color: AppColors.black.withValues(alpha: 0.8),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// Handle accepting a delivery
  Future<void> _handleAcceptDelivery(Map<String, dynamic> orderData) async {
    try {
      // Check if there's already an active delivery
      if (_activeDelivery != null) {
        Toast.error('You must complete your current delivery before accepting another one.');
        return;
      }

      // Update order status to accepted
      await _updateOrderStatus(orderData['id'], 'accepted');

      // Set as active delivery
      setState(() {
        _activeDelivery = orderData;
      });

      // Plot route to pickup location first
      await _plotRouteToPickup(orderData);

      // Show success message
      Toast.success('Delivery accepted! Route plotted to pickup location.');

      // Refresh pickup points to remove accepted delivery
      await _loadPickupPoints();

    } catch (e) {
      print('Error accepting delivery: $e');
      Toast.error('Failed to accept delivery. Please try again.');
    }
  }

  /// Update order status in storage
  Future<void> _updateOrderStatus(String orderId, String newStatus) async {
    try {
      final completedOrders = await PackageDataService.getCompletedOrders();
      final orderIndex = completedOrders.indexWhere((order) => order['id'] == orderId);

      if (orderIndex != -1) {
        completedOrders[orderIndex]['status'] = newStatus;
        completedOrders[orderIndex]['acceptedTime'] = DateTime.now().millisecondsSinceEpoch;

        // Save updated orders back to storage
        await PackageDataService.saveCompletedOrder(completedOrders[orderIndex]);
      }
    } catch (e) {
      print('Error updating order status: $e');
    }
  }

  /// Plot route from current location to pickup location
  Future<void> _plotRouteToPickup(Map<String, dynamic> orderData) async {
    if (_currentUserLocation == null) return;

    final pickupData = orderData['pickupData'] as Map<String, dynamic>? ?? {};
    final pickupLat = pickupData['latitude'] as double?;
    final pickupLng = pickupData['longitude'] as double?;

    if (pickupLat == null || pickupLng == null) return;

    final pickupLocation = LatLng(pickupLat, pickupLng);

    // Create route polyline
    await _createRoute(_currentUserLocation!, pickupLocation, 'pickup');

    // Add delivery location marker for reference
    final receiverData = orderData['receiverData'] as Map<String, dynamic>? ?? {};
    final deliveryLat = receiverData['latitude'] as double?;
    final deliveryLng = receiverData['longitude'] as double?;

    if (deliveryLat != null && deliveryLng != null) {
      final deliveryLocation = LatLng(deliveryLat, deliveryLng);
      await _addDeliveryMarker(deliveryLocation, receiverData);
    }

    // Adjust camera to show the route
    await _fitRouteInView(_currentUserLocation!, pickupLocation);
  }

  /// Create route polyline between two points
  Future<void> _createRoute(LatLng start, LatLng end, String routeType) async {
    // For now, create a simple straight line route
    // In a real app, you'd use Google Directions API for actual road routes
    final polyline = Polyline(
      polylineId: PolylineId('route_$routeType'),
      points: [start, end],
      color: routeType == 'pickup' ? AppColors.primary : AppColors.success,
      width: 4,
      patterns: routeType == 'delivery' ? [PatternItem.dash(10), PatternItem.gap(5)] : [],
    );

    setState(() {
      _routePolylines.add(polyline);
    });
  }

  /// Add delivery location marker
  Future<void> _addDeliveryMarker(LatLng location, Map<String, dynamic> receiverData) async {
    final marker = Marker(
      markerId: MarkerId('delivery_location'),
      position: location,
      icon: await _createDeliveryMarkerIcon(),
      infoWindow: InfoWindow(
        title: 'Delivery Location',
        snippet: receiverData['name'] ?? 'Delivery Point',
      ),
    );

    setState(() {
      _pickupMarkers.add(marker);
    });
  }



  /// Get delivery markers for active delivery
  Set<Marker> _getDeliveryMarkers() {
    if (_activeDelivery == null) return {};

    final markers = <Marker>{};
    final receiverData = _activeDelivery!['receiverData'] as Map<String, dynamic>? ?? {};
    final deliveryLat = receiverData['latitude'] as double?;
    final deliveryLng = receiverData['longitude'] as double?;

    if (deliveryLat != null && deliveryLng != null) {
      final deliveryLocation = LatLng(deliveryLat, deliveryLng);
      final marker = Marker(
        markerId: MarkerId('active_delivery'),
        position: deliveryLocation,
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
        infoWindow: InfoWindow(
          title: 'Delivery Location',
          snippet: receiverData['name'] ?? 'Delivery Point',
        ),
      );
      markers.add(marker);
    }

    return markers;
  }

  /// Fit route in camera view
  Future<void> _fitRouteInView(LatLng start, LatLng end) async {
    if (_mapController == null) return;

    final bounds = LatLngBounds(
      southwest: LatLng(
        math.min(start.latitude, end.latitude),
        math.min(start.longitude, end.longitude),
      ),
      northeast: LatLng(
        math.max(start.latitude, end.latitude),
        math.max(start.longitude, end.longitude),
      ),
    );

    await _mapController!.animateCamera(
      CameraUpdate.newLatLngBounds(bounds, 100.0),
    );
  }

  @override
  void dispose() {
    MapService.stopLocationUpdates();
    super.dispose();
  }
}
