import 'package:rideoon/models/address/address.dart';

/// Response model for address operations
class AddressResponse {
  final int status;
  final Address? data;
  final List<Address>? addresses;
  final String? message;
  final String? statusText;

  const AddressResponse({
    required this.status,
    this.data,
    this.addresses,
    this.message,
    this.statusText,
  });

  /// Create AddressResponse from JSON for single address
  factory AddressResponse.fromJson(Map<String, dynamic> json) {
    return AddressResponse(
      status: json['status'] as int,
      data: json['data'] != null ? Address.fromJson(json['data'] as Map<String, dynamic>) : null,
      message: json['message'] as String?,
      statusText: json['statusText'] as String?,
    );
  }

  /// Create AddressResponse from JSON for multiple addresses
  factory AddressResponse.fromJsonList(Map<String, dynamic> json) {
    List<Address>? addressList;
    
    if (json['data'] != null) {
      final dataList = json['data'] as List<dynamic>;
      addressList = dataList
          .map((item) => Address.fromJson(item as Map<String, dynamic>))
          .toList();
    }

    return AddressResponse(
      status: json['status'] as int,
      addresses: addressList,
      message: json['message'] as String?,
      statusText: json['statusText'] as String?,
    );
  }

  /// Create success response for single address
  factory AddressResponse.success({
    required Address address,
    String? message,
    int status = 200,
  }) {
    return AddressResponse(
      status: status,
      data: address,
      message: message ?? 'Success',
    );
  }

  /// Create success response for multiple addresses
  factory AddressResponse.successList({
    required List<Address> addresses,
    String? message,
    int status = 200,
  }) {
    return AddressResponse(
      status: status,
      addresses: addresses,
      message: message ?? 'Success',
    );
  }

  /// Create error response
  factory AddressResponse.error({
    required String message,
    int status = 400,
  }) {
    return AddressResponse(
      status: status,
      message: message,
    );
  }

  /// Create delete success response
  factory AddressResponse.deleted({
    int status = 200,
    String statusText = 'Deleted',
  }) {
    return AddressResponse(
      status: status,
      statusText: statusText,
    );
  }

  /// Convert AddressResponse to JSON
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'status': status,
    };

    if (data != null) {
      json['data'] = data!.toJson();
    }

    if (addresses != null) {
      json['data'] = addresses!.map((address) => address.toJson()).toList();
    }

    if (message != null) {
      json['message'] = message;
    }

    if (statusText != null) {
      json['statusText'] = statusText;
    }

    return json;
  }

  /// Check if the response indicates success
  bool get isSuccess => status >= 200 && status < 300;

  /// Check if the response indicates an error
  bool get isError => !isSuccess;

  /// Get the first address from the response
  Address? get firstAddress {
    if (data != null) return data;
    if (addresses != null && addresses!.isNotEmpty) return addresses!.first;
    return null;
  }

  /// Get all addresses from the response
  List<Address> get allAddresses {
    if (addresses != null) return addresses!;
    if (data != null) return [data!];
    return [];
  }

  /// Get the count of addresses
  int get addressCount {
    if (addresses != null) return addresses!.length;
    if (data != null) return 1;
    return 0;
  }

  @override
  String toString() {
    return 'AddressResponse(status: $status, addressCount: $addressCount, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AddressResponse &&
        other.status == status &&
        other.data == data &&
        other.message == message &&
        other.statusText == statusText;
  }

  @override
  int get hashCode {
    return Object.hash(status, data, message, statusText);
  }
}
