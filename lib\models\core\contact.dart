/// Core contact information model
class Contact {
  final String name;
  final String phoneNumber;
  final String? email;
  final String? alternatePhone;

  const Contact({
    required this.name,
    required this.phoneNumber,
    this.email,
    this.alternatePhone,
  });

  /// Get display name (first name only if full name provided)
  String get displayName {
    final parts = name.trim().split(' ');
    return parts.isNotEmpty ? parts.first : name;
  }

  /// Get initials for avatar display
  String get initials {
    final parts = name.trim().split(' ');
    if (parts.length >= 2) {
      return '${parts.first[0]}${parts.last[0]}'.toUpperCase();
    } else if (parts.isNotEmpty && parts.first.isNotEmpty) {
      return parts.first[0].toUpperCase();
    }
    return 'U';
  }

  /// Check if contact has email
  bool get hasEmail => email?.isNotEmpty == true;

  /// Check if contact has alternate phone
  bool get hasAlternatePhone => alternatePhone?.isNotEmpty == true;

  /// Format phone number for display
  String get formattedPhone {
    if (phoneNumber.startsWith('+234')) {
      return phoneNumber;
    } else if (phoneNumber.startsWith('0')) {
      return '+234${phoneNumber.substring(1)}';
    } else if (phoneNumber.length == 10) {
      return '+234$phoneNumber';
    }
    return phoneNumber;
  }

  /// Create Contact from JSON
  factory Contact.fromJson(Map<String, dynamic> json) {
    return Contact(
      name: json['name'] as String? ?? json['senderName'] as String? ?? '',
      phoneNumber: json['phoneNumber'] as String? ?? json['phone'] as String? ?? '',
      email: json['email'] as String?,
      alternatePhone: json['alternatePhone'] as String?,
    );
  }

  /// Convert Contact to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'phoneNumber': phoneNumber,
      if (email != null) 'email': email,
      if (alternatePhone != null) 'alternatePhone': alternatePhone,
    };
  }

  /// Create a copy with updated fields
  Contact copyWith({
    String? name,
    String? phoneNumber,
    String? email,
    String? alternatePhone,
  }) {
    return Contact(
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      alternatePhone: alternatePhone ?? this.alternatePhone,
    );
  }

  @override
  String toString() {
    return 'Contact(name: $name, phone: $formattedPhone)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Contact &&
        other.name == name &&
        other.phoneNumber == phoneNumber &&
        other.email == email &&
        other.alternatePhone == alternatePhone;
  }

  @override
  int get hashCode {
    return Object.hash(name, phoneNumber, email, alternatePhone);
  }
}
