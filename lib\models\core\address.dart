/// Core address model for locations
class Address {
  final String street;
  final String? landmark;
  final String city;
  final String state;
  final String? postalCode;
  final String country;
  final double? latitude;
  final double? longitude;

  const Address({
    required this.street,
    this.landmark,
    required this.city,
    required this.state,
    this.postalCode,
    this.country = 'Nigeria',
    this.latitude,
    this.longitude,
  });

  /// Get full address as a single string
  String get fullAddress {
    final parts = <String>[
      street,
      if (landmark?.isNotEmpty == true) landmark!,
      city,
      state,
      if (postalCode?.isNotEmpty == true) postalCode!,
      country,
    ];
    return parts.join(', ');
  }

  /// Get short address (street, city, state)
  String get shortAddress {
    return '$street, $city, $state';
  }

  /// Check if address has coordinates
  bool get hasCoordinates => latitude != null && longitude != null;

  /// Create Address from JSON
  factory Address.fromJson(Map<String, dynamic> json) {
    return Address(
      street: json['street'] as String? ?? json['fullAddress'] as String? ?? '',
      landmark: json['landmark'] as String?,
      city: json['city'] as String? ?? '',
      state: json['state'] as String? ?? '',
      postalCode: json['postalCode'] as String?,
      country: json['country'] as String? ?? 'Nigeria',
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
    );
  }

  /// Convert Address to JSON
  Map<String, dynamic> toJson() {
    return {
      'street': street,
      if (landmark != null) 'landmark': landmark,
      'city': city,
      'state': state,
      if (postalCode != null) 'postalCode': postalCode,
      'country': country,
      if (latitude != null) 'latitude': latitude,
      if (longitude != null) 'longitude': longitude,
      'fullAddress': fullAddress,
    };
  }

  /// Create a copy with updated fields
  Address copyWith({
    String? street,
    String? landmark,
    String? city,
    String? state,
    String? postalCode,
    String? country,
    double? latitude,
    double? longitude,
  }) {
    return Address(
      street: street ?? this.street,
      landmark: landmark ?? this.landmark,
      city: city ?? this.city,
      state: state ?? this.state,
      postalCode: postalCode ?? this.postalCode,
      country: country ?? this.country,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
    );
  }

  @override
  String toString() {
    return 'Address(fullAddress: $fullAddress, hasCoordinates: $hasCoordinates)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Address &&
        other.street == street &&
        other.landmark == landmark &&
        other.city == city &&
        other.state == state &&
        other.postalCode == postalCode &&
        other.country == country &&
        other.latitude == latitude &&
        other.longitude == longitude;
  }

  @override
  int get hashCode {
    return Object.hash(
      street,
      landmark,
      city,
      state,
      postalCode,
      country,
      latitude,
      longitude,
    );
  }
}
