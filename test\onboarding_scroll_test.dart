import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:rideoon/views/onboarding/splash_screen.dart';
import 'package:rideoon/views/onboarding/onboarding_container.dart';
import 'package:rideoon/views/onboarding/user_type.dart';

void main() {
  group('Onboarding Scrollability Tests', () {
    testWidgets('Splash screen should be scrollable on short screens', (WidgetTester tester) async {
      // Set a short screen size
      await tester.binding.setSurfaceSize(const Size(400, 400));
      
      await tester.pumpWidget(
        const MaterialApp(
          home: SplashScreen(),
        ),
      );

      // Verify that SingleChildScrollView is present
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      
      // Verify that the screen doesn't overflow
      expect(tester.takeException(), isNull);
    });

    testWidgets('Onboarding container should handle short screens', (WidgetTester tester) async {
      // Set a short screen size
      await tester.binding.setSurfaceSize(const Size(400, 400));
      
      await tester.pumpWidget(
        const MaterialApp(
          home: OnboardingContainer(),
        ),
      );

      // Wait for animations
      await tester.pump(const Duration(seconds: 1));
      
      // Verify that the screen doesn't overflow
      expect(tester.takeException(), isNull);
      
      // Verify that PageView is present
      expect(find.byType(PageView), findsOneWidget);
    });

    testWidgets('User type selection should be scrollable on short screens', (WidgetTester tester) async {
      // Set a short screen size
      await tester.binding.setSurfaceSize(const Size(400, 400));
      
      await tester.pumpWidget(
        const MaterialApp(
          home: UserTypeSelection(),
        ),
      );

      // Verify that SingleChildScrollView is present
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      
      // Verify that the screen doesn't overflow
      expect(tester.takeException(), isNull);
      
      // Verify that both user type cards are present
      expect(find.text('Client'), findsOneWidget);
      expect(find.text('Rider'), findsOneWidget);
    });

    testWidgets('All screens should adapt to very short screens', (WidgetTester tester) async {
      // Set a very short screen size (like some smartwatches)
      await tester.binding.setSurfaceSize(const Size(300, 300));
      
      // Test splash screen
      await tester.pumpWidget(
        const MaterialApp(
          home: SplashScreen(),
        ),
      );
      expect(tester.takeException(), isNull);
      
      // Test onboarding container
      await tester.pumpWidget(
        const MaterialApp(
          home: OnboardingContainer(),
        ),
      );
      await tester.pump(const Duration(seconds: 1));
      expect(tester.takeException(), isNull);
      
      // Test user type selection
      await tester.pumpWidget(
        const MaterialApp(
          home: UserTypeSelection(),
        ),
      );
      expect(tester.takeException(), isNull);
    });
  });
}
