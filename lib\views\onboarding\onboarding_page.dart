import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/services/onboarding_navigation_service.dart';
import 'package:rideoon/views/onboarding/onboarding_container.dart';

class OnboardingPage extends StatefulWidget {
  final OnboardingPageData data;
  final OnboardingNavigationService navigationService;
  final VoidCallback onSkip;
  final VoidCallback onNext;

  const OnboardingPage({
    super.key,
    required this.data,
    required this.navigationService,
    required this.onSkip,
    required this.onNext,
  });

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onPanEnd: widget.navigationService.handleSwipe,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildContent(context),
            ),
          );
        },
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Container(
        constraints: BoxConstraints(
          minHeight: MediaQuery.of(context).size.height -
                     MediaQuery.of(context).padding.top -
                     MediaQuery.of(context).padding.bottom -
                     _getIndicatorSpacing(context), // Account for page indicators
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: _getHorizontalPadding(context),
            vertical: _getVerticalPadding(context),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Top spacer
              SizedBox(height: _getSpacing(context, 20)),

              // Illustration
              _buildIllustration(context),

              SizedBox(height: _getSpacing(context, 40)),

              // Text content
              _buildTextContent(context),

              SizedBox(height: _getSpacing(context, 60)),

              // Navigation buttons
              _buildNavigationButtons(context),

              // Bottom spacer
              SizedBox(height: _getSpacing(context, 20)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIllustration(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: _getIllustrationSize(context),
        maxHeight: _getIllustrationSize(context),
      ),
      child: Image.asset(
        widget.data.illustration,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: _getIllustrationSize(context),
            height: _getIllustrationSize(context),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              widget.data.fallbackIcon,
              color: AppColors.primary,
              size: _getIllustrationSize(context) * 0.4,
            ),
          );
        },
      ),
    );
  }

  Widget _buildTextContent(BuildContext context) {
    return Column(
      children: [
        Text(
          widget.data.title,
          style: TextStyle(
            fontSize: _getTitleFontSize(context),
            fontWeight: FontWeight.bold,
            color: AppColors.black,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: _getSpacing(context, 16)),
        Text(
          widget.data.description,
          style: TextStyle(
            fontSize: _getBodyFontSize(context),
            color: AppColors.black.withValues(alpha: 0.7),
            height: 1.5,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildNavigationButtons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Skip button
        TextButton(
          onPressed: widget.onSkip,
          child: Text(
            'Skip',
            style: TextStyle(
              fontSize: _getButtonFontSize(context),
              color: AppColors.black.withValues(alpha: 0.6),
            ),
          ),
        ),

        // Forward button
        Container(
          width: _getButtonSize(context),
          height: _getButtonSize(context),
          decoration: const BoxDecoration(
            color: AppColors.primary,
            shape: BoxShape.circle,
          ),
          child: IconButton(
            onPressed: widget.onNext,
            icon: Icon(
              Icons.arrow_forward,
              color: AppColors.white,
              size: _getButtonIconSize(context),
            ),
          ),
        ),
      ],
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 16; // Smartwatch
    if (screenWidth > 600) return 60; // Tablet
    return 24; // Mobile
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return baseSpacing * 0.6; // Smartwatch
    if (screenWidth > 600) return baseSpacing * 1.2; // Tablet
    return baseSpacing; // Mobile
  }

  double _getIllustrationSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = screenWidth * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = screenWidth * 0.3; // Tablet
    } else {
      baseSize = screenWidth * 0.7; // Mobile
    }

    // Reduce illustration size for short screens
    if (isShortScreen) {
      baseSize = baseSize * 0.6;
    }

    return baseSize;
  }

  double _getVerticalPadding(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    if (screenHeight < 600) return 8; // Short screens
    if (screenHeight > 800) return 24; // Tall screens
    return 16; // Normal screens
  }

  double _getIndicatorSpacing(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    if (screenHeight < 600) return 60; // Short screens
    return 80; // Normal screens
  }

  double _getTitleFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 18; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 32; // Tablet
    } else {
      baseSize = 24; // Mobile
    }

    // Reduce font size for short screens
    if (isShortScreen) {
      baseSize = baseSize * 0.8;
    }

    return baseSize;
  }

  double _getBodyFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 12; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 18; // Tablet
    } else {
      baseSize = 16; // Mobile
    }

    // Reduce font size for short screens
    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getButtonFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 12; // Smartwatch
    if (screenWidth > 600) return 18; // Tablet
    return 16; // Mobile
  }

  double _getButtonSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 40; // Smartwatch
    if (screenWidth > 600) return 60; // Tablet
    return 50; // Mobile
  }

  double _getButtonIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 16; // Smartwatch
    if (screenWidth > 600) return 28; // Tablet
    return 24; // Mobile
  }
}
