import 'package:rideoon/models/shipment/shipment.dart';
import 'package:rideoon/models/shipment/tracking.dart';
import 'package:rideoon/models/api_response.dart';

/// Response model for creating a shipment
class CreateShipmentResponse {
  final bool success;
  final String message;
  final String? shipmentId;
  final String? trackingNumber;
  final Shipment? shipment;

  const CreateShipmentResponse({
    required this.success,
    required this.message,
    this.shipmentId,
    this.trackingNumber,
    this.shipment,
  });

  /// Create from JSON response
  factory CreateShipmentResponse.fromJson(Map<String, dynamic> json) {
    return CreateShipmentResponse(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      shipmentId: json['shipmentId'] as String? ?? json['id'] as String?,
      trackingNumber: json['trackingNumber'] as String?,
      shipment: json['shipment'] != null 
          ? Shipment.fromJson(json['shipment'] as Map<String, dynamic>)
          : json['data'] != null
              ? Shipment.fromJson(json['data'] as Map<String, dynamic>)
              : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      if (shipmentId != null) 'shipmentId': shipmentId,
      if (trackingNumber != null) 'trackingNumber': trackingNumber,
      if (shipment != null) 'shipment': shipment!.toJson(),
    };
  }
}

/// Response model for getting shipment details
class GetShipmentResponse {
  final bool success;
  final String message;
  final Shipment? shipment;

  const GetShipmentResponse({
    required this.success,
    required this.message,
    this.shipment,
  });

  /// Create from JSON response
  factory GetShipmentResponse.fromJson(Map<String, dynamic> json) {
    return GetShipmentResponse(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      shipment: json['shipment'] != null 
          ? Shipment.fromJson(json['shipment'] as Map<String, dynamic>)
          : json['data'] != null
              ? Shipment.fromJson(json['data'] as Map<String, dynamic>)
              : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      if (shipment != null) 'shipment': shipment!.toJson(),
    };
  }
}

/// Response model for listing shipments
class ListShipmentsResponse {
  final bool success;
  final String message;
  final List<Shipment> shipments;
  final int totalCount;
  final int page;
  final int pageSize;
  final bool hasMore;

  const ListShipmentsResponse({
    required this.success,
    required this.message,
    required this.shipments,
    this.totalCount = 0,
    this.page = 1,
    this.pageSize = 20,
    this.hasMore = false,
  });

  /// Create from JSON response
  factory ListShipmentsResponse.fromJson(Map<String, dynamic> json) {
    final shipmentsData = json['shipments'] as List<dynamic>? ?? 
                         json['data'] as List<dynamic>? ?? 
                         [];
    
    return ListShipmentsResponse(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      shipments: shipmentsData
          .map((s) => Shipment.fromJson(s as Map<String, dynamic>))
          .toList(),
      totalCount: json['totalCount'] as int? ?? shipmentsData.length,
      page: json['page'] as int? ?? 1,
      pageSize: json['pageSize'] as int? ?? 20,
      hasMore: json['hasMore'] as bool? ?? false,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'shipments': shipments.map((s) => s.toJson()).toList(),
      'totalCount': totalCount,
      'page': page,
      'pageSize': pageSize,
      'hasMore': hasMore,
    };
  }
}

/// Response model for tracking a shipment
class TrackShipmentResponse {
  final bool success;
  final String message;
  final Shipment? shipment;
  final TrackingTimeline? tracking;

  const TrackShipmentResponse({
    required this.success,
    required this.message,
    this.shipment,
    this.tracking,
  });

  /// Create from JSON response
  factory TrackShipmentResponse.fromJson(Map<String, dynamic> json) {
    return TrackShipmentResponse(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      shipment: json['shipment'] != null 
          ? Shipment.fromJson(json['shipment'] as Map<String, dynamic>)
          : json['data'] != null
              ? Shipment.fromJson(json['data'] as Map<String, dynamic>)
              : null,
      tracking: json['tracking'] != null 
          ? TrackingTimeline.fromJson(json['tracking'] as Map<String, dynamic>)
          : json['trackingTimeline'] != null
              ? TrackingTimeline.fromJson(json['trackingTimeline'] as Map<String, dynamic>)
              : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      if (shipment != null) 'shipment': shipment!.toJson(),
      if (tracking != null) 'tracking': tracking!.toJson(),
    };
  }
}

/// Response model for updating shipment status
class UpdateShipmentStatusResponse {
  final bool success;
  final String message;
  final String? shipmentId;
  final String? newStatus;
  final TrackingEvent? trackingEvent;

  const UpdateShipmentStatusResponse({
    required this.success,
    required this.message,
    this.shipmentId,
    this.newStatus,
    this.trackingEvent,
  });

  /// Create from JSON response
  factory UpdateShipmentStatusResponse.fromJson(Map<String, dynamic> json) {
    return UpdateShipmentStatusResponse(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      shipmentId: json['shipmentId'] as String?,
      newStatus: json['newStatus'] as String? ?? json['status'] as String?,
      trackingEvent: json['trackingEvent'] != null 
          ? TrackingEvent.fromJson(json['trackingEvent'] as Map<String, dynamic>)
          : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      if (shipmentId != null) 'shipmentId': shipmentId,
      if (newStatus != null) 'newStatus': newStatus,
      if (trackingEvent != null) 'trackingEvent': trackingEvent!.toJson(),
    };
  }
}

/// Response model for calculating shipping cost
class CalculateShippingResponse {
  final bool success;
  final String message;
  final double? shippingCost;
  final double? vat;
  final double? insurance;
  final double? pickupCharge;
  final double? deliveryCharge;
  final double? serviceFee;
  final double? total;
  final Map<String, dynamic>? breakdown;

  const CalculateShippingResponse({
    required this.success,
    required this.message,
    this.shippingCost,
    this.vat,
    this.insurance,
    this.pickupCharge,
    this.deliveryCharge,
    this.serviceFee,
    this.total,
    this.breakdown,
  });

  /// Create from JSON response
  factory CalculateShippingResponse.fromJson(Map<String, dynamic> json) {
    return CalculateShippingResponse(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      shippingCost: (json['shippingCost'] as num?)?.toDouble(),
      vat: (json['vat'] as num?)?.toDouble(),
      insurance: (json['insurance'] as num?)?.toDouble(),
      pickupCharge: (json['pickupCharge'] as num?)?.toDouble(),
      deliveryCharge: (json['deliveryCharge'] as num?)?.toDouble(),
      serviceFee: (json['serviceFee'] as num?)?.toDouble(),
      total: (json['total'] as num?)?.toDouble(),
      breakdown: json['breakdown'] as Map<String, dynamic>?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      if (shippingCost != null) 'shippingCost': shippingCost,
      if (vat != null) 'vat': vat,
      if (insurance != null) 'insurance': insurance,
      if (pickupCharge != null) 'pickupCharge': pickupCharge,
      if (deliveryCharge != null) 'deliveryCharge': deliveryCharge,
      if (serviceFee != null) 'serviceFee': serviceFee,
      if (total != null) 'total': total,
      if (breakdown != null) 'breakdown': breakdown,
    };
  }
}

/// Generic shipment API response wrapper
typedef ShipmentApiResponse<T> = ApiResponse<T>;

/// Convenience methods for shipment API responses
extension ShipmentApiResponseExtensions on ApiResponse {
  /// Create a shipment API response
  static ApiResponse<CreateShipmentResponse> createShipment({
    required bool success,
    required String message,
    CreateShipmentResponse? data,
    int? statusCode,
  }) {
    return ApiResponse<CreateShipmentResponse>(
      success: success,
      message: message,
      data: data,
      statusCode: statusCode,
    );
  }

  /// Create a track shipment API response
  static ApiResponse<TrackShipmentResponse> trackShipment({
    required bool success,
    required String message,
    TrackShipmentResponse? data,
    int? statusCode,
  }) {
    return ApiResponse<TrackShipmentResponse>(
      success: success,
      message: message,
      data: data,
      statusCode: statusCode,
    );
  }

  /// Create a list shipments API response
  static ApiResponse<ListShipmentsResponse> listShipments({
    required bool success,
    required String message,
    ListShipmentsResponse? data,
    int? statusCode,
  }) {
    return ApiResponse<ListShipmentsResponse>(
      success: success,
      message: message,
      data: data,
      statusCode: statusCode,
    );
  }
}
