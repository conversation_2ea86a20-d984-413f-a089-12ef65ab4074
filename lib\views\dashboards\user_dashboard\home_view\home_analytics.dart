import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/services/package_data_service.dart';

/// Analytics component for the home view
///
/// Displays user statistics including packages sent, received, and total spent
class HomeAnalytics extends StatefulWidget {
  /// Callback to get the refresh function
  final Function(VoidCallback)? onRefreshCallback;

  const HomeAnalytics({
    super.key,
    this.onRefreshCallback,
  });

  @override
  State<HomeAnalytics> createState() => _HomeAnalyticsState();
}

class _HomeAnalyticsState extends State<HomeAnalytics> {
  int _packagesSent = 0;
  int _packagesReceived = 0;
  int _currentShipments = 0;
  double _totalSpent = 0.0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAnalyticsData();

    // Set refresh callback if provided
    if (widget.onRefreshCallback != null) {
      widget.onRefreshCallback!(refreshAnalytics);
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// Public method to refresh analytics data
  void refreshAnalytics() {
    _loadAnalyticsData();
  }

  /// Load analytics data from local storage
  Future<void> _loadAnalyticsData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Load current shipments (pending and in-progress)
      final currentShipmentsData = await PackageDataService.getCurrentShipments();
      final pendingShipments = currentShipmentsData.where((shipment) =>
        shipment['status'] == 'pending' || shipment['status'] == 'in_progress'
      ).toList();

      // Load completed orders
      final completedOrdersData = await PackageDataService.getCompletedOrders();
      final completedOrders = completedOrdersData.where((order) =>
        order['status'] == 'completed'
      ).toList();

      // Calculate total spent from all orders
      double totalSpent = 0.0;
      for (final order in completedOrdersData) {
        if (order['paymentData'] != null) {
          final paymentData = order['paymentData'] as Map<String, dynamic>;
          totalSpent += (paymentData['total'] ?? 0.0).toDouble();
        }
      }

      setState(() {
        _packagesSent = completedOrdersData.length; // All orders sent
        _packagesReceived = completedOrders.length; // Only completed deliveries
        _currentShipments = pendingShipments.length; // Pending and in-progress
        _totalSpent = totalSpent;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading analytics data: $e');
      setState(() {
        _packagesSent = 0;
        _packagesReceived = 0;
        _currentShipments = 0;
        _totalSpent = 0.0;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 20)),
      margin: EdgeInsets.symmetric(horizontal: _getHorizontalPadding(context)),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 16)),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: _getSpacing(context, 8),
            offset: Offset(0, _getSpacing(context, 2)),
            spreadRadius: 0,
          ),
        ],
      ),
      child: _isLoading
          ? _buildLoadingState(context)
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your Activity',
                  style: TextStyle(
                    color: AppColors.black,
                    fontSize: _getFontSize(context, 18),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w600,
                    letterSpacing: -0.5,
                  ),
                ),
                SizedBox(height: _getSpacing(context, 16)),
                Row(
                  children: [
                    Expanded(
                      child: _buildAnalyticsCard(
                        context,
                        title: 'Packages Sent',
                        value: _packagesSent.toString(),
                        icon: Icons.send_outlined,
                        color: AppColors.primary,
                      ),
                    ),
                    SizedBox(width: _getSpacing(context, 12)),
                    Expanded(
                      child: _buildAnalyticsCard(
                        context,
                        title: 'Packages Received',
                        value: _packagesReceived.toString(),
                        icon: Icons.inbox_outlined,
                        color: AppColors.success,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: _getSpacing(context, 12)),
                Row(
                  children: [
                    Expanded(
                      child: _buildAnalyticsCard(
                        context,
                        title: 'Current Shipments',
                        value: _currentShipments.toString(),
                        icon: Icons.local_shipping_outlined,
                        color: AppColors.warning,
                      ),
                    ),
                    SizedBox(width: _getSpacing(context, 12)),
                    Expanded(
                      child: _buildAnalyticsCard(
                        context,
                        title: 'Total Spent',
                        value: _formatCurrency(_totalSpent),
                        icon: Icons.account_balance_wallet_outlined,
                        color: AppColors.pending,
                      ),
                    ),
                  ],
                ),
              ],
            ),
    );
  }

  /// Build loading state widget
  Widget _buildLoadingState(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Your Activity',
          style: TextStyle(
            color: AppColors.black,
            fontSize: _getFontSize(context, 18),
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w600,
            letterSpacing: -0.5,
          ),
        ),
        SizedBox(height: _getSpacing(context, 16)),
        Row(
          children: [
            Expanded(child: _buildLoadingCard(context)),
            SizedBox(width: _getSpacing(context, 12)),
            Expanded(child: _buildLoadingCard(context)),
          ],
        ),
        SizedBox(height: _getSpacing(context, 12)),
        Row(
          children: [
            Expanded(child: _buildLoadingCard(context)),
            SizedBox(width: _getSpacing(context, 12)),
            Expanded(child: _buildLoadingCard(context)),
          ],
        ),
      ],
    );
  }

  /// Build loading card placeholder
  Widget _buildLoadingCard(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(_getSpacing(context, 16)),
      decoration: BoxDecoration(
        color: AppColors.black.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: _getIconSize(context, 24),
            height: _getIconSize(context, 24),
            decoration: BoxDecoration(
              color: AppColors.black.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          SizedBox(height: _getSpacing(context, 8)),
          Container(
            width: double.infinity,
            height: _getFontSize(context, 12),
            decoration: BoxDecoration(
              color: AppColors.black.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          SizedBox(height: _getSpacing(context, 4)),
          Container(
            width: _getSpacing(context, 40),
            height: _getFontSize(context, 18),
            decoration: BoxDecoration(
              color: AppColors.black.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }

  /// Format currency value
  String _formatCurrency(double amount) {
    if (amount == 0) return '₦0';
    if (amount >= 1000000) {
      return '₦${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return '₦${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return '₦${amount.toStringAsFixed(0)}';
    }
  }

  Widget _buildAnalyticsCard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    bool isFullWidth = false,
  }) {
    return Container(
      width: isFullWidth ? double.infinity : null,
      padding: EdgeInsets.all(_getSpacing(context, 16)),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(_getBorderRadius(context, 12)),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: isFullWidth
          ? Row(
              children: [
                Icon(
                  icon,
                  size: _getIconSize(context, 24),
                  color: color,
                ),
                SizedBox(width: _getSpacing(context, 12)),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          color: AppColors.black.withValues(alpha: 0.7),
                          fontSize: _getFontSize(context, 14),
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      SizedBox(height: _getSpacing(context, 4)),
                      Text(
                        value,
                        style: TextStyle(
                          color: AppColors.black,
                          fontSize: _getFontSize(context, 20),
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  icon,
                  size: _getIconSize(context, 24),
                  color: color,
                ),
                SizedBox(height: _getSpacing(context, 8)),
                Text(
                  title,
                  style: TextStyle(
                    color: AppColors.black.withValues(alpha: 0.7),
                    fontSize: _getFontSize(context, 12),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w400,
                  ),
                ),
                SizedBox(height: _getSpacing(context, 4)),
                Text(
                  value,
                  style: TextStyle(
                    color: AppColors.black,
                    fontSize: _getFontSize(context, 18),
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 40; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet
    } else {
      spacing = baseSpacing; // Mobile
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double fontSize;
    if (screenWidth < 300) {
      fontSize = baseFontSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      fontSize = baseFontSize * 1.2; // Tablet
    } else {
      fontSize = baseFontSize; // Mobile
    }

    if (isShortScreen) {
      fontSize = fontSize * 0.9;
    }

    return fontSize;
  }

  double _getIconSize(BuildContext context, double baseIconSize) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double iconSize;
    if (screenWidth < 300) {
      iconSize = baseIconSize * 0.8; // Smartwatch
    } else if (screenWidth > 600) {
      iconSize = baseIconSize * 1.2; // Tablet
    } else {
      iconSize = baseIconSize; // Mobile
    }

    if (isShortScreen) {
      iconSize = iconSize * 0.9;
    }

    return iconSize;
  }

  double _getBorderRadius(BuildContext context, double baseBorderRadius) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double borderRadius;
    if (screenWidth < 300) {
      borderRadius = baseBorderRadius * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      borderRadius = baseBorderRadius * 1.2; // Tablet
    } else {
      borderRadius = baseBorderRadius; // Mobile
    }

    if (isShortScreen) {
      borderRadius = borderRadius * 0.8;
    }

    return borderRadius;
  }
}
