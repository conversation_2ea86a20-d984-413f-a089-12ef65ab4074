Stack trace:
Frame         Function      Args
0007FFFFB740  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x2118E
0007FFFFB740  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x69BA
0007FFFFB740  0002100469F2 (00021028DF99, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB740  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB740  00021006A545 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFBA20  00021006B9A5 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEA2B10000 ntdll.dll
7FFEA0FB0000 KERNEL32.DLL
7FFEA0240000 KERNELBASE.dll
7FFEA2050000 USER32.dll
7FFEA06D0000 win32u.dll
7FFEA1360000 GDI32.dll
7FFE9FFD0000 gdi32full.dll
7FFEA0700000 msvcp_win.dll
7FFE9FDF0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFEA1080000 advapi32.dll
7FFEA2210000 msvcrt.dll
7FFEA13F0000 sechost.dll
7FFEA0620000 bcrypt.dll
7FFEA11F0000 RPCRT4.dll
7FFE9F310000 CRYPTBASE.DLL
7FFEA0650000 bcryptPrimitives.dll
7FFEA1320000 IMM32.DLL
