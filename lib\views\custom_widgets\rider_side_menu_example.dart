import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/rider_side_menu.dart';

/// Example usage of the RiderSideMenu widget
/// 
/// This file demonstrates different ways to use the reusable
/// side menu throughout the rider app.
class RiderSideMenuExample extends StatefulWidget {
  const RiderSideMenuExample({super.key});

  @override
  State<RiderSideMenuExample> createState() => _RiderSideMenuExampleState();
}

class _RiderSideMenuExampleState extends State<RiderSideMenuExample> {
  int _selectedMenuIndex = 0;

  // Sample user profile
  final UserProfile _userProfile = UserProfile(
    name: '<PERSON> <PERSON>',
    email: '<EMAIL>',
    avatarUrl: 'https://placehold.co/70x70',
    onProfileTap: () {
      print('Profile tapped');
    },
  );

  // Define menu items matching the original design
  List<SideMenuItem> get _menuItems => [
    SideMenuItem(
      icon: Icons.flash_on,
      label: 'Instant Delivery',
      isSelected: _selectedMenuIndex == 0,
      onTap: () => _onMenuItemTapped(0),
    ),
    SideMenuItem(
      icon: Icons.local_shipping,
      label: 'Active Deliveries',
      isSelected: _selectedMenuIndex == 1,
      onTap: () => _onMenuItemTapped(1),
    ),
    SideMenuItem(
      icon: Icons.schedule,
      label: 'Scheduled Delivery',
      isSelected: _selectedMenuIndex == 2,
      onTap: () => _onMenuItemTapped(2),
    ),
    SideMenuItem(
      icon: Icons.history,
      label: 'History',
      isSelected: _selectedMenuIndex == 3,
      onTap: () => _onMenuItemTapped(3),
    ),
    SideMenuItem(
      icon: Icons.notifications,
      label: 'Notifications',
      badgeCount: 6,
      isSelected: _selectedMenuIndex == 4,
      onTap: () => _onMenuItemTapped(4),
    ),
    SideMenuItem(
      icon: Icons.star,
      label: 'Rating',
      isSelected: _selectedMenuIndex == 5,
      onTap: () => _onMenuItemTapped(5),
    ),
    SideMenuItem(
      icon: Icons.settings,
      label: 'Settings',
      isSelected: _selectedMenuIndex == 6,
      onTap: () => _onMenuItemTapped(6),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Rider Side Menu Example'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        leading: IconButton(
          icon: const Icon(Icons.menu),
          onPressed: _showSideMenu,
        ),
      ),
      body: _buildCurrentScreen(),
    );
  }

  Widget _buildCurrentScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getScreenIcon(),
            size: 80,
            color: AppColors.primary,
          ),
          const SizedBox(height: 16),
          Text(
            _getScreenTitle(),
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.black,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _getScreenDescription(),
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 16,
              color: AppColors.black,
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: _showSideMenu,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
            ),
            child: const Text('Open Side Menu'),
          ),
        ],
      ),
    );
  }

  void _showSideMenu() {
    showDialog(
      context: context,
      barrierDismissible: true,
      barrierColor: AppColors.black.withValues(alpha: 0.5),
      builder: (BuildContext context) {
        return Align(
          alignment: Alignment.centerLeft,
          child: Material(
            color: Colors.transparent,
            child: RiderSideMenu(
              userProfile: _userProfile,
              menuItems: _menuItems,
              onClose: () => Navigator.of(context).pop(),
            ),
          ),
        );
      },
    );
  }

  void _onMenuItemTapped(int index) {
    setState(() {
      _selectedMenuIndex = index;
    });

    // Close the menu
    Navigator.of(context).pop();

    // Show a snackbar to demonstrate the callback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Selected: ${_menuItems[index].label}'),
        duration: const Duration(milliseconds: 1500),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  IconData _getScreenIcon() {
    switch (_selectedMenuIndex) {
      case 0: return Icons.flash_on;
      case 1: return Icons.local_shipping;
      case 2: return Icons.schedule;
      case 3: return Icons.history;
      case 4: return Icons.notifications;
      case 5: return Icons.star;
      case 6: return Icons.settings;
      default: return Icons.flash_on;
    }
  }

  String _getScreenTitle() {
    return _menuItems[_selectedMenuIndex].label;
  }

  String _getScreenDescription() {
    switch (_selectedMenuIndex) {
      case 0: return 'Handle instant delivery requests';
      case 1: return 'Manage your active deliveries';
      case 2: return 'View scheduled delivery tasks';
      case 3: return 'Review your delivery history';
      case 4: return 'Check your notifications';
      case 5: return 'View your ratings and reviews';
      case 6: return 'Manage your account settings';
      default: return 'Welcome to the rider dashboard';
    }
  }
}

/// Alternative example with custom styling
class CustomRiderSideMenuExample extends StatelessWidget {
  const CustomRiderSideMenuExample({super.key});

  @override
  Widget build(BuildContext context) {
    final customProfile = UserProfile(
      name: 'John Rider',
      email: '<EMAIL>',
    );

    final customMenuItems = [
      SideMenuItem(
        icon: Icons.dashboard,
        label: 'Dashboard',
        color: AppColors.success,
        onTap: () => print('Dashboard tapped'),
      ),
      SideMenuItem(
        icon: Icons.route,
        label: 'Routes',
        color: AppColors.warning,
        onTap: () => print('Routes tapped'),
      ),
      SideMenuItem(
        icon: Icons.account_balance_wallet,
        label: 'Earnings',
        color: AppColors.primary,
        badgeCount: 3,
        onTap: () => print('Earnings tapped'),
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Custom Rider Menu'),
        backgroundColor: AppColors.success,
        foregroundColor: AppColors.white,
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () => _showCustomMenu(context, customProfile, customMenuItems),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.success,
            foregroundColor: AppColors.white,
          ),
          child: const Text('Show Custom Menu'),
        ),
      ),
    );
  }

  void _showCustomMenu(BuildContext context, UserProfile profile, List<SideMenuItem> items) {
    showDialog(
      context: context,
      barrierDismissible: true,
      barrierColor: AppColors.black.withValues(alpha: 0.3),
      builder: (BuildContext context) {
        return Align(
          alignment: Alignment.centerLeft,
          child: Material(
            color: Colors.transparent,
            child: RiderSideMenu(
              userProfile: profile,
              menuItems: items,
              backgroundColor: AppColors.white,
              width: 280,
              onClose: () => Navigator.of(context).pop(),
            ),
          ),
        );
      },
    );
  }
}
