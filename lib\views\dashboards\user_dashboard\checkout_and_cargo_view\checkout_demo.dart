import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/payment_summary.dart';
import 'package:rideoon/views/custom_widgets/add_cargo.dart';
import 'package:rideoon/views/dashboards/user_dashboard/checkout_and_cargo_view/checkout_and_cargo_view.dart';

/// Demo page to showcase the CheckoutAndCargoView
///
/// This demonstrates how to use the transformed payment_summary.dart and add_cargo.dart
/// widgets within the checkout_and_cargo_view.dart following the app's design patterns.
class CheckoutDemo extends StatelessWidget {
  const CheckoutDemo({super.key});

  @override
  Widget build(BuildContext context) {
    // Sample payment data
    final paymentData = PaymentSummaryData(
      shippingCost: 2600,
      vat: 0,
      insurance: 500,
      pickupCharge: 200,
      isInsuranceFree: true,
      isPickupFree: true,
    );

    // Sample cargo items
    final cargoItems = [
      CargoItem(
        itemName: 'Gold chain',
        category: 'Electronics',
        itemType: 'Phone',
        weight: 0.5,
        quantity: 1,
        durability: ItemDurability.fragile,
        imagePaths: [],
        canEdit: true,
        canDelete: true,
      ),
      CargoItem(
        itemName: 'Laptop',
        category: 'Electronics',
        itemType: 'Computer',
        weight: 2.5,
        quantity: 1,
        durability: ItemDurability.average,
        imagePaths: [],
        canEdit: true,
        canDelete: true,
      ),
      CargoItem(
        itemName: 'Documents',
        category: 'Papers',
        itemType: 'Files',
        weight: 0.2,
        quantity: 5,
        durability: ItemDurability.durable,
        imagePaths: [],
        canEdit: false,
        canDelete: false,
      ),
    ];

    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBar(
        title: Text(
          'Checkout Demo',
          style: TextStyle(
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        elevation: 0,
      ),
      body: CheckoutAndCargoView(
        initialPaymentData: paymentData,
        initialCargoItems: cargoItems,
        onNavigateBack: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }
}

/// Example of using individual widgets separately
class IndividualWidgetsDemo extends StatelessWidget {
  const IndividualWidgetsDemo({super.key});

  @override
  Widget build(BuildContext context) {
    final paymentData = PaymentSummaryData(
      shippingCost: 3500,
      vat: 350,
      insurance: 0,
      pickupCharge: 0,
      isInsuranceFree: true,
      isPickupFree: true,
    );

    final cargoItems = [
      CargoItem(
        itemName: 'Smartphone',
        category: 'Electronics',
        itemType: 'Mobile',
        weight: 0.3,
        quantity: 1,
        durability: ItemDurability.average,
        imagePaths: [],
        canEdit: true,
        canDelete: true,
      ),
    ];

    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBar(
        title: Text(
          'Individual Widgets Demo',
          style: TextStyle(
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(height: 20),
              
              // Payment Summary Widget
              PaymentSummary(
                paymentData: paymentData,
                title: 'Order Summary',
                showShadow: true,
              ),
              
              SizedBox(height: 24),
              
              // Add Cargo Widget
              AddCargo(
                cargoItems: cargoItems,
                onSwitchDeliveryMethod: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Switch delivery method tapped'),
                      backgroundColor: AppColors.primary,
                    ),
                  );
                },
                onDuplicateOrder: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Duplicate order tapped'),
                      backgroundColor: AppColors.success,
                    ),
                  );
                },
                showShadow: true,
                showActionButtons: true,
              ),
              
              SizedBox(height: 100), // Space for bottom navigation
            ],
          ),
        ),
      ),
    );
  }
}
