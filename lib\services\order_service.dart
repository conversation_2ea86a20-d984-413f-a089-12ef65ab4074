import 'package:rideoon/services/order_api_service.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/models/order/collection.dart';

/// Service class for managing order data using API calls
///
/// This service handles storing, retrieving, and managing order information
/// through API endpoints instead of local storage.
///
/// Features:
/// - Store and retrieve completed orders via API
/// - Manage draft orders via API
/// - Get order counts from API data
/// - API-based order management
class OrderService {

  /// Get all completed orders from API
  static Future<List<Map<String, dynamic>>> getCompletedOrders() async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return [];

      final response = await OrderApiService.getOrders(
        queryParams: {'status': 'completed'},
        authToken: authToken,
      );

      if (response.success && response.data != null) {
        return response.data!.map((order) => {
          'orderId': order.uuid,
          'trackingNumber': order.uuid.substring(0, 8).toUpperCase(),
          'status': order.status,
          'timestamp': order.created.millisecondsSinceEpoch,
          'orderDate': order.created.toIso8601String(),
          'deliveryType': order.orderPickupType,
          'note': order.note,
          'amount': order.amount,
        }).toList();
      }

      return [];
    } catch (e) {
      print('Error getting completed orders: $e');
      return [];
    }
  }

  /// Save a completed order via API
  static Future<bool> saveCompletedOrder(Map<String, dynamic> orderData) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return false;

      // Convert local order data to API format
      final createRequest = CreateCollectionRequest(
        orderPickupType: orderData['deliveryType'] ?? 'instant',
        orderPickupDate: orderData['orderDate'] != null ? DateTime.parse(orderData['orderDate']) : null,
        note: orderData['note'] ?? 'Completed order',
        pickupAddressUuid: orderData['pickupAddressUuid'],
        deliveryAddressUuid: orderData['deliveryAddressUuid'],
      );

      final response = await OrderApiService.createOrder(createRequest, authToken: authToken);
      return response.success;
    } catch (e) {
      print('Error saving completed order: $e');
      return false;
    }
  }

  /// Get all draft orders from API
  static Future<List<Map<String, dynamic>>> getDraftOrders() async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return [];

      final response = await OrderApiService.getOrders(
        queryParams: {'status': 'pending,draft'},
        authToken: authToken,
      );

      if (response.success && response.data != null) {
        return response.data!.map((order) => {
          'draftId': order.uuid,
          'trackingNumber': order.uuid.substring(0, 8).toUpperCase(),
          'status': order.status,
          'timestamp': order.created.millisecondsSinceEpoch,
          'orderDate': order.created.toIso8601String(),
          'deliveryType': order.orderPickupType,
          'note': order.note,
          'amount': order.amount,
        }).toList();
      }

      return [];
    } catch (e) {
      print('Error getting draft orders: $e');
      return [];
    }
  }

  /// Save a draft order via API
  static Future<bool> saveDraftOrder(Map<String, dynamic> orderData) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return false;

      // Convert local order data to API format
      final createRequest = CreateCollectionRequest(
        orderPickupType: orderData['deliveryType'] ?? 'instant',
        orderPickupDate: orderData['orderDate'] != null ? DateTime.parse(orderData['orderDate']) : null,
        note: orderData['note'] ?? 'Draft order',
        pickupAddressUuid: orderData['pickupAddressUuid'],
        deliveryAddressUuid: orderData['deliveryAddressUuid'],
      );

      final response = await OrderApiService.createOrder(createRequest, authToken: authToken);
      return response.success;
    } catch (e) {
      print('Error saving draft order: $e');
      return false;
    }
  }

  /// Get total order count (completed + draft) from API
  static Future<int> getTotalOrderCount() async {
    try {
      final completedOrders = await getCompletedOrders();
      final draftOrders = await getDraftOrders();
      return completedOrders.length + draftOrders.length;
    } catch (e) {
      print('Error getting total order count: $e');
      return 0;
    }
  }

  /// Get completed order count only from API
  static Future<int> getCompletedOrderCount() async {
    try {
      final completedOrders = await getCompletedOrders();
      return completedOrders.length;
    } catch (e) {
      print('Error getting completed order count: $e');
      return 0;
    }
  }

  /// Get draft order count only from API
  static Future<int> getDraftOrderCount() async {
    try {
      final draftOrders = await getDraftOrders();
      return draftOrders.length;
    } catch (e) {
      print('Error getting draft order count: $e');
      return 0;
    }
  }

  /// Delete a completed order by ID - API managed
  static Future<bool> deleteCompletedOrder(String orderId) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return false;

      // Note: The API might not have a delete endpoint,
      // this would need to be implemented based on actual API schema
      // For now, we'll just return true as orders are managed by API
      print('Delete completed order: $orderId - API managed');
      return true;
    } catch (e) {
      print('Error deleting completed order: $e');
      return false;
    }
  }

  /// Delete a draft order by ID - API managed
  static Future<bool> deleteDraftOrder(String draftId) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return false;

      // Note: The API might not have a delete endpoint,
      // this would need to be implemented based on actual API schema
      // For now, we'll just return true as orders are managed by API
      print('Delete draft order: $draftId - API managed');
      return true;
    } catch (e) {
      print('Error deleting draft order: $e');
      return false;
    }
  }

  /// Clear all completed orders - API managed
  static Future<bool> clearCompletedOrders() async {
    try {
      // Orders are managed by API, no local clearing needed
      print('Clear completed orders: API managed, no action needed');
      return true;
    } catch (e) {
      print('Error clearing completed orders: $e');
      return false;
    }
  }

  /// Clear all draft orders - API managed
  static Future<bool> clearDraftOrders() async {
    try {
      // Orders are managed by API, no local clearing needed
      print('Clear draft orders: API managed, no action needed');
      return true;
    } catch (e) {
      print('Error clearing draft orders: $e');
      return false;
    }
  }

  /// Clear all order data - API managed
  static Future<bool> clearAllOrders() async {
    try {
      // Orders are managed by API, no local clearing needed
      print('Clear all orders: API managed, no action needed');
      return true;
    } catch (e) {
      print('Error clearing all orders: $e');
      return false;
    }
  }

  /// Get order by ID from completed orders via API
  static Future<Map<String, dynamic>?> getCompletedOrderById(String orderId) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return null;

      final response = await OrderApiService.getOrder(orderId, authToken: authToken);
      if (response.success && response.data != null) {
        final order = response.data!;
        return {
          'orderId': order.uuid,
          'trackingNumber': order.uuid.substring(0, 8).toUpperCase(),
          'status': order.status,
          'timestamp': order.created.millisecondsSinceEpoch,
          'orderDate': order.created.toIso8601String(),
          'deliveryType': order.orderPickupType,
          'note': order.note,
          'amount': order.amount,
        };
      }
      return null;
    } catch (e) {
      print('Error getting order by ID: $e');
      return null;
    }
  }

  /// Get draft by ID from draft orders via API
  static Future<Map<String, dynamic>?> getDraftOrderById(String draftId) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return null;

      final response = await OrderApiService.getOrder(draftId, authToken: authToken);
      if (response.success && response.data != null) {
        final order = response.data!;
        return {
          'draftId': order.uuid,
          'trackingNumber': order.uuid.substring(0, 8).toUpperCase(),
          'status': order.status,
          'timestamp': order.created.millisecondsSinceEpoch,
          'orderDate': order.created.toIso8601String(),
          'deliveryType': order.orderPickupType,
          'note': order.note,
          'amount': order.amount,
        };
      }
      return null;
    } catch (e) {
      print('Error getting draft by ID: $e');
      return null;
    }
  }

  /// Convert order to tracking shipment format
  static Map<String, dynamic> orderToShipment(Map<String, dynamic> orderData) {
    final pickupData = orderData['pickupData'] as Map<String, dynamic>? ?? {};
    final receiverData = orderData['receiverData'] as Map<String, dynamic>? ?? {};
    final paymentData = orderData['paymentData'] as Map<String, dynamic>? ?? {};
    
    return {
      'trackingNumber': orderData['trackingNumber'] ?? orderData['orderId'] ?? 'N/A',
      'status': orderData['status'] ?? 'pending',
      'senderName': pickupData['senderName'] ?? 'N/A',
      'receiverName': receiverData['name'] ?? 'N/A',
      'pickupLocation': pickupData['fullAddress'] ?? 'N/A',
      'deliveryLocation': receiverData['address'] ?? 'N/A',
      'estimatedDelivery': _calculateEstimatedDelivery(orderData),
      'cost': (paymentData['total'] as num?)?.toDouble() ?? 0.0,
      'orderDate': orderData['orderDate'] ?? DateTime.now().toIso8601String(),
      'isExpanded': false,
    };
  }

  /// Calculate estimated delivery time
  static String _calculateEstimatedDelivery(Map<String, dynamic> orderData) {
    try {
      final orderDateString = orderData['orderDate'] as String?;
      if (orderDateString != null) {
        final orderDate = DateTime.parse(orderDateString);
        final estimatedDelivery = orderDate.add(Duration(days: 2)); // 2 days delivery
        return estimatedDelivery.toIso8601String();
      }
    } catch (e) {
      print('Error calculating estimated delivery: $e');
    }

    // Default to 2 days from now
    return DateTime.now().add(Duration(days: 2)).toIso8601String();
  }

  // ============================================================================
  // NEW ORDER CREATION AND MANAGEMENT METHODS
  // ============================================================================

  /// Create a new order using the new API endpoints
  ///
  /// [orderData] - The order data including pickup/delivery addresses and packages
  /// Returns the created order data with UUID
  static Future<Map<String, dynamic>?> createNewOrder(
    Map<String, dynamic> orderData
  ) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return null;

      final response = await OrderApiService.createNewOrder(
        orderData,
        authToken: authToken,
      );

      if (response.success && response.data != null) {
        return response.data;
      }
      return null;
    } catch (e) {
      print('Error creating new order: $e');
      return null;
    }
  }

  /// Create or update an order with specific UUID
  ///
  /// [orderUuid] - The order UUID (use 'new' for new orders)
  /// [orderData] - The order data
  /// Returns the order data
  static Future<Map<String, dynamic>?> createOrUpdateOrderWithUuid(
    String orderUuid,
    Map<String, dynamic> orderData
  ) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return null;

      final response = await OrderApiService.createOrUpdateOrder(
        orderUuid,
        orderData,
        authToken: authToken,
      );

      if (response.success && response.data != null) {
        return response.data;
      }
      return null;
    } catch (e) {
      print('Error creating/updating order: $e');
      return null;
    }
  }

  /// Get order details by UUID using new API
  ///
  /// [orderUuid] - The order UUID
  /// Returns the order data with full details
  static Future<Map<String, dynamic>?> getOrderDetails(String orderUuid) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return null;

      final response = await OrderApiService.getOrderDetails(
        orderUuid,
        authToken: authToken,
      );

      if (response.success && response.data != null) {
        return response.data;
      }
      return null;
    } catch (e) {
      print('Error getting order details: $e');
      return null;
    }
  }

  /// Update packages on an order
  ///
  /// [orderUuid] - The order UUID
  /// [packages] - List of package data
  /// Returns the updated packages
  static Future<List<Map<String, dynamic>>?> updateOrderPackages(
    String orderUuid,
    List<Map<String, dynamic>> packages
  ) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return null;

      final response = await OrderApiService.updateOrderPackages(
        orderUuid,
        packages,
        authToken: authToken,
      );

      if (response.success && response.data != null) {
        return response.data;
      }
      return null;
    } catch (e) {
      print('Error updating order packages: $e');
      return null;
    }
  }

  /// Update a single package on an order
  ///
  /// [orderUuid] - The order UUID
  /// [packageUuid] - The package UUID (use 'new' for new packages)
  /// [packageData] - The package data
  /// Returns the updated package
  static Future<Map<String, dynamic>?> updateOrderPackage(
    String orderUuid,
    String packageUuid,
    Map<String, dynamic> packageData
  ) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return null;

      final response = await OrderApiService.updateOrderPackage(
        orderUuid,
        packageUuid,
        packageData,
        authToken: authToken,
      );

      if (response.success && response.data != null) {
        return response.data;
      }
      return null;
    } catch (e) {
      print('Error updating order package: $e');
      return null;
    }
  }

  /// Delete packages from an order
  ///
  /// [orderUuid] - The order UUID
  /// [packageUuids] - Comma-separated package UUIDs
  /// Returns success status
  static Future<bool> deleteOrderPackages(
    String orderUuid,
    String packageUuids
  ) async {
    try {
      final authToken = await AuthService.getAuthToken();
      if (authToken == null) return false;

      final response = await OrderApiService.deleteOrderPackages(
        orderUuid,
        packageUuids,
        authToken: authToken,
      );

      return response.success;
    } catch (e) {
      print('Error deleting order packages: $e');
      return false;
    }
  }
}
